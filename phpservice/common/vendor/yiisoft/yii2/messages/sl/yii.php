<?php
/**
 * Message translations.
 *
 * This file is automatically generated by 'yii message' command.
 * It contains the localizable messages extracted from source code.
 * You may modify this file by translating the extracted messages.
 *
 * Each array element represents the translation (value) of a message (key).
 * If the value is empty, the message is considered as not translated.
 * Messages that no longer need translation will have their translations
 * enclosed between a pair of '@@' marks.
 *
 * Message string can be used with plural forms format. Check i18n section
 * of the guide for details.
 *
 * NOTE: this file must be saved in UTF-8 encoding.
 */
return [
    'just now' => 'ravno zdaj',
    '(not set)' => '(ni nastavljeno)',
    'An internal server error occurred.' => '<PERSON><PERSON><PERSON><PERSON> je do notranje napake na strežniku.',
    'Are you sure you want to delete this item?' => 'Ste prepričani, da želite izbrisati ta element?',
    'Delete' => 'Izbrišite',
    'Error' => 'Napaka',
    'File upload failed.' => 'Nalaganje datoteke ni bilo uspe<PERSON>no.',
    'Home' => 'Domov',
    'Invalid data received for parameter "{param}".' => 'Neveljavni podatki so bili poslani za parameter "{param}".',
    'Login Required' => 'Zahtevana je prijava',
    'Missing required arguments: {params}' => 'Manjkajo zahtevani argumenti: {params}',
    'Missing required parameters: {params}' => 'Manjkajo zahtevani parametri: {params}',
    'No' => 'Ne',
    'No help for unknown command "{command}".' => 'Pomoči za neznani ukaz "{command}" ni mogoče najti.',
    'No help for unknown sub-command "{command}".' => 'Pomoči za neznani pod-ukaz "{command}" ni mogoče najti.',
    'No results found.' => 'Razultatov ni bilo mogoče najti.',
    'Only files with these MIME types are allowed: {mimeTypes}.' => 'Dovoljene so samo datoteke s temi MIME tipi: {mimeTypes}.',
    'Only files with these extensions are allowed: {extensions}.' => 'Dovoljene so samo datoteke s temi končnicami: {extensions}.',
    'Page not found.' => 'Strani ni mogoče najti.',
    'Please fix the following errors:' => 'Prosimo, popravite sledeče napake:',
    'Please upload a file.' => 'Prosimo, naložite datoteko.',
    'Showing <b>{begin, number}-{end, number}</b> of <b>{totalCount, number}</b> {totalCount, plural, one{item} other{items}}.' => 'Prikaz <b>{begin, number}-{end, number}</b> od <b>{totalCount, number}</b> {totalCount, plural, one{Element} two{Elementa} few{Elementi} other{Elementov}}.',
    'The file "{file}" is not an image.' => 'Datoteka "{file}" ni slika.',
    'The file "{file}" is too big. Its size cannot exceed {formattedLimit}.' => 'Datoteka "{file}" je prevelika. Njena velikost {formattedLimit}.',
    'The file "{file}" is too small. Its size cannot be smaller than {formattedLimit}.' => 'Datoteka "{file}" je premajhna. Njena velikost ne sme biti manjša od {formattedLimit}.',
    'The format of {attribute} is invalid.' => 'Format {attribute} ni veljaven.',
    'The image "{file}" is too large. The height cannot be larger than {limit, number} {limit, plural, one{pixel} other{pixels}}.' => 'Slika "{file}" je prevelika. Višina ne sme biti večja od {limit, number} {limit, plural, one{piksla} other{pikslov}}.',
    'The image "{file}" is too large. The width cannot be larger than {limit, number} {limit, plural, one{pixel} other{pixels}}.' => 'Slika "{file}" je prevelika. Širina ne sme biti večja od {limit, number} {limit, plural, one{piksla} other{pikslov}}.',
    'The image "{file}" is too small. The height cannot be smaller than {limit, number} {limit, plural, one{pixel} other{pixels}}.' => 'Slika "{file}" je premajhna. Višina ne sme biti manjša od {limit, number} {limit, plural, one{piksla} other{pikslov}}.',
    'The image "{file}" is too small. The width cannot be smaller than {limit, number} {limit, plural, one{pixel} other{pixels}}.' => 'Slika "{file}" je premajhna. Širina ne sme biti manjša od {limit, number} {limit, plural, one{piksla} other{pikslov}}.',
    'The requested view "{name}" was not found.' => 'Zahtevani pogled "{name}" ni bil najden.',
    'The verification code is incorrect.' => 'Koda za preverjanje je napačna.',
    'Total <b>{count, number}</b> {count, plural, one{item} other{items}}.' => 'Skupaj <b>{count, number}</b> {count, plural, one{element} two{elementa} few{elementi} other{elementov}}.',
    'Unable to verify your data submission.' => 'Preverjanje vaših poslanih podatkov ni uspelo.',
    'Unknown command "{command}".' => 'Neznani ukaz "{command}".',
    'Unknown option: --{name}' => 'Neznana opcija: --{name}',
    'Update' => 'Posodobitev',
    'View' => 'Pogled',
    'Yes' => 'Da',
    'You are not allowed to perform this action.' => 'Ta akcija ni dovoljena za izvajanje.',
    'You can upload at most {limit, number} {limit, plural, one{file} other{files}}.' => 'Naložite lahko največ {limit, number} {limit, plural, one{datoteko} two{datoteki} few{datoteke} other{datotek}}.',
    'in {delta, plural, =1{a day} other{# days}}' => 'v {delta, plural, one{# dnevu} other{# dneh}}',
    'in {delta, plural, =1{a minute} other{# minutes}}' => 'v {delta, plural, one{# minuti} other{# minutah}}',
    'in {delta, plural, =1{a month} other{# months}}' => 'v {delta, plural, one{# mesecu} other{# mesecih}}',
    'in {delta, plural, =1{a second} other{# seconds}}' => 'v {delta, plural, one{# sekundi} other{# sekundah}}',
    'in {delta, plural, =1{a year} other{# years}}' => 'v {delta, plural, one{# letu} other{# letih}}',
    'in {delta, plural, =1{an hour} other{# hours}}' => 'v {delta, plural, one{# uri} other{# urah}}',
    'the input value' => 'vhodna vrednost',
    '{attribute} "{value}" has already been taken.' => 'Atribut {attribute} "{value}" je že nastavljen.',
    '{attribute} cannot be blank.' => 'Atribut {attribute} ne more biti prazen',
    '{attribute} is invalid.' => 'Atribut {attribute} je neveljaven.',
    '{attribute} is not a valid URL.' => 'Atribut {attribute} ni veljaven URL.',
    '{attribute} is not a valid email address.' => 'Atribut {attribute} ni veljaven e-poštni naslov.',
    '{attribute} must be "{requiredValue}".' => '{attribute} mora biti "{requiredValue}".',
    '{attribute} must be a number.' => '{attribute} mora biti število.',
    '{attribute} must be a string.' => '{attribute} mora biti niz.',
    '{attribute} must be an integer.' => '{attribute} mora biti celo število.',
    '{attribute} must be either "{true}" or "{false}".' => '{attribute} mora biti ali "{true}" ali "{false}".',
    '{attribute} must be greater than "{compareValue}".' => 'Atribut {attribute} mora biti večji od "{compareValue}".',
    '{attribute} must be greater than or equal to "{compareValue}".' => 'Atribut {attribute} mora biti večji ali enak "{compareValue}".',
    '{attribute} must be less than "{compareValue}".' => 'Atribut {attribute} mora biti manjši od "{compareValue}".',
    '{attribute} must be less than or equal to "{compareValue}".' => 'Atribut {attribute} mora biti manjši ali enak "{compareValue}".',
    '{attribute} must be no greater than {max}.' => 'Atribut {attribute} ne sme biti večji od {max}',
    '{attribute} must be no less than {min}.' => 'Atribut {attribute} ne sme biti manjši od {min}.',
    '{attribute} must be repeated exactly.' => 'Atribut {attribute} mora biti točno ponovljen.',
    '{attribute} must not be equal to "{compareValue}".' => 'Atribut {attribute} ne sme biti enak "{compareValue}".',
    '{attribute} should contain at least {min, number} {min, plural, one{character} other{characters}}.' => 'Atribut {attribute} mora vsebovati vsaj {min, number} {min, plural, one{znak} two{znaka} few{znake} other{znakov}}.',
    '{attribute} should contain at most {max, number} {max, plural, one{character} other{characters}}.' => 'Atribut {attribute} mora vsebovati največ {max, number} {max, plural, one{znak} two{znaka} few{znake} other{znakov}}.',
    '{attribute} should contain {length, number} {length, plural, one{character} other{characters}}.' => 'Atribut {attribute} mora vsebovati {length, number} {length, plural, one{znak} two{znaka} few{znake} other{znakov}}.',
    '{delta, plural, =1{a day} other{# days}} ago' => 'pred {delta, plural, one{# dnevom} two{# dnevoma} other{# dnevi}}',
    '{delta, plural, =1{a minute} other{# minutes}} ago' => 'pred {delta, plural, one{# minuto} two{# minutama} other{# minutami}}',
    '{delta, plural, =1{a month} other{# months}} ago' => 'pred {delta, plural, one{# mesecem} two{# mesecema} other{# meseci}}',
    '{delta, plural, =1{a second} other{# seconds}} ago' => 'pred {delta, plural, one{# sekundo} two{# sekundama} other{# sekundami}}',
    '{delta, plural, =1{a year} other{# years}} ago' => 'pred {delta, plural, one{# letom} two{# letoma} other{# leti}}',
    '{delta, plural, =1{an hour} other{# hours}} ago' => 'pred {delta, plural, one{#uro} two{# urama} other{# urami}}',
    '{nFormatted} B' => '{nFormatted} B',
    '{nFormatted} GB' => '{nFormatted} GB',
    '{nFormatted} GiB' => '{nFormatted} GiB',
    '{nFormatted} KB' => '{nFormatted} KB',
    '{nFormatted} KiB' => '{nFormatted} KiB',
    '{nFormatted} MB' => '{nFormatted} MB',
    '{nFormatted} MiB' => '{nFormatted} MiB',
    '{nFormatted} PB' => '{nFormatted} PB',
    '{nFormatted} PiB' => '{nFormatted} PiB',
    '{nFormatted} TB' => '{nFormatted} TB',
    '{nFormatted} TiB' => '{nFormatted} TiB',
    '{nFormatted} {n, plural, =1{byte} other{bytes}}' => '{nFormatted} {n, plural, one{bajt} two{bajta} few{bajti} other{bajtov}}',
    '{nFormatted} {n, plural, =1{gibibyte} other{gibibytes}}' => '{nFormatted} {n, plural, one{gibibajt} two{gibibajta} few{gibibajti} other{gibibajtov}}',
    '{nFormatted} {n, plural, =1{gigabyte} other{gigabytes}}' => '{nFormatted} {n, plural, one{gigabajt} two{gigabajta} few{gigabajti} other{gigabajtov}}',
    '{nFormatted} {n, plural, =1{kibibyte} other{kibibytes}}' => '{nFormatted} {n, plural, one{kibibajt} two{kibibajta} few{kibibajti} other{kibibajtov}}',
    '{nFormatted} {n, plural, =1{kilobyte} other{kilobytes}}' => '{nFormatted} {n, plural, one{kilobajt} two{kilobajta} few{kilobajti} other{kilobajtov}}',
    '{nFormatted} {n, plural, =1{mebibyte} other{mebibytes}}' => '{nFormatted} {n, plural, one{mebibajt} two{mebibajta} few{mebibajti} other{mebibajtov}}',
    '{nFormatted} {n, plural, =1{megabyte} other{megabytes}}' => '{nFormatted} {n, plural, one{megabajt} two{megabajta} few{megabajti} other{megabajtov}}',
    '{nFormatted} {n, plural, =1{pebibyte} other{pebibytes}}' => '{nFormatted} {n, plural, one{pebibajt} two{pebibajta} few{pebibajti} other{pebibajtov}}',
    '{nFormatted} {n, plural, =1{petabyte} other{petabytes}}' => '{nFormatted} {n, plural, one{petabajt} two{petabajta} few{petabajti} other{petabajtov}}',
    '{nFormatted} {n, plural, =1{tebibyte} other{tebibytes}}' => '{nFormatted} {n, plural, one{tebibajt} two{tebibajta} few{tebibajti} other{tebibajtov}}',
    '{nFormatted} {n, plural, =1{terabyte} other{terabytes}}' => '{nFormatted} {n, plural, one{terabajt} two{terabajta} few{terabajti} other{terabajtov}}',
];
