<?php
/**
 * Message translations.
 *
 * This file is automatically generated by 'yii message' command.
 * It contains the localizable messages extracted from source code.
 * You may modify this file by translating the extracted messages.
 *
 * Each array element represents the translation (value) of a message (key).
 * If the value is empty, the message is considered as not translated.
 * Messages that no longer need translation will have their translations
 * enclosed between a pair of '@@' marks.
 *
 * Message string can be used with plural forms format. Check i18n section
 * of the guide for details.
 *
 * NOTE: this file must be saved in UTF-8 encoding.
 */
return [
    'The requested view "{name}" was not found.' => 'View "{name}" yang diminta tidak ditemukan.',
    'You are requesting with an invalid access token.' => 'Anda melakukan permintaan dengan akses token yang tidak valid.',
    '(not set)' => '(belum diset)',
    'An internal server error occurred.' => 'Terjadi kesalahan internal server.',
    'Are you sure you want to delete this item?' => 'Apakah Anda yakin ingin menghapus item ini?',
    'Delete' => 'Hapus',
    'Error' => 'Kesalahan',
    'File upload failed.' => 'Mengunggah berkas gagal.',
    'Home' => 'Beranda',
    'Invalid data received for parameter "{param}".' => 'Data yang diterima tidak valid untuk parameter "{param}"',
    'Login Required' => 'Diperlukan login',
    'Missing required arguments: {params}' => 'Argumen yang diperlukan tidak ada: {params}',
    'Missing required parameters: {params}' => 'Parameter yang diperlukan tidak ada: {params}',
    'No' => 'Tidak',
    'No help for unknown command "{command}".' => 'Tidak ada bantuan untuk perintah yang tidak diketahui "{command}".',
    'No help for unknown sub-command "{command}".' => 'Tidak ada bantuan untuk sub perintah yang tidak diketahui "{command}".',
    'No results found.' => 'Tidak ada data yang ditemukan.',
    'Only files with these MIME types are allowed: {mimeTypes}.' => 'Hanya berkas dengan tipe MIME ini yang diperbolehkan: {mimeTypes}.',
    'Only files with these extensions are allowed: {extensions}.' => 'Hanya berkas dengan ekstensi ini yang diperbolehkan: {extensions}.',
    'Page not found.' => 'Halaman tidak ditemukan.',
    'Please fix the following errors:' => 'Silahkan perbaiki kesalahan berikut:',
    'Please upload a file.' => 'Silahkan mengunggah berkas.',
    'Showing <b>{begin, number}-{end, number}</b> of <b>{totalCount, number}</b> {totalCount, plural, one{item} other{items}}.' => 'Menampilkan <b>{begin, number}-{end, number}</b> dari <b>{totalCount, number}</b> {totalCount, plural, one{item} other{item}}.',
    'The file "{file}" is not an image.' => 'File bukan berupa gambar.',
    'The file "{file}" is too big. Its size cannot exceed {formattedLimit}.' => 'Berkas "{file}" terlalu besar. Ukurannya tidak boleh lebih besar dari {formattedLimit}.',
    'The file "{file}" is too small. Its size cannot be smaller than {formattedLimit}.' => 'Berkas "{file}" terlalu kecil. Ukurannya tidak boleh lebih kecil dari {formattedLimit}.',
    'The format of {attribute} is invalid.' => 'Format dari {attribute} tidak valid.',
    'The image "{file}" is too large. The height cannot be larger than {limit, number} {limit, plural, one{pixel} other{pixels}}.' => 'Gambar "{file}" terlalu besar. Tingginya tidak boleh lebih besar dari {limit, number} {limit, plural, one{piksel} other{piksel}}.',
    'The image "{file}" is too large. The width cannot be larger than {limit, number} {limit, plural, one{pixel} other{pixels}}.' => 'Gambar "{file}" terlalu besar. Lebarnya tidak boleh lebih besar dari  {limit, number} {limit, plural, one{piksel} other{piksel}}.',
    'The image "{file}" is too small. The height cannot be smaller than {limit, number} {limit, plural, one{pixel} other{pixels}}.' => 'Gambar  "{file}" terlalu kecil. Tingginya tidak boleh lebih kecil dari {limit, number} {limit, plural, one{piksel} other{piksel}}.',
    'The image "{file}" is too small. The width cannot be smaller than {limit, number} {limit, plural, one{pixel} other{pixels}}.' => 'Gambar "{file}" terlalu kecil. Lebarnya tidak boleh lebih kecil dari {limit, number} {limit, plural, one{piksel} other{piksel}}.',
    'The verification code is incorrect.' => 'Kode verifikasi tidak benar.',
    'Total <b>{count, number}</b> {count, plural, one{item} other{items}}.' => 'Total <b>{count, number}</b> {count, plural, one{item} other{item}}.',
    'Unable to verify your data submission.' => 'Tidak dapat mem-verifikasi pengiriman data Anda.',
    'Unknown command "{command}".' => 'Perintah tidak dikenal "{command}".',
    'Unknown option: --{name}' => 'Opsi tidak dikenal: --{name}',
    'Update' => 'Ubah',
    'View' => 'Lihat',
    'Yes' => 'Ya',
    'You are not allowed to perform this action.' => 'Anda tidak diperbolehkan untuk melakukan aksi ini.',
    'You can upload at most {limit, number} {limit, plural, one{file} other{files}}.' => 'Anda dapat mengunggah paling banyak {limit, number} {limit, plural, one{file} other{file}}.',
    'in {delta, plural, =1{a day} other{# days}}' => 'dalam {delta, plural, =1{satu hari} other{# hari}}',
    'in {delta, plural, =1{a minute} other{# minutes}}' => 'dalam {delta, plural, =1{satu menit} other{# menit}}',
    'in {delta, plural, =1{a month} other{# months}}' => 'dalam {delta, plural, =1{satu bulan} other{# bulan}}',
    'in {delta, plural, =1{a second} other{# seconds}}' => 'dalam {delta, plural, =1{satu detik} other{# detik}}',
    'in {delta, plural, =1{a year} other{# years}}' => 'dalam {delta, plural, =1{satu tahun} other{# tahun}}',
    'in {delta, plural, =1{an hour} other{# hours}}' => 'dalam {delta, plural, =1{satu jam} other{# jam}}',
    'the input value' => 'nilai input',
    '{attribute} "{value}" has already been taken.' => '{attribute} "{value}" telah dipergunakan.',
    '{attribute} cannot be blank.' => '{attribute} tidak boleh kosong.',
    '{attribute} is invalid.' => '{attribute} tidak valid.',
    '{attribute} is not a valid URL.' => '{attribute} bukan URL yang valid.',
    '{attribute} is not a valid email address.' => '{attribute} bukan alamat email yang valid.',
    '{attribute} must be "{requiredValue}".' => '{attribute} harus berupa "{requiredValue}".',
    '{attribute} must be a number.' => '{attribute} harus berupa angka.',
    '{attribute} must be a string.' => '{attribute} harus berupa string.',
    '{attribute} must be an integer.' => '{attribute} harus berupa integer.',
    '{attribute} must be either "{true}" or "{false}".' => '{attribute} harus berupa "{true}" atau "{false}".',
    '{attribute} must be greater than "{compareValue}".' => '{attribute} harus lebih besar dari "{compareValue}".',
    '{attribute} must be greater than or equal to "{compareValue}".' => '{attribute} harus lebih besar dari atau sama dengan "{compareValue}".',
    '{attribute} must be less than "{compareValue}".' => '{attribute} harus lebih kecil dari "{compareValue}".',
    '{attribute} must be less than or equal to "{compareValue}".' => '{attribute} harus lebih kecil dari atau sama dengan "{compareValue}".',
    '{attribute} must be no greater than {max}.' => '{attribute} harus tidak boleh lebih besar dari {max}.',
    '{attribute} must be no less than {min}.' => '{attribute} harus tidak boleh lebih kecil dari {min}.',
    '{attribute} must be repeated exactly.' => '{attribute} harus diulang sama persis.',
    '{attribute} must not be equal to "{compareValue}".' => '{attribute} harus tidak boleh sama dengan "{compareValue}".',
    '{attribute} should contain at least {min, number} {min, plural, one{character} other{characters}}.' => '{attribute} harus memiliki paling sedikit {min, number} {min, plural, one{karakter} other{karakter}}.',
    '{attribute} should contain at most {max, number} {max, plural, one{character} other{characters}}.' => '{attribute} harus memiliki paling banyak {max, number} {max, plural, one{karakter} other{karakter}}.',
    '{attribute} should contain {length, number} {length, plural, one{character} other{characters}}.' => '{attribute} harus memiliki {length, number} {length, plural, one{karakter} other{karakter}}.',
    '{delta, plural, =1{a day} other{# days}} ago' => '{delta, plural, =1{satu hari} other{# hari}} yang lalu',
    '{delta, plural, =1{a minute} other{# minutes}} ago' => '{delta, plural, =1{satu menit} other{# menit}} yang lalu',
    '{delta, plural, =1{a month} other{# months}} ago' => '{delta, plural, =1{satu bulan} other{# bulan}} yang lalu',
    '{delta, plural, =1{a second} other{# seconds}} ago' => '{delta, plural, =1{satu detik} other{# detik}} yang lalu',
    '{delta, plural, =1{a year} other{# years}} ago' => '{delta, plural, =1{satu tahun} other{# tahun}} yang lalu',
    '{delta, plural, =1{an hour} other{# hours}} ago' => '{delta, plural, =1{satu jam} other{# jam}} yang lalu',
    '{n, plural, =1{# byte} other{# bytes}}' => '{n, plural, =1{# bita} other{# bita}}',
    '{n, plural, =1{# gigabyte} other{# gigabytes}}' => '{n, plural, =1{# gigabita} other{# gigabita}}',
    '{n, plural, =1{# kilobyte} other{# kilobytes}}' => '{n, plural, =1{# kilobita} other{# kilobita}}',
    '{n, plural, =1{# megabyte} other{# megabytes}}' => '{n, plural, =1{# megabita} other{# megabita}}',
    '{n, plural, =1{# petabyte} other{# petabytes}}' => '{n, plural, =1{# petabita} other{# petabita}}',
    '{n, plural, =1{# terabyte} other{# terabytes}}' => '{n, plural, =1{# petabita} other{# petabita}}',
    '{n} B' => '{n} B',
    '{n} GB' => '{n} GB',
    '{n} KB' => '{n} KB',
    '{n} MB' => '{n} MB',
    '{n} PB' => '{n} PB',
    '{n} TB' => '{n} TB',
];
