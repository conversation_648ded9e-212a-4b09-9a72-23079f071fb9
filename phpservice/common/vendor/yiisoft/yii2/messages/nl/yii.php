<?php
/**
 * Message translations.
 *
 * This file is automatically generated by 'yii message' command.
 * It contains the localizable messages extracted from source code.
 * You may modify this file by translating the extracted messages.
 *
 * Each array element represents the translation (value) of a message (key).
 * If the value is empty, the message is considered as not translated.
 * Messages that no longer need translation will have their translations
 * enclosed between a pair of '@@' marks.
 *
 * Message string can be used with plural forms format. Check i18n section
 * of the guide for details.
 *
 * NOTE: this file must be saved in UTF-8 encoding.
 */
return [
    '(not set)' => '(niet ingesteld)',
    'An internal server error occurred.' => 'Er is een interne serverfout opgetreden.',
    'Are you sure you want to delete this item?' => 'Weet je zeker dat je dit item wilt verwijderen?',
    'Delete' => 'Verwijderen',
    'Error' => 'Fout',
    'File upload failed.' => 'Bestand uploaden mislukt.',
    'Home' => 'Home',
    'Invalid data received for parameter "{param}".' => 'Ongeldige gegevens ontvangen voor parameter "{param}".',
    'Login Required' => 'Inloggen verplicht',
    'Missing required arguments: {params}' => 'Ontbrekende vereiste argumenten: {params}',
    'Missing required parameters: {params}' => 'Ontbrekende vereiste parameters: {params}',
    'No' => 'Nee',
    'No results found.' => 'Geen resultaten gevonden',
    'Only files with these MIME types are allowed: {mimeTypes}.' => 'Alleen bestanden met de volgende MIME types zijn toegestaan: {mimeTypes}',
    'Only files with these extensions are allowed: {extensions}.' => 'Alleen bestanden met de volgende extensies zijn toegestaan: {extensions}.',
    'Page not found.' => 'Pagina niet gevonden.',
    'Please fix the following errors:' => 'Corrigeer de volgende fouten:',
    'Please upload a file.' => 'Upload een bestand.',
    'Showing <b>{begin, number}-{end, number}</b> of <b>{totalCount, number}</b> {totalCount, plural, one{item} other{items}}.' => 'Resultaat <b>{begin, number}-{end, number}</b> van <b>{totalCount, number}</b> {totalCount, plural, one{item} other{items}}.',
    'The file "{file}" is not an image.' => 'Het bestand "{file}" is geen afbeelding.',
    'The file "{file}" is too big. Its size cannot exceed {formattedLimit}.' => 'Het bestand "{file}" is te groot. Het kan niet groter zijn dan {formattedLimit}.',
    'The file "{file}" is too small. Its size cannot be smaller than {formattedLimit}.' => 'Het bestand "{file}" is te klein. Het kan niet kleiner zijn dan {formattedLimit}.',
    'The format of {attribute} is invalid.' => 'Het formaat van {attribute} is ongeldig',
    'The image "{file}" is too large. The height cannot be larger than {limit, number} {limit, plural, one{pixel} other{pixels}}.' => 'De afbeelding "{file}" is te groot. Het mag maximaal {limit, number} {limit, plural, one{pixel} other{pixels}} hoog zijn.',
    'The image "{file}" is too large. The width cannot be larger than {limit, number} {limit, plural, one{pixel} other{pixels}}.' => 'De afbeelding "{file}" is te groot. Het mag maximaal {limit, number} {limit, plural, one{pixel} other{pixels}} breed zijn.',
    'The image "{file}" is too small. The height cannot be smaller than {limit, number} {limit, plural, one{pixel} other{pixels}}.' => 'De afbeelding "{file}" is te klein. Het moet minimaal {limit, number} {limit, plural, one{pixel} other{pixels}} hoog zijn.',
    'The image "{file}" is too small. The width cannot be smaller than {limit, number} {limit, plural, one{pixel} other{pixels}}.' => 'De afbeelding "{file}" is te klein. Het moet minimaal {limit, number} {limit, plural, one{pixel} other{pixels}} breed zijn.',
    'The requested view "{name}" was not found.' => 'De gevraagde view "{view}" werd niet gevonden.',
    'The verification code is incorrect.' => 'De verificatiecode is onjuist.',
    'Total <b>{count, number}</b> {count, plural, one{item} other{items}}.' => 'Totaal <b>{count, number}</b> {count, plural, one{item} other{items}}.',
    'Unable to verify your data submission.' => 'Het is niet mogelijk uw verstrekte gegevens te verifiëren.',
    'Unknown option: --{name}' => 'Onbekende optie: --{name}',
    'Update' => 'Bewerk',
    'View' => 'Bekijk',
    'Yes' => 'Ja',
    'You are not allowed to perform this action.' => 'U bent niet gemachtigd om deze actie uit te voeren.',
    'You can upload at most {limit, number} {limit, plural, one{file} other{files}}.' => 'U kunt maximaal {limit, number} {limit, plural, one{ander bestand} other{andere bestander}} uploaden.',
    'in {delta, plural, =1{a day} other{# days}}' => 'binnen {delta, plural, =1{een dag} other{# dagen}}',
    'in {delta, plural, =1{a minute} other{# minutes}}' => 'binnen {delta, plural, =1{een minuut} other{# minuten}}',
    'in {delta, plural, =1{a month} other{# months}}' => 'binnen {delta, plural, =1{een maand} other{# maanden}}',
    'in {delta, plural, =1{a second} other{# seconds}}' => 'binnen {delta, plural, =1{een seconde} other{# seconden}}',
    'in {delta, plural, =1{a year} other{# years}}' => 'binnen {delta, plural, =1{een jaar} other{# jaren}}',
    'in {delta, plural, =1{an hour} other{# hours}}' => 'binnen {delta, plural, =1{een uur} other{# uur}}',
    'just now' => 'zojuist',
    'the input value' => 'de invoerwaarde',
    '{attribute} "{value}" has already been taken.' => '{attribute} "{value}" is reeds in gebruik.',
    '{attribute} cannot be blank.' => '{attribute} mag niet leeg zijn.',
    '{attribute} is invalid.' => '{attribute} is ongeldig.',
    '{attribute} is not a valid URL.' => '{attribute} is geen geldige URL.',
    '{attribute} is not a valid email address.' => '{attribute} is geen geldig emailadres.',
    '{attribute} must be "{requiredValue}".' => '{attribute} moet "{requiredValue}" zijn.',
    '{attribute} must be a number.' => '{attribute} moet een getal zijn.',
    '{attribute} must be a string.' => '{attribute} moet een string zijn.',
    '{attribute} must be an integer.' => '{attribute} moet een geheel getal zijn.',
    '{attribute} must be either "{true}" or "{false}".' => '{attribute} moet "{true}" of "{false}" zijn.',
    '{attribute} must be greater than "{compareValue}".' => '{attribute} moet groter zijn dan "{compareValue}".',
    '{attribute} must be greater than or equal to "{compareValue}".' => '{attribute} moet groter dan of gelijk aan "{compareValue}" zijn.',
    '{attribute} must be less than "{compareValue}".' => '{attribute} moet minder zijn dan "{compareValue}".',
    '{attribute} must be less than or equal to "{compareValue}".' => '{attribute} moet minder dan of gelijk aan "{compareValue}" zijn.',
    '{attribute} must be no greater than {max}.' => '{attribute} mag niet groter zijn dan {max}.',
    '{attribute} must be no less than {min}.' => '{attribute} mag niet kleiner zijn dan {min}.',
    '{attribute} must be repeated exactly.' => '{attribute} moet exact herhaald worden.',
    '{attribute} must not be equal to "{compareValue}".' => '{attribute} mag niet gelijk zijn aan "{compareValue}".',
    '{attribute} should contain at least {min, number} {min, plural, one{character} other{characters}}.' => '{attribute} moet minstens {min, number} {min, plural, one{karakter} other{karakters}} bevatten.',
    '{attribute} should contain at most {max, number} {max, plural, one{character} other{characters}}.' => '{attribute} mag maximaal {max, number} {max, plural, one{karakter} other{karakters}} bevatten.',
    '{attribute} should contain {length, number} {length, plural, one{character} other{characters}}.' => '{attribute} moet precies {min, number} {min, plural, one{karakter} other{karakters}} bevatten.',
    '{delta, plural, =1{a day} other{# days}} ago' => '{delta, plural, =1{een dag} other{# dagen}} geleden',
    '{delta, plural, =1{a minute} other{# minutes}} ago' => '{delta, plural, =1{een minuut} other{# minuten}} geleden',
    '{delta, plural, =1{a month} other{# months}} ago' => '{delta, plural, =1{een maand} other{# maanden}} geleden',
    '{delta, plural, =1{a second} other{# seconds}} ago' => '{delta, plural, =1{een seconde} other{# seconden}} geleden',
    '{delta, plural, =1{a year} other{# years}} ago' => '{delta, plural, =1{een jaar} other{# jaren}} geleden',
    '{delta, plural, =1{an hour} other{# hours}} ago' => '{delta, plural, =1{een uur} other{# uren}} geleden',
    '{nFormatted} B' => '{nFormatted} B',
    '{nFormatted} GB' => '{nFormatted} GB',
    '{nFormatted} GiB' => '{nFormatted} GiB',
    '{nFormatted} KB' => '{nFormatted} KB',
    '{nFormatted} KiB' => '{nFormatted} KiB',
    '{nFormatted} MB' => '{nFormatted} MB',
    '{nFormatted} MiB' => '{nFormatted} MiB',
    '{nFormatted} PB' => '{nFormatted} PB',
    '{nFormatted} PiB' => '{nFormatted} PiB',
    '{nFormatted} TB' => '{nFormatted} TB',
    '{nFormatted} TiB' => '{nFormatted} TiB',
    '{nFormatted} {n, plural, =1{byte} other{bytes}}' => '{nFormatted} {n, plural, =1{byte} other{bytes}}',
    '{nFormatted} {n, plural, =1{gibibyte} other{gibibytes}}' => '{nFormatted} {n, plural, =1{gibibyte} other{gibibytes}}',
    '{nFormatted} {n, plural, =1{gigabyte} other{gigabytes}}' => '{nFormatted} {n, plural, =1{gigabyte} other{gigabytes}}',
    '{nFormatted} {n, plural, =1{kibibyte} other{kibibytes}}' => '{nFormatted} {n, plural, =1{kibibyte} other{kibibytes}}',
    '{nFormatted} {n, plural, =1{kilobyte} other{kilobytes}}' => '{nFormatted} {n, plural, =1{kilobyte} other{kilobytes}}',
    '{nFormatted} {n, plural, =1{mebibyte} other{mebibytes}}' => '{nFormatted} {n, plural, =1{mebibyte} other{mebibytes}}',
    '{nFormatted} {n, plural, =1{megabyte} other{megabytes}}' => '{nFormatted} {n, plural, =1{megabyte} other{megabytes}}',
    '{nFormatted} {n, plural, =1{pebibyte} other{pebibytes}}' => '{nFormatted} {n, plural, =1{pebibyte} other{pebibytes}}',
    '{nFormatted} {n, plural, =1{petabyte} other{petabytes}}' => '{nFormatted} {n, plural, =1{petabyte} other{petabytes}}',
    '{nFormatted} {n, plural, =1{tebibyte} other{tebibytes}}' => '{nFormatted} {n, plural, =1{tebibyte} other{tebibytes}}',
    '{nFormatted} {n, plural, =1{terabyte} other{terabytes}}' => '{nFormatted} {n, plural, =1{terabyte} other{terabytes}}',
];
