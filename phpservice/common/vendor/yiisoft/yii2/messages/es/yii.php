<?php
/**
 * Message translations.
 *
 * This file is automatically generated by 'yii message/extract' command.
 * It contains the localizable messages extracted from source code.
 * You may modify this file by translating the extracted messages.
 *
 * Each array element represents the translation (value) of a message (key).
 * If the value is empty, the message is considered as not translated.
 * Messages that no longer need translation will have their translations
 * enclosed between a pair of '@@' marks.
 *
 * Message string can be used with plural forms format. Check i18n section
 * of the guide for details.
 *
 * NOTE: this file must be saved in UTF-8 encoding.
 */
return [
    'Powered by {yii}' => 'Desarrollado con {yii}',
    'The requested view "{name}" was not found.' => 'La vista solicitada "{name}" no fue encontrada',
    'Yii Framework' => 'Yii Framework',
    'just now' => 'justo ahora',
    '{attribute} contains wrong subnet mask.' => '{attribute} contiene la máscara de subred incorrecta',
    '{attribute} is not in the allowed range.' => '{attribute} no está en el rango permitido.',
    '{attribute} must be a valid IP address.' => '{attribute} debe ser una dirección IP válida ',
    '{attribute} must be an IP address with specified subnet.' => '{attribute} debe ser una dirección IP con subred especificada.',
    '{attribute} must be equal to "{compareValueOrAttribute}".' => '{attribute} debe ser igual a "{compareValueOrAttribute}".',
    '{attribute} must be greater than "{compareValueOrAttribute}".' => '{attribute} debe ser mayor a "{compareValueOrAttribute}".',
    '{attribute} must be greater than or equal to "{compareValueOrAttribute}".' => '{attribute} debe ser mayor o igual a "{compareValueOrAttribute}".',
    '{attribute} must be less than "{compareValueOrAttribute}".' => '{attribute} debe ser menor a "{compareValueOrAttribute}".',
    '{attribute} must be less than or equal to "{compareValueOrAttribute}".' => '{attribute} debe ser menor  o igual a "{compareValueOrAttribute}".',
    '{attribute} must not be a subnet.' => '{attribute} no debe ser una subnet.',
    '{attribute} must not be an IPv4 address.' => '{attribute} no debe ser una dirección IPv4.',
    '{attribute} must not be an IPv6 address.' => '{attribute} no debe ser una dirección IPv6.',
    '{attribute} must not be equal to "{compareValueOrAttribute}".' => '{attribute} no debe ser igual a "{compareValueOrAttribute}".',
    '{delta, plural, =1{1 day} other{# days}}' => '{delta, plural, =1{1 día} other{# días}}',
    '{delta, plural, =1{1 hour} other{# hours}}' => '{delta, plural, =1{1 hora} other{# horas}}',
    '{delta, plural, =1{1 minute} other{# minutes}}' => '{delta, plural, =1{1 minuto} other{# minutos}}',
    '{delta, plural, =1{1 month} other{# months}}' => '{delta, plural, =1{1 mes} other{# meses}}',
    '{delta, plural, =1{1 second} other{# seconds}}' => '{delta, plural, =1{1 segundo} other{# segundos}}',
    '{delta, plural, =1{1 year} other{# years}}' => '{delta, plural, =1{1 año} other{# años}}',
    '{nFormatted} B' => '{nFormatted} B',
    '{nFormatted} GB' => '{nFormatted} GB',
    '{nFormatted} GiB' => '{nFormatted} GiB',
    '{nFormatted} KB' => '{nFormatted} KB',
    '{nFormatted} KiB' => '{nFormatted} KiB',
    '{nFormatted} MB' => '{nFormatted} MB',
    '{nFormatted} MiB' => '{nFormatted} MiB',
    '{nFormatted} PB' => '{nFormatted} PB',
    '{nFormatted} PiB' => '{nFormatted} PiB',
    '{nFormatted} TB' => '{nFormatted} TB',
    '{nFormatted} TiB' => '{nFormatted} TiB',
    '{nFormatted} {n, plural, =1{byte} other{bytes}}' => '{nFormatted} {n, plural, =1{byte} other{bytes}}',
    '{nFormatted} {n, plural, =1{gibibyte} other{gibibytes}}' => '{nFormatted} {n, plural, =1{gibibyte} other{gibibytes}}',
    '{nFormatted} {n, plural, =1{gigabyte} other{gigabytes}}' => '{nFormatted} {n, plural, =1{gigabyte} other{gigabytes}}',
    '{nFormatted} {n, plural, =1{kibibyte} other{kibibytes}}' => '{nFormatted} {n, plural, =1{kibibyte} other{kibibytes}}',
    '{nFormatted} {n, plural, =1{kilobyte} other{kilobytes}}' => '{nFormatted} {n, plural, =1{kilobyte} other{kilobytes}}',
    '{nFormatted} {n, plural, =1{mebibyte} other{mebibytes}}' => '{nFormatted} {n, plural, =1{mebibyte} other{mebibytes}}',
    '{nFormatted} {n, plural, =1{megabyte} other{megabytes}}' => '{nFormatted} {n, plural, =1{megabyte} other{megabytes}}',
    '{nFormatted} {n, plural, =1{pebibyte} other{pebibytes}}' => '{nFormatted} {n, plural, =1{pebibyte} other{pebibytes}}',
    '{nFormatted} {n, plural, =1{petabyte} other{petabytes}}' => '{nFormatted} {n, plural, =1{petabyte} other{petabytes}}',
    '{nFormatted} {n, plural, =1{tebibyte} other{tebibytes}}' => '{nFormatted} {n, plural, =1{tebibyte} other{tebibytes}}',
    '{nFormatted} {n, plural, =1{terabyte} other{terabytes}}' => '{nFormatted} {n, plural, =1{terabyte} other{terabytes}}',
    '(not set)' => '(no definido)',
    'An internal server error occurred.' => 'Hubo un error interno del servidor.',
    'Are you sure you want to delete this item?' => '¿Está seguro de eliminar este elemento?',
    'Delete' => 'Eliminar',
    'Error' => 'Error',
    'File upload failed.' => 'Falló la subida del archivo.',
    'Home' => 'Inicio',
    'Invalid data received for parameter "{param}".' => 'Se recibieron datos erróneos para el parámetro "{param}"',
    'Login Required' => 'Login Requerido',
    'Missing required arguments: {params}' => 'Argumentos requeridos ausentes: {params}',
    'Missing required parameters: {params}' => 'Parámetros requeridos ausentes: {params}',
    'No' => 'No',
    'No results found.' => 'No se encontraron resultados.',
    'Only files with these MIME types are allowed: {mimeTypes}.' => 'Sólo se aceptan archivos con los siguientes tipos MIME: {mimeTypes}.',
    'Only files with these extensions are allowed: {extensions}.' => 'Sólo se aceptan archivos con las siguientes extensiones: {extensions}',
    'Page not found.' => 'Página no encontrada.',
    'Please fix the following errors:' => 'Por favor corrija los siguientes errores:',
    'Please upload a file.' => 'Por favor suba un archivo.',
    'Showing <b>{begin, number}-{end, number}</b> of <b>{totalCount, number}</b> {totalCount, plural, one{item} other{items}}.' => 'Mostrando <b>{begin, number}-{end, number}</b> de <b>{totalCount, number}</b> {totalCount, plural, one{elemento} other{elementos}}.',
    'The file "{file}" is not an image.' => 'El archivo "{file}" no es una imagen.',
    'The file "{file}" is too big. Its size cannot exceed {formattedLimit}.' => 'El archivo "{file}" es demasiado grande. Su tamaño no puede exceder {formattedLimit}.',
    'The file "{file}" is too small. Its size cannot be smaller than {formattedLimit}.' => 'El archivo "{file}" es demasiado pequeño. Su tamaño no puede ser menor a {formattedLimit}.',
    'The format of {attribute} is invalid.' => 'El formato de {attribute} es inválido.',
    'The image "{file}" is too large. The height cannot be larger than {limit, number} {limit, plural, one{pixel} other{pixels}}.' => 'La imagen "{file}" es demasiado grande. La altura no puede ser mayor a {limit, number} {limit, plural, one{píxel} other{píxeles}}.',
    'The image "{file}" is too large. The width cannot be larger than {limit, number} {limit, plural, one{pixel} other{pixels}}.' => 'La imagen "{file}" es demasiado grande. La anchura no puede ser mayor a {limit, number} {limit, plural, one{píxel} other{píxeles}}.',
    'The image "{file}" is too small. The height cannot be smaller than {limit, number} {limit, plural, one{pixel} other{pixels}}.' => 'La imagen "{file}" es demasiado pequeña. La altura no puede ser menor a {limit, number} {limit, plural, one{píxel} other{píxeles}}.',
    'The image "{file}" is too small. The width cannot be smaller than {limit, number} {limit, plural, one{pixel} other{pixels}}.' => 'La imagen "{file}" es demasiado pequeña. La anchura no puede ser menor a {limit, number} {limit, plural, one{píxel} other{píxeles}}.',
    'The verification code is incorrect.' => 'El código de verificación es incorrecto.',
    'Total <b>{count, number}</b> {count, plural, one{item} other{items}}.' => 'Total <b>{count, number}</b> {count, plural, one{elemento} other{elementos}}.',
    'Unable to verify your data submission.' => 'Incapaz de verificar los datos enviados.',
    'Unknown alias: -{name}' => 'Alias desconocido: -{name}',
    'Unknown option: --{name}' => 'Opción desconocida: --{name}',
    'Update' => 'Actualizar',
    'View' => 'Ver',
    'Yes' => 'Sí',
    'You are not allowed to perform this action.' => 'No tiene permitido ejecutar esta acción.',
    'You can upload at most {limit, number} {limit, plural, one{file} other{files}}.' => 'Puedes subir como máximo {limit, number} {limit, plural, one{archivo} other{archivos}}.',
    'in {delta, plural, =1{a day} other{# days}}' => 'en {delta, plural, =1{un día} other{# días}}',
    'in {delta, plural, =1{a minute} other{# minutes}}' => 'en {delta, plural, =1{un minuto} other{# minutos}}',
    'in {delta, plural, =1{a month} other{# months}}' => 'en {delta, plural, =1{un mes} other{# meses}}',
    'in {delta, plural, =1{a second} other{# seconds}}' => 'en {delta, plural, =1{un segundo} other{# segundos}}',
    'in {delta, plural, =1{a year} other{# years}}' => 'en {delta, plural, =1{un año} other{# años}}',
    'in {delta, plural, =1{an hour} other{# hours}}' => 'en {delta, plural, =1{una hora} other{# horas}}',
    'the input value' => 'el valor de entrada',
    '{attribute} "{value}" has already been taken.' => '{attribute} "{value}" ya ha sido utilizado.',
    '{attribute} cannot be blank.' => '{attribute} no puede estar vacío.',
    '{attribute} is invalid.' => '{attribute} es inválido.',
    '{attribute} is not a valid URL.' => '{attribute} no es una URL válida.',
    '{attribute} is not a valid email address.' => '{attribute} no es una dirección de correo válida.',
    '{attribute} must be "{requiredValue}".' => '{attribute} debe ser "{requiredValue}".',
    '{attribute} must be a number.' => '{attribute} debe ser un número.',
    '{attribute} must be a string.' => '{attribute} debe ser una cadena de caracteres.',
    '{attribute} must be an integer.' => '{attribute} debe ser un número entero.',
    '{attribute} must be either "{true}" or "{false}".' => '{attribute} debe ser "{true}" o "{false}".',
    '{attribute} must be no greater than {max}.' => '{attribute} no debe ser mayor a {max}.',
    '{attribute} must be no less than {min}.' => '{attribute} no debe ser menor a {min}.',
    '{attribute} should contain at least {min, number} {min, plural, one{character} other{characters}}.' => '{attribute} debería contener al menos {min, number} {min, plural, one{letra} other{letras}}.',
    '{attribute} should contain at most {max, number} {max, plural, one{character} other{characters}}.' => '{attribute} debería contener como máximo {max, number} {max, plural, one{letra} other{letras}}.',
    '{attribute} should contain {length, number} {length, plural, one{character} other{characters}}.' => '{attribute} debería contener {length, number} {length, plural, one{letra} other{letras}}.',
    '{delta, plural, =1{a day} other{# days}} ago' => 'hace {delta, plural, =1{un día} other{# días}}',
    '{delta, plural, =1{a minute} other{# minutes}} ago' => 'hace {delta, plural, =1{un minuto} other{# minutos}}',
    '{delta, plural, =1{a month} other{# months}} ago' => 'hace {delta, plural, =1{un mes} other{# meses}}',
    '{delta, plural, =1{a second} other{# seconds}} ago' => 'hace {delta, plural, =1{un segundo} other{# segundos}}',
    '{delta, plural, =1{a year} other{# years}} ago' => 'hace {delta, plural, =1{un año} other{# años}}',
    '{delta, plural, =1{an hour} other{# hours}} ago' => 'hace {delta, plural, =1{una hora} other{# horas}}',
];
