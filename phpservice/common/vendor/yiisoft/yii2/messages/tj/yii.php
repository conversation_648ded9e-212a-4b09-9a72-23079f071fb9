<?php
/**
* Message translations.
*
* This file is automatically generated by 'yii message' command.
* It contains the localizable messages extracted from source code.
* You may modify this file by translating the extracted messages.
*
* Each array element represents the translation (value) of a message (key).
* If the value is empty, the message is considered as not translated.
* Messages that no longer need translation will have their translations
* enclosed between a pair of '@@' marks.
*
* Message string can be used with plural forms format. Check i18n section
* of the guide for details.
*
* NOTE: this file must be saved in UTF-8 encoding.
*/
return [
    '{nFormatted} B' => '{nFormatted} B',
    '{nFormatted} KB' => '{nFormatted} KB',
    '{nFormatted} KiB' => '{nFormatted} KiB',
    '{nFormatted} MB' => '{nFormatted} MB',
    '{nFormatted} MiB' => '{nFormatted} MiB',
    '{nFormatted} GB' => '{nFormatted} GB',
    '{nFormatted} GiB' => '{nFormatted} GiB',
    '{nFormatted} PB' => '{nFormatted} PB',
    '{nFormatted} PiB' => '{nFormatted} PiB',
    '{nFormatted} TB' => '{nFormatted} TB',
    '{nFormatted} TiB' => '{nFormatted} TiB',
    '{nFormatted} {n, plural, =1{byte} other{bytes}}' => '{nFormatted} байт',
    '{nFormatted} {n, plural, =1{kibibyte} other{kibibytes}}' => '{nFormatted} кибибайт',
    '{nFormatted} {n, plural, =1{kilobyte} other{kilobytes}}' => '{nFormatted} килобайт',
    '{nFormatted} {n, plural, =1{mebibyte} other{mebibytes}}' => '{nFormatted} мебибайт',
    '{nFormatted} {n, plural, =1{megabyte} other{megabytes}}' => '{nFormatted} мегабайт',
    '{nFormatted} {n, plural, =1{gibibyte} other{gibibytes}}' => '{nFormatted} гибибайт',
    '{nFormatted} {n, plural, =1{gigabyte} other{gigabytes}}' => '{nFormatted} гигабайт',
    '{nFormatted} {n, plural, =1{pebibyte} other{pebibytes}}' => '{nFormatted} пебибайт',
    '{nFormatted} {n, plural, =1{petabyte} other{petabytes}}' => '{nFormatted} петабайт',
    '{nFormatted} {n, plural, =1{tebibyte} other{tebibytes}}' => '{nFormatted} тебибайт',
    '{nFormatted} {n, plural, =1{terabyte} other{terabytes}}' => '{nFormatted} терабайт',
    'Are you sure you want to delete this item?' => 'Оё шумо дар ҳақиқат мехоҳед, ки ин нашрро нест кунед?',
    'The requested view "{name}" was not found.' => 'Файл "{name}" барои манзур ёфт нашуд',
    '(not set)' => '(танзим нашуда)',
    'An internal server error occurred.' => 'Хатои дохилии сервер рух дод.',
    'Delete' => 'Нест',
    'Error' => 'Хато',
    'File upload failed.' => 'Аплоди файл шикаст хурд.',
    'Home' => 'Асосӣ',
    'Invalid data received for parameter "{param}".' => 'Маълумоти номувофиқ барои параметри "{param}" гирифта шуд.',
    'Login Required' => 'Вуруд маҷбурист',
    'Missing required arguments: {params}' => 'Аргументи лозими вуҷд надорад: {params}',
    'Missing required parameters: {params}' => 'Параметри лозими вуҷуд надорад: {params}',
    'No' => 'На',
    'No results found.' => 'Чизе ёфт нашуд.',
    'Page not found.' => 'Саҳифа ёфт нашуд.',
    'Please fix the following errors:' => 'Илтимос хатоҳои зеринро ислоҳ кунед:',
    'Only files with these extensions are allowed: {extensions}.' => 'Танҳо файлҳои бо ин пасванд иҷоза аст: {extensions}.',
    'Only files with these MIME types are allowed: {mimeTypes}.' => 'Фақат ин намуди файлҳо иҷозат аст: {mimeTypes}.',
    'The format of {attribute} is invalid.' => 'Формати {attribute} ғалат буд.',
    'Please upload a file.' => 'Илтимос файл аплод кунед.',
    'Showing <b>{begin, number}-{end, number}</b> of <b>{totalCount, number}</b> {totalCount, plural, one{item} other{items}}.' => 'Манзури <b>{begin, number}-{end, number}</b> аз <b>{totalCount, number}</b>.',
    'The file "{file}" is not an image.' => 'Файл "{file}" расм набуд.',
    'The file "{file}" is too big. Its size cannot exceed {formattedLimit}.' => 'Файл "{file}" калон аст. Аз {formattedLimit} набояд калонтар бошад.',
    'The file "{file}" is too small. Its size cannot be smaller than {formattedLimit}.' => 'Файл "{file}" хурд аст. Аз {formattedLimit} набояд хурдтар бошад.',
    'The image "{file}" is too large. The height cannot be larger than {limit, number} {limit, plural, one{pixel} other{pixels}}.' => 'Расми "{file}" баланд аст. Баландияш набояд аз {limit, number} зиёд бошад.',
    'The image "{file}" is too large. The width cannot be larger than {limit, number} {limit, plural, one{pixel} other{pixels}}.' => 'Расми "{file}" паҳн аст. Паҳнияш набояд аз {limit, number} зиёд бошад.',
    'The image "{file}" is too small. The height cannot be smaller than {limit, number} {limit, plural, one{pixel} other{pixels}}.' => 'Расми "{file}" хурд аст. Баландияш набояд аз {limit, number} хурд бошад.',
    'The image "{file}" is too small. The width cannot be smaller than {limit, number} {limit, plural, one{pixel} other{pixels}}.' => 'Расми "{file}" хурд аст. Паҳнияш набояд аз {limit, number} хурд бошад.',
    'You can upload at most {limit, number} {limit, plural, one{file} other{files}}.' => 'Ҳамаги {limit, number} аплод карда метавонед.',
    'The verification code is incorrect.' => 'Коди санҷиши ғалат аст.',
    'Total <b>{count, number}</b> {count, plural, one{item} other{items}}.' => 'Ҳамаги <b>{count, number}</b> нашр.',
    'Unable to verify your data submission.' => 'Маълумоти фиристодаи шуморо санҷиш карда натавонистам.',
    'Unknown option: --{name}' => 'Гузинаи номаълум: --{name}',
    'Update' => 'Тағир',
    'View' => 'Манзур',
    'Yes' => 'Ҳа',
    'just now' => 'ҳоло',
    'the input value' => 'маълумоти вурудбуда',
    'You are not allowed to perform this action.' => 'Шумо барои анҷоми ин амал дастнорасед.',
    'in {delta, plural, =1{a second} other{# seconds}}' => '{delta} сонияи дигар',
    'in {delta, plural, =1{a minute} other{# minutes}}' => '{delta} дақиқаи дигар',
    'in {delta, plural, =1{an hour} other{# hours}}' => '{delta} соати дигар',
    'in {delta, plural, =1{a day} other{# days}}' => '{delta} рӯзи дигар',
    'in {delta, plural, =1{a month} other{# months}}' => '{delta} моҳи дигар',
    'in {delta, plural, =1{a year} other{# years}}' => '{delta} соли дигар',
    '{delta, plural, =1{a second} other{# seconds}} ago' => '{delta} сонияи қабл',
    '{delta, plural, =1{a minute} other{# minutes}} ago' => '{delta} дақиқаи қабл',
    '{delta, plural, =1{an hour} other{# hours}} ago' => '{delta} соати қабл',
    '{delta, plural, =1{a day} other{# days}} ago' => '{delta} рӯзи қабл',
    '{delta, plural, =1{a month} other{# months}} ago' => '{delta} моҳи қабл',
    '{delta, plural, =1{a year} other{# years}} ago' => '{delta} сол пеш',
    '{attribute} "{value}" has already been taken.' => '{attribute} "{value}" машғул аст.',
    '{attribute} cannot be blank.' => '{attribute} набояд холи бошад.',
    '{attribute} is invalid.' => '{attribute} ғалат аст.',
    '{attribute} is not a valid URL.' => '{attribute} URL ғалат аст.',
    '{attribute} is not a valid email address.' => '{attribute} E-mail одреси ғалат аст.',
    '{attribute} must be "{requiredValue}".' => '{attribute} бояд "{requiredValue}" бошад.',
    '{attribute} must be a number.' => '{attribute} бояд адад бошад.',
    '{attribute} must be a string.' => '{attribute} бояд хат бошад.',
    '{attribute} must be an integer.' => '{attribute} бояд адади комил бошад.',
    '{attribute} must be either "{true}" or "{false}".' => '{attribute} бояд ё "{true}" ё "{false}" бошад.',
    '{attribute} must be greater than "{compareValue}".' => '{attribute} бояд аз "{compareValue}" калон бошад.',
    '{attribute} must be greater than or equal to "{compareValue}".' => '{attribute} бояд калон ё баробари "{compareValue}" бошад.',
    '{attribute} must be less than "{compareValue}".' => '{attribute} бояд аз "{compareValue}" хурд бошад.',
    '{attribute} must be less than or equal to "{compareValue}".' => '{attribute} бояд хурд ё баробари "{compareValue}" бошад.',
    '{attribute} must be no greater than {max}.' => '{attribute} бояд аз {max} зиёд набошад.',
    '{attribute} must be no less than {min}.' => '{attribute} бояд аз {min} кам набошад.',
    '{attribute} must be repeated exactly.' => '{attribute} айнан бояд такрор шавад.',
    '{attribute} must not be equal to "{compareValue}".' => '{attribute} бояд баробари "{compareValue}" набошад.',
    '{attribute} should contain at least {min, number} {min, plural, one{character} other{characters}}.' => '{attribute} хади ақал {min, number} рамз дошта бошад.',
    '{attribute} should contain at most {max, number} {max, plural, one{character} other{characters}}.' => '{attribute} хамаги {max, number} рамз дошта бошад.',
    '{attribute} should contain {length, number} {length, plural, one{character} other{characters}}.' => '{attribute} бояд {length, number} рамз дошта бошад.',
];
