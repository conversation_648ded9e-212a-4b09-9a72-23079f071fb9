<?php
/**
 * Message translations.
 *
 * This file is automatically generated by 'yii message' command.
 * It contains the localizable messages extracted from source code.
 * You may modify this file by translating the extracted messages.
 *
 * Each array element represents the translation (value) of a message (key).
 * If the value is empty, the message is considered as not translated.
 * Messages that no longer need translation will have their translations
 * enclosed between a pair of '@@' marks.
 *
 * Message string can be used with plural forms format. Check i18n section
 * of the guide for details.
 *
 * NOTE: this file must be saved in UTF-8 encoding.
 */
return [
    'Unknown alias: -{name}' => '未知的別名: -{name}',
    '(not set)' => '(未設定)',
    'An internal server error occurred.' => '內部系統錯誤。',
    'Are you sure you want to delete this item?' => '您確定要刪除此項嗎？',
    'Delete' => '刪除',
    'Error' => '錯誤',
    'File upload failed.' => '未能上傳檔案。',
    'Home' => '首頁',
    'Invalid data received for parameter "{param}".' => '"{param}" 參數資料不正確。',
    'Login Required' => '需要登入',
    'Missing required arguments: {params}' => '參數不齊全：{params}',
    'Missing required parameters: {params}' => '參數不齊全：{params}',
    'No' => '否',
    'No help for unknown command "{command}".' => '子命令 "{command}" 發生未知的錯誤。',
    'No help for unknown sub-command "{command}".' => '子命令 "{command}" 發生未知的錯誤。',
    'No results found.' => '沒有資料。',
    'Only files with these MIME types are allowed: {mimeTypes}.' => '只允許這些MIME類型的文件: {mimeTypes}。',
    'Only files with these extensions are allowed: {extensions}.' => '只可以使用以下擴充名的檔案：{extensions}。',
    'Page not found.' => '找不到頁面。',
    'Please fix the following errors:' => '請修正以下錯誤：',
    'Please upload a file.' => '請上傳一個檔案。',
    'Powered by {yii}' => '技术支持 {yii}',
    'Showing <b>{begin, number}-{end, number}</b> of <b>{totalCount, number}</b> {totalCount, plural, one{item} other{items}}.' => '第 <b>{begin, number}-{end, number}</b> 項，共 <b>{totalCount, number}</b> 項資料.',
    'The file "{file}" is not an image.' => '檔案 "{file}" 不是一個圖片檔案。',
    'The file "{file}" is too big. Its size cannot exceed {formattedLimit}.' => '檔案"{file}"太大了。它的大小不可以超過{formattedLimit}。',
    'The file "{file}" is too small. Its size cannot be smaller than {formattedLimit}.' => '文件"{file}"太小了。它的大小不可以小於{formattedLimit}。',
    'The file "{file}" is too big. Its size cannot exceed {limit, number} {limit, plural, one{byte} other{bytes}}.' => '檔案 "{file}" 太大。它的大小不可以超過 {limit, number} 位元組。',
    'The file "{file}" is too small. Its size cannot be smaller than {limit, number} {limit, plural, one{byte} other{bytes}}.' => '檔案 "{file}" 太小。它的大小不可以小於 {limit, number} 位元組。',
    'The format of {attribute} is invalid.' => '屬性 {attribute} 的格式不正確。',
    'The image "{file}" is too large. The height cannot be larger than {limit, number} {limit, plural, one{pixel} other{pixels}}.' => '圖像 "{file}" 太大。它的高度不可以超過 {limit, number} 像素。',
    'The image "{file}" is too large. The width cannot be larger than {limit, number} {limit, plural, one{pixel} other{pixels}}.' => '圖像 "{file}" 太大。它的寬度不可以超過 {limit, number} 像素。',
    'The image "{file}" is too small. The height cannot be smaller than {limit, number} {limit, plural, one{pixel} other{pixels}}.' => '圖像 "{file}" 太小。它的高度不可以小於 {limit, number} 像素。',
    'The image "{file}" is too small. The width cannot be smaller than {limit, number} {limit, plural, one{pixel} other{pixels}}.' => '圖像 "{file}" 太小。它的寬度不可以小於 {limit, number} 像素。',
    'The requested view "{name}" was not found.' => '所請求的視圖不存在"{name}"。',
    'The verification code is incorrect.' => '驗證碼不正確。',
    'Total <b>{count, number}</b> {count, plural, one{item} other{items}}.' => '總計 <b>{count, number}</b> 項資料。',
    'Unable to verify your data submission.' => '您提交的資料無法被驗證。',
    'Unknown command "{command}".' => '未知的指令 "{command}"。',
    'Unknown option: --{name}' => '未知的選項：--{name}',
    'Update' => '更新',
    'View' => '查看',
    'Yes' => '是',
    'Yii Framework' => 'Yii 框架',
    'You are not allowed to perform this action.' => '您沒有執行此操作的權限。',
    'You can upload at most {limit, number} {limit, plural, one{file} other{files}}.' => '您最多可以上載 {limit, number} 個檔案。',
    'the input value' => '該輸入',
    '{attribute} "{value}" has already been taken.' => '{attribute} 的值 "{value}" 已經被佔用了。',
    '{attribute} cannot be blank.' => '{attribute} 不能為空白。',
    '{attribute} contains wrong subnet mask.' => '{attribute} 屬性包含錯誤的子網掩碼。',
    '{attribute} is invalid.' => '{attribute} 不正確。',
    '{attribute} is not a valid URL.' => '{attribute} 不是一個正確的網址。',
    '{attribute} is not a valid email address.' => '{attribute} 不是一個正確的電郵地址。',
    '{attribute} is not in the allowed range.' => '{attribute} 不在允許的範圍內。',
    '{attribute} must be "{requiredValue}".' => '{attribute}必須為 "{requiredValue}"。',
    '{attribute} must be a number.' => '{attribute} 必須為數字。',
    '{attribute} must be a string.' => '{attribute} 必須為字串。',
    '{attribute} must be a valid IP address.' => '{attribute} 必須是一個有效的IP地址。',
    '{attribute} must be an IP address with specified subnet.' => '{attribute} 必須指定一個IP地址和子網。',
    '{attribute} must be an integer.' => '{attribute} 必須為整數。',
    '{attribute} must be either "{true}" or "{false}".' => '{attribute} 必須為 "{true}" 或 "{false}"。',
    '{attribute} must be greater than "{compareValue}".' => '{attribute} 必須大於 "{compareValue}"。',
    '{attribute} must be greater than or equal to "{compareValue}".' => '{attribute} 必須大或等於 "{compareValue}"。',
    '{attribute} must be less than "{compareValue}".' => '{attribute} 必須小於 "{compareValue}"。',
    '{attribute} must be less than or equal to "{compareValue}".' => '{attribute} 必須少或等於 "{compareValue}"。',
    '{attribute} must be no greater than {max}.' => '{attribute} 不可以大於 {max}。',
    '{attribute} must be no less than {min}.' => '{attribute} 不可以少於 {min}。',
    '{attribute} must not be a subnet.' => '{attribute} 必須不是一個子網。',
    '{attribute} must not be an IPv4 address.' => '{attribute} 必須不是一個IPv4地址。',
    '{attribute} must not be an IPv6 address.' => '{attribute} 必須不是一個IPv6地址。',
    '{attribute} must be repeated exactly.' => '{attribute} 必須重複一致。',
    '{attribute} must not be equal to "{compareValue}".' => '{attribute} 不可以等於 "{compareValue}"。',
    '{attribute} should contain at least {min, number} {min, plural, one{character} other{characters}}.' => '{attribute} 應該包含至少 {min, number} 個字符。',
    '{attribute} should contain at most {max, number} {max, plural, one{character} other{characters}}.' => '{attribute} 只能包含最多 {max, number} 個字符。',
    '{attribute} should contain {length, number} {length, plural, one{character} other{characters}}.' => '{attribute} 應該包含 {length, number} 個字符。',
    'in {delta, plural, =1{a year} other{# years}}' => '{delta}年後',
    'in {delta, plural, =1{a month} other{# months}}' => '{delta}個月後',
    'in {delta, plural, =1{a day} other{# days}}' => '{delta}天後',
    'in {delta, plural, =1{an hour} other{# hours}}' => '{delta}小時後',
    'in {delta, plural, =1{a minute} other{# minutes}}' => '{delta}分鐘後',
    'in {delta, plural, =1{a second} other{# seconds}}' => '{delta}秒後',
    '{delta, plural, =1{1 day} other{# days}}' => '{delta} 天',
    '{delta, plural, =1{1 hour} other{# hours}}' => '{delta} 小時',
    '{delta, plural, =1{1 minute} other{# minutes}}' => '{delta} 分鐘',
    '{delta, plural, =1{1 month} other{# months}}' => '{delta} 月',
    '{delta, plural, =1{1 second} other{# seconds}}' => '{delta} 秒',
    '{delta, plural, =1{1 year} other{# years}}' => '{delta} 年',
    '{delta, plural, =1{a year} other{# years}} ago' => '{delta}年前',
    '{delta, plural, =1{a month} other{# months}} ago' => '{delta}個月前',
    '{delta, plural, =1{a day} other{# days}} ago' => '{delta}天前',
    '{delta, plural, =1{an hour} other{# hours}} ago' => '{delta}小時前',
    '{delta, plural, =1{a minute} other{# minutes}} ago' => '{delta}分鐘前',
    'just now' => '剛剛',
    '{delta, plural, =1{a second} other{# seconds}} ago' => '{delta}秒前',
    '{nFormatted} B' => '{nFormatted} B',
    '{nFormatted} GB' => '{nFormatted} GB',
    '{nFormatted} GiB' => '{nFormatted} GiB',
    '{nFormatted} KB' => '{nFormatted} KB',
    '{nFormatted} KiB' => '{nFormatted} KiB',
    '{nFormatted} MB' => '{nFormatted} MB',
    '{nFormatted} MiB' => '{nFormatted} MiB',
    '{nFormatted} PB' => '{nFormatted} PB',
    '{nFormatted} PiB' => '{nFormatted} PiB',
    '{nFormatted} TB' => '{nFormatted} TB',
    '{nFormatted} TiB' => '{nFormatted} TiB',
    '{nFormatted} {n, plural, =1{byte} other{bytes}}' => '{nFormatted} 字節',
    '{nFormatted} {n, plural, =1{gibibyte} other{gibibytes}}' => '{nFormatted} 千兆位二進制字節',
    '{nFormatted} {n, plural, =1{gigabyte} other{gigabytes}}' => '{nFormatted} 千兆字節',
    '{nFormatted} {n, plural, =1{kibibyte} other{kibibytes}}' => '{nFormatted} 千位二進制字節',
    '{nFormatted} {n, plural, =1{kilobyte} other{kilobytes}}' => '{nFormatted} 千字節',
    '{nFormatted} {n, plural, =1{mebibyte} other{mebibytes}}' => '{nFormatted} 兆位二進制字節',
    '{nFormatted} {n, plural, =1{megabyte} other{megabytes}}' => '{nFormatted} 兆字節',
    '{nFormatted} {n, plural, =1{pebibyte} other{pebibytes}}' => '{nFormatted} 拍位二進制字節',
    '{nFormatted} {n, plural, =1{petabyte} other{petabytes}}' => '{nFormatted} 拍字節',
    '{nFormatted} {n, plural, =1{tebibyte} other{tebibytes}}' => '{nFormatted} 太位二進制字節',
    '{nFormatted} {n, plural, =1{terabyte} other{terabytes}}' => '{nFormatted} 太字節',
];
