<?php
/**
 * Message translations.
 *
 * This file is automatically generated by 'yii message/extract' command.
 * It contains the localizable messages extracted from source code.
 * You may modify this file by translating the extracted messages.
 *
 * Each array element represents the translation (value) of a message (key).
 * If the value is empty, the message is considered as not translated.
 * Messages that no longer need translation will have their translations
 * enclosed between a pair of '@@' marks.
 *
 * Message string can be used with plural forms format. Check i18n section
 * of the guide for details.
 *
 * NOTE: this file must be saved in UTF-8 encoding.
 */
return [
    'Unknown alias: -{name}' => 'Unbekannter Alias: -{name}',
    '(not set)' => '(nicht gesetzt)',
    'An internal server error occurred.' => 'Es ist ein interner Serverfehler aufgetreten.',
    'Are you sure you want to delete this item?' => 'Wollen Sie diesen Eintrag wirklich löschen?',
    'Delete' => 'Löschen',
    'Error' => 'Fehler',
    'File upload failed.' => 'Das Hochladen der Datei ist gescheitert.',
    'Home' => 'Home',
    'Invalid data received for parameter "{param}".' => 'Ungültige Daten erhalten für Parameter "{param}".',
    'Login Required' => 'Anmeldung erforderlich',
    'Missing required arguments: {params}' => 'Pflichtargumente fehlen: {params}',
    'Missing required parameters: {params}' => 'Pflichtparameter fehlen: {params}',
    'No' => 'Nein',
    'No results found.' => 'Keine Ergebnisse gefunden',
    'Only files with these MIME types are allowed: {mimeTypes}.' => 'Es sind nur Dateien mit folgenden MIME-Typen erlaubt: {mimeTypes}.',
    'Only files with these extensions are allowed: {extensions}.' => 'Es sind nur Dateien mit folgenden Dateierweiterungen erlaubt: {extensions}.',
    'Page not found.' => 'Seite nicht gefunden.',
    'Please fix the following errors:' => 'Bitte korrigieren Sie die folgenden Fehler:',
    'Please upload a file.' => 'Bitte laden Sie eine Datei hoch.',
    'Powered by {yii}' => 'Basiert auf {yii}',
    'Showing <b>{begin, number}-{end, number}</b> of <b>{totalCount, number}</b> {totalCount, plural, one{item} other{items}}.' => 'Zeige <b>{begin, number}-{end, number}</b> von <b>{totalCount, number}</b> {totalCount, plural, one{Eintrag} other{Einträgen}}.',
    'The file "{file}" is not an image.' => 'Die Datei "{file}" ist kein Bild.',
    'The file "{file}" is too big. Its size cannot exceed {formattedLimit}.' => 'Die Datei "{file}" ist zu groß. Es sind maximal {formattedLimit} erlaubt.',
    'The file "{file}" is too small. Its size cannot be smaller than {formattedLimit}.' => 'Die Datei "{file}" ist zu klein. Es sind mindestens {formattedLimit} erforderlich.',
    'The format of {attribute} is invalid.' => 'Das Format von {attribute} ist ungültig.',
    'The image "{file}" is too large. The height cannot be larger than {limit, number} {limit, plural, one{pixel} other{pixels}}.' => 'Das Bild "{file}" ist zu groß. Es darf maximal {limit, number} Pixel hoch sein.',
    'The image "{file}" is too large. The width cannot be larger than {limit, number} {limit, plural, one{pixel} other{pixels}}.' => 'Das Bild "{file}" ist zu groß. Es darf maximal {limit, number} Pixel breit sein.',
    'The image "{file}" is too small. The height cannot be smaller than {limit, number} {limit, plural, one{pixel} other{pixels}}.' => 'Das Bild "{file}" ist zu klein. Es muss mindestens {limit, number} Pixel hoch sein.',
    'The image "{file}" is too small. The width cannot be smaller than {limit, number} {limit, plural, one{pixel} other{pixels}}.' => 'Das Bild "{file}" ist zu klein. Es muss mindestens {limit, number} Pixel breit sein.',
    'The requested view "{name}" was not found.' => 'Die View-Datei "{name}" konnte nicht gefunden werden.',
    'The verification code is incorrect.' => 'Der Prüfcode ist falsch.',
    'Total <b>{count, number}</b> {count, plural, one{item} other{items}}.' => 'Insgesamt <b>{count, number}</b> {count, plural, one{Eintrag} other{Einträge}}.',
    'Unable to verify your data submission.' => 'Es ist nicht möglich, Ihre Dateneingabe zu prüfen.',
    'Unknown option: --{name}' => 'Unbekannte Option: --{name}',
    'Update' => 'Bearbeiten',
    'View' => 'Anzeigen',
    'Yes' => 'Ja',
    'Yii Framework' => 'Yii Framework',
    'You are not allowed to perform this action.' => 'Sie dürfen diese Aktion nicht durchführen.',
    'You can upload at most {limit, number} {limit, plural, one{file} other{files}}.' => 'Sie können maximal {limit, number} {limit, plural, one{eine Datei} other{# Dateien}} hochladen.',
    'in {delta, plural, =1{a day} other{# days}}' => 'in {delta, plural, =1{einem Tag} other{# Tagen}}',
    'in {delta, plural, =1{a minute} other{# minutes}}' => 'in {delta, plural, =1{einer Minute} other{# Minuten}}',
    'in {delta, plural, =1{a month} other{# months}}' => 'in {delta, plural, =1{einem Monat} other{# Monaten}}',
    'in {delta, plural, =1{a second} other{# seconds}}' => 'in {delta, plural, =1{einer Sekunde} other{# Sekunden}}',
    'in {delta, plural, =1{a year} other{# years}}' => 'in {delta, plural, =1{einem Jahr} other{# Jahren}}',
    'in {delta, plural, =1{an hour} other{# hours}}' => 'in {delta, plural, =1{einer Stunde} other{# Stunden}}',
    'just now' => 'gerade jetzt',
    'the input value' => 'der eingegebene Wert',
    '{attribute} "{value}" has already been taken.' => '{attribute} "{value}" wird bereits verwendet.',
    '{attribute} cannot be blank.' => '{attribute} darf nicht leer sein.',
    '{attribute} contains wrong subnet mask.' => '{attribute} enthält ungültige Subnetz-Maske.',
    '{attribute} is invalid.' => '{attribute} ist ungültig.',
    '{attribute} is not a valid URL.' => '{attribute} ist keine gültige URL.',
    '{attribute} is not a valid email address.' => '{attribute} ist keine gültige Emailadresse.',
    '{attribute} is not in the allowed range.' => '{attribute} ist außerhalb des gültigen Bereichs.',
    '{attribute} must be "{requiredValue}".' => '{attribute} muss den Wert {requiredValue} haben.',
    '{attribute} must be a number.' => '{attribute} muss eine Zahl sein.',
    '{attribute} must be a string.' => '{attribute} muss eine Zeichenkette sein.',
    '{attribute} must be a valid IP address.' => '{attribute} muss eine gültige IP-Adresse sein.',
    '{attribute} must be an IP address with specified subnet.' => '{attribute} muss eine gültige IP-Adresse inklusive Subnetz-Maske sein.',
    '{attribute} must be an integer.' => '{attribute} muss eine Ganzzahl sein.',
    '{attribute} must be either "{true}" or "{false}".' => '{attribute} muss entweder "{true}" oder "{false}" sein.',
    '{attribute} must be equal to "{compareValueOrAttribute}".' => '{attribute} muss gleich mit "{compareValueOrAttribute}" sein.',
    '{attribute} must be greater than "{compareValueOrAttribute}".' => '{attribute} muss größer als "{compareValueOrAttribute}" sein.',
    '{attribute} must be greater than or equal to "{compareValueOrAttribute}".' => '{attribute} muss größer oder gleich "{compareValueOrAttribute}" sein.',
    '{attribute} must be less than "{compareValueOrAttribute}".' => '{attribute} muss kleiner als "{compareValueOrAttribute}" sein.',
    '{attribute} must be less than or equal to "{compareValueOrAttribute}".' => '{attribute} muss kleiner oder gleich "{compareValueOrAttribute}" sein.',
    '{attribute} must be no greater than {max}.' => '{attribute} darf nicht größer als {max} sein.',
    '{attribute} must be no less than {min}.' => '{attribute} darf nicht kleiner als {min} sein.',
    '{attribute} must not be a subnet.' => '{attribute} darf kein Subnetz sein.',
    '{attribute} must not be an IPv4 address.' => '{attribute} darf keine IPv4-Adresse sein.',
    '{attribute} must not be an IPv6 address.' => '{attribute} darf keine IPv6-Adresse sein.',
    '{attribute} must not be equal to "{compareValueOrAttribute}".' => '{attribute} darf nicht "{compareValueOrAttribute}" sein.',
    '{attribute} should contain at least {min, number} {min, plural, one{character} other{characters}}.' => '{attribute} muss mindestens {min, number} Zeichen enthalten.',
    '{attribute} should contain at most {max, number} {max, plural, one{character} other{characters}}.' => '{attribute} darf maximal {max, number} Zeichen enthalten.',
    '{attribute} should contain {length, number} {length, plural, one{character} other{characters}}.' => '{attribute} muss aus genau {length, number} Zeichen bestehen.',
    '{delta, plural, =1{1 day} other{# days}}' => '{delta, plural, =1{1 Tag} other{# Tage}}',
    '{delta, plural, =1{1 hour} other{# hours}}' => '{delta, plural, =1{1 Stunde} other{# Stunden}}',
    '{delta, plural, =1{1 minute} other{# minutes}}' => '{delta, plural, =1{1 Minute} other{# Minuten}}',
    '{delta, plural, =1{1 month} other{# months}}' => '{delta, plural, =1{1 Monat} other{# Monate}}',
    '{delta, plural, =1{1 second} other{# seconds}}' => '{delta, plural, =1{1 Sekunde} other{# Sekunden}}',
    '{delta, plural, =1{1 year} other{# years}}' => '{delta, plural, =1{1 Jahr} other{# Jahre}}',
    '{delta, plural, =1{a day} other{# days}} ago' => 'vor {delta, plural, =1{einem Tag} other{# Tagen}}',
    '{delta, plural, =1{a minute} other{# minutes}} ago' => 'vor {delta, plural, =1{einer Minute} other{# Minuten}}',
    '{delta, plural, =1{a month} other{# months}} ago' => 'vor {delta, plural, =1{einem Monat} other{# Monaten}}',
    '{delta, plural, =1{a second} other{# seconds}} ago' => 'vor {delta, plural, =1{einer Sekunde} other{# Sekunden}}',
    '{delta, plural, =1{a year} other{# years}} ago' => 'vor {delta, plural, =1{einem Jahr} other{# Jahren}}',
    '{delta, plural, =1{an hour} other{# hours}} ago' => 'vor {delta, plural, =1{einer Stunde} other{# Stunden}}',
    '{nFormatted} B' => '{nFormatted} B',
    '{nFormatted} GB' => '{nFormatted} GB',
    '{nFormatted} GiB' => '{nFormatted} GiB',
    '{nFormatted} KB' => '{nFormatted} KB',
    '{nFormatted} KiB' => '{nFormatted} KiB',
    '{nFormatted} MB' => '{nFormatted} MB',
    '{nFormatted} MiB' => '{nFormatted} MiB',
    '{nFormatted} PB' => '{nFormatted} PB',
    '{nFormatted} PiB' => '{nFormatted} PiB',
    '{nFormatted} TB' => '{nFormatted} TB',
    '{nFormatted} TiB' => '{nFormatted} TiB',
    '{nFormatted} {n, plural, =1{byte} other{bytes}}' => '{nFormatted} Byte',
    '{nFormatted} {n, plural, =1{gibibyte} other{gibibytes}}' => '{nFormatted} GibiByte',
    '{nFormatted} {n, plural, =1{gigabyte} other{gigabytes}}' => '{nFormatted} Gigabyte',
    '{nFormatted} {n, plural, =1{kibibyte} other{kibibytes}}' => '{nFormatted} KibiByte',
    '{nFormatted} {n, plural, =1{kilobyte} other{kilobytes}}' => '{nFormatted} Kilobyte',
    '{nFormatted} {n, plural, =1{mebibyte} other{mebibytes}}' => '{nFormatted} MebiByte',
    '{nFormatted} {n, plural, =1{megabyte} other{megabytes}}' => '{nFormatted} Megabyte',
    '{nFormatted} {n, plural, =1{pebibyte} other{pebibytes}}' => '{nFormatted} PebiByte',
    '{nFormatted} {n, plural, =1{petabyte} other{petabytes}}' => '{nFormatted} Petabyte',
    '{nFormatted} {n, plural, =1{tebibyte} other{tebibytes}}' => '{nFormatted} TebiByte',
    '{nFormatted} {n, plural, =1{terabyte} other{terabytes}}' => '{nFormatted} Terabyte',
];
