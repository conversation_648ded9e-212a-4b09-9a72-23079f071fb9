<?php
/**
 * Message translations.
 *
 * This file is automatically generated by 'yii message/extract' command.
 * It contains the localizable messages extracted from source code.
 * You may modify this file by translating the extracted messages.
 *
 * Each array element represents the translation (value) of a message (key).
 * If the value is empty, the message is considered as not translated.
 * Messages that no longer need translation will have their translations
 * enclosed between a pair of '@@' marks.
 *
 * Message string can be used with plural forms format. Check i18n section
 * of the guide for details.
 *
 * NOTE: this file must be saved in UTF-8 encoding.
 */
return [
    '(not set)' => '(Veri Yok)',
    'An internal server error occurred.' => 'Bir sunucu hatası oluştu.',
    'Are you sure you want to delete this item?' => 'Bu veriyi silmek istediğinizden emin misiniz?',
    'Delete' => 'Sil',
    'Error' => 'Hata',
    'File upload failed.' => 'Dosya yükleme başarısız.',
    'Home' => 'Anasayfa',
    'Invalid data received for parameter "{param}".' => 'Bu "{param}" parametre için yanlış veri alındı.',
    'Login Required' => 'Üye Girişi Gerekli',
    'Missing required arguments: {params}' => 'Gerekli argüman eksik: {params}',
    'Missing required parameters: {params}' => 'Gerekli parametre eksik: {params}',
    'No' => 'Hayır',
    'No results found.' => 'Sonuç bulunamadı',
    'Only files with these MIME types are allowed: {mimeTypes}.' => 'Sadece bu tip MIME türleri geçerlidir: {mimeTypes}.',
    'Only files with these extensions are allowed: {extensions}.' => 'Sadece bu tip uzantıları olan dosyalar geçerlidir: {extensions}.',
    'Page not found.' => 'Sayfa bulunamadı.',
    'Please fix the following errors:' => 'Lütfen hataları düzeltin:',
    'Please upload a file.' => 'Lütfen bir dosya yükleyin.',
    'Showing <b>{begin, number}-{end, number}</b> of <b>{totalCount, number}</b> {totalCount, plural, one{item} other{items}}.' => '<b>{totalCount, number}</b> {totalCount, plural, one{öğenin} other{öğenin}} <b>{begin, number}-{end, number} arası gösteriliyor.</b>',
    'The file "{file}" is not an image.' => '"{file}" bir resim dosyası değil.',
    'The file "{file}" is too big. Its size cannot exceed {formattedLimit}.' => '"{file}" dosyası çok büyük. Boyutu {formattedLimit} değerinden büyük olamaz.',
    'The file "{file}" is too small. Its size cannot be smaller than {formattedLimit}.' => '"{file}" dosyası çok küçük. Boyutu {formattedLimit} değerinden küçük olamaz.',
    'The format of {attribute} is invalid.' => '{attribute} formatı geçersiz.',
    'The image "{file}" is too large. The height cannot be larger than {limit, number} {limit, plural, one{pixel} other{pixels}}.' => '"{file}" çok büyük. Yükseklik {limit, plural, one{pixel} other{pixels}} değerinden büyük olamaz.',
    'The image "{file}" is too large. The width cannot be larger than {limit, number} {limit, plural, one{pixel} other{pixels}}.' => '"{file}" çok büyük. Genişlik {limit, number} {limit, plural, one{pixel} other{pixels}} değerinden büyük olamaz.',
    'The image "{file}" is too small. The height cannot be smaller than {limit, number} {limit, plural, one{pixel} other{pixels}}.' => '"{file}" çok küçük. Genişlik {limit, number} {limit, plural, one{pixel} other{pixels}} değerinden küçük olamaz.',
    'The image "{file}" is too small. The width cannot be smaller than {limit, number} {limit, plural, one{pixel} other{pixels}}.' => '"{file}" çok küçük. Genişlik {limit, number} {limit, plural, one{pixel} other{pixels}} değerinden küçük olamaz.',
    'The verification code is incorrect.' => 'Doğrulama kodu yanlış.',
    'Total <b>{count, number}</b> {count, plural, one{item} other{items}}.' => 'Toplam <b>{count, number}</b> {count, plural, one{öğe} other{öğe}}.',
    'Unable to verify your data submission.' => 'İlettiğiniz veri doğrulanamadı.',
    'Unknown alias: -{name}' => 'Bilinmeyen rumuz: -{name}',
    'Unknown option: --{name}' => 'Bilinmeyen opsiyon: --{name}',
    'Update' => 'Güncelle',
    'View' => 'Görüntüle',
    'Yes' => 'Evet',
    'You are not allowed to perform this action.' => 'Bu işlemi yapmaya yetkiniz yok.',
    'You can upload at most {limit, number} {limit, plural, one{file} other{files}}.' => 'Sadece {limit, number} {limit, plural, one{dosya} other{# dosya}} yükleyebilirsiniz.',
    'in {delta, plural, =1{a day} other{# days}}' => '{delta, plural, =1{bir gün} other{# gün}} içerisinde',
    'in {delta, plural, =1{a minute} other{# minutes}}' => '{delta, plural, =1{bir dakika} other{# dakika}} içerisinde',
    'in {delta, plural, =1{a month} other{# months}}' => '{delta, plural, =1{bir ay} other{# ay}} içerisinde',
    'in {delta, plural, =1{a second} other{# seconds}}' => '{delta, plural, =1{bir saniye} other{# saniye}} içerisinde',
    'in {delta, plural, =1{a year} other{# years}}' => '{delta, plural, =1{bir yıl} other{# yıl}} içerisinde',
    'in {delta, plural, =1{an hour} other{# hours}}' => '{delta, plural, =1{bir saat} other{# saat}} içerisinde',
    'just now' => 'henüz',
    'the input value' => 'veri giriş değeri',
    '{attribute} "{value}" has already been taken.' => '{attribute} "{value}" daha önce alınmış.',
    '{attribute} cannot be blank.' => '{attribute} boş bırakılamaz.',
    '{attribute} contains wrong subnet mask.' => '{attribute} yanlış alt ağ maskesi içeriyor.',
    '{attribute} is invalid.' => '{attribute} geçersiz.',
    '{attribute} is not a valid URL.' => '{attribute} geçerli bir URL değil.',
    '{attribute} is not a valid email address.' => '{attribute} geçerli bir mail adresi değil.',
    '{attribute} is not in the allowed range.' => '{attribute} izin verilen aralıkta değil.',
    '{attribute} must be "{requiredValue}".' => '{attribute} {requiredValue} olmalı.',
    '{attribute} must be a number.' => '{attribute} sayı olmalı.',
    '{attribute} must be a string.' => '{attribute} harf olmalı.',
    '{attribute} must be a valid IP address.' => '{attribute} geçerli bir IP adresi değil.',
    '{attribute} must be an IP address with specified subnet.' => '{attribute} IP adresi belirtilen alt ağ ile birlikte olmalı.',
    '{attribute} must be an integer.' => '{attribute} rakam olmalı.',
    '{attribute} must be either "{true}" or "{false}".' => '{attribute} "{true}" ya da "{false}" olmalı.',
    '{attribute} must be equal to "{compareValueOrAttribute}".' => '{attribute} "{compareValueOrAttribute}" değerine eşit olmalı.',
    '{attribute} must be greater than "{compareValueOrAttribute}".' => '{attribute} "{compareValueOrAttribute}" değerinden büyük olmalı.',
    '{attribute} must be greater than or equal to "{compareValueOrAttribute}".' => '{attribute} "{compareValueOrAttribute}" değerinden büyük veya eşit olmalı.',
    '{attribute} must be less than "{compareValueOrAttribute}".' => '{attribute} "{compareValueOrAttribute}" değerinden küçük olmalı.',
    '{attribute} must be less than or equal to "{compareValueOrAttribute}".' => '{attribute} "{compareValueOrAttribute}" değerinden küçük veya eşit olmalı.',
    '{attribute} must be no greater than {max}.' => '{attribute} {max} değerinden büyük olamaz.',
    '{attribute} must be no less than {min}.' => '{attribute} {min} değerinden küçük olamaz.',
    '{attribute} must not be a subnet.' => '{attribute} alt ağ olamaz.',
    '{attribute} must not be an IPv4 address.' => '{attribute} IPv4 olamaz.',
    '{attribute} must not be an IPv6 address.' => '{attribute} IPv6 olamaz.',
    '{attribute} must not be equal to "{compareValueOrAttribute}".' => '{attribute} "{compareValueOrAttribute}" değerine eşit olamaz.',
    '{attribute} should contain at least {min, number} {min, plural, one{character} other{characters}}.' => '{attribute} en az {min, number} karakter içermeli.',
    '{attribute} should contain at most {max, number} {max, plural, one{character} other{characters}}.' => '{attribute} en fazla {max, number} karakter içermeli.',
    '{attribute} should contain {length, number} {length, plural, one{character} other{characters}}.' => '{attribute} {length, number} karakter içermeli.',
    '{delta, plural, =1{1 day} other{# days}}' => '{delta, plural, =1{1 gün} other{# gün}}',
    '{delta, plural, =1{1 hour} other{# hours}}' => '{delta, plural, =1{1 saat} other{# saat}}',
    '{delta, plural, =1{1 minute} other{# minutes}}' => '{delta, plural, =1{1 dakika} other{# dakika}}',
    '{delta, plural, =1{1 month} other{# months}}' => '{delta, plural, =1{1 ay} other{# ay}}',
    '{delta, plural, =1{1 second} other{# seconds}}' => '{delta, plural, =1{1 saniye} other{# saniye}}',
    '{delta, plural, =1{1 year} other{# years}}' => '{delta, plural, =1{1 yıl} other{# yıl}}',
    '{delta, plural, =1{a day} other{# days}} ago' => '{delta, plural, =1{bir gün} other{# gün}} önce',
    '{delta, plural, =1{a minute} other{# minutes}} ago' => '{delta, plural, =1{bir dakika} other{# dakika}} önce',
    '{delta, plural, =1{a month} other{# months}} ago' => '{delta, plural, =1{bir ay} other{# ay}} önce',
    '{delta, plural, =1{a second} other{# seconds}} ago' => '{delta, plural, =1{bir saniye} other{# saniye}} önce',
    '{delta, plural, =1{a year} other{# years}} ago' => '{delta, plural, =1{bir yıl} other{# yıl}} önce',
    '{delta, plural, =1{an hour} other{# hours}} ago' => '{delta, plural, =1{bir saat} other{# saat}} önce',
    '{nFormatted} B' => '{nFormatted} B',
    '{nFormatted} GB' => '{nFormatted} GB',
    '{nFormatted} GiB' => '{nFormatted} GiB',
    '{nFormatted} KB' => '{nFormatted} KB',
    '{nFormatted} KiB' => '{nFormatted} KiB',
    '{nFormatted} MB' => '{nFormatted} MB',
    '{nFormatted} MiB' => '{nFormatted} MiB',
    '{nFormatted} PB' => '{nFormatted} PB',
    '{nFormatted} PiB' => '{nFormatted} PiB',
    '{nFormatted} TB' => '{nFormatted} TB',
    '{nFormatted} TiB' => '{nFormatted} TiB',
    '{nFormatted} {n, plural, =1{byte} other{bytes}}' => '{nFormatted} {n, plural, =1{bayt} other{bayt}}',
    '{nFormatted} {n, plural, =1{gibibyte} other{gibibytes}}' => '{nFormatted} {n, plural, =1{gibibayt} other{gibibayt}}',
    '{nFormatted} {n, plural, =1{gigabyte} other{gigabytes}}' => '{nFormatted} {n, plural, =1{gigabayt} other{gigabayt}}',
    '{nFormatted} {n, plural, =1{kibibyte} other{kibibytes}}' => '{nFormatted} {n, plural, =1{kibibayt} other{kibibayt}}',
    '{nFormatted} {n, plural, =1{kilobyte} other{kilobytes}}' => '{nFormatted} {n, plural, =1{kilobayt} other{kilobayt}}',
    '{nFormatted} {n, plural, =1{mebibyte} other{mebibytes}}' => '{nFormatted} {n, plural, =1{mebibayt} other{mebibayt}}',
    '{nFormatted} {n, plural, =1{megabyte} other{megabytes}}' => '{nFormatted} {n, plural, =1{megabayt} other{megabayt}}',
    '{nFormatted} {n, plural, =1{pebibyte} other{pebibytes}}' => '{nFormatted} {n, plural, =1{pebibayt} other{pebibayt}}',
    '{nFormatted} {n, plural, =1{petabyte} other{petabytes}}' => '{nFormatted} {n, plural, =1{petabayt} other{petabayt}}',
    '{nFormatted} {n, plural, =1{tebibyte} other{tebibytes}}' => '{nFormatted} {n, plural, =1{tebibayt} other{tebibayt}}',
    '{nFormatted} {n, plural, =1{terabyte} other{terabytes}}' => '{nFormatted} {n, plural, =1{terabayt} other{terabayt}}',
];
