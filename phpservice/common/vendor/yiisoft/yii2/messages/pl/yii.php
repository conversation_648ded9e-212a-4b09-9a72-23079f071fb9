<?php
/**
 * Message translations.
 *
 * This file is automatically generated by 'yii message' command.
 * It contains the localizable messages extracted from source code.
 * You may modify this file by translating the extracted messages.
 *
 * Each array element represents the translation (value) of a message (key).
 * If the value is empty, the message is considered as not translated.
 * Messages that no longer need translation will have their translations
 * enclosed between a pair of '@@' marks.
 *
 * Message string can be used with plural forms format. Check i18n section
 * of the guide for details.
 *
 * NOTE: this file must be saved in UTF-8 encoding.
 */
return [
    'Powered by {yii}' => 'Powered by {yii}',
    'Yii Framework' => 'Yii Framework',
    '(not set)' => '(brak wartości)',
    'An internal server error occurred.' => 'Wystąpił wewnętrzny błąd serwera.',
    'Are you sure you want to delete this item?' => 'Czy na pewno usunąć ten element?',
    'Delete' => 'Usuń',
    'Error' => 'Błąd',
    'File upload failed.' => 'Wgrywanie pliku nie powiodło się.',
    'Home' => 'Strona domowa',
    'Invalid data received for parameter "{param}".' => 'Otr<PERSON>mano <PERSON> dane dla parametru "{param}".',
    'Login Required' => 'Wymagane zalogowanie się',
    'Missing required arguments: {params}' => 'Brak wymaganych argumentów: {params}',
    'Missing required parameters: {params}' => 'Brak wymaganych parametrów: {params}',
    'No' => 'Nie',
    'No results found.' => 'Brak wyników.',
    'Only files with these MIME types are allowed: {mimeTypes}.' => 'Dozwolone są tylko pliki z następującymi typami MIME:  {mimeTypes}.',
    'Only files with these extensions are allowed: {extensions}.' => 'Dozwolone są tylko pliki z następującymi rozszerzeniami: {extensions}.',
    'Page not found.' => 'Nie odnaleziono strony.',
    'Please fix the following errors:' => 'Proszę poprawić następujące błędy:',
    'Please upload a file.' => 'Proszę wgrać plik.',
    'Showing <b>{begin, number}-{end, number}</b> of <b>{totalCount, number}</b> {totalCount, plural, one{item} other{items}}.' => 'Wyświetlone <b>{begin, number}-{end, number}</b> z <b>{totalCount, number}</b>.',
    'The file "{file}" is not an image.' => 'Plik "{file}" nie jest obrazem.',
    'The file "{file}" is too big. Its size cannot exceed {formattedLimit}.' => 'Plik "{file}" jest zbyt duży. Jego rozmiar nie może przekraczać {formattedLimit}.',
    'The file "{file}" is too small. Its size cannot be smaller than {formattedLimit}.' => 'Plik "{file}" jest za mały. Jego rozmiar nie może być mniejszy niż {formattedLimit}.',
    'The format of {attribute} is invalid.' => 'Format {attribute} jest nieprawidłowy.',
    'The image "{file}" is too large. The height cannot be larger than {limit, number} {limit, plural, one{pixel} other{pixels}}.' => 'Obraz "{file}" jest zbyt duży. Wysokość nie może być większa niż {limit, number} {limit, plural, one{piksela} few{pikseli} many{pikseli} other{piksela}}.',
    'The image "{file}" is too large. The width cannot be larger than {limit, number} {limit, plural, one{pixel} other{pixels}}.' => 'Obraz "{file}" jest zbyt duży. Szerokość nie może być większa niż {limit, number} {limit, plural, one{piksela} few{pikseli} many{pikseli} other{piksela}}.',
    'The image "{file}" is too small. The height cannot be smaller than {limit, number} {limit, plural, one{pixel} other{pixels}}.' => 'Obraz "{file}" jest za mały. Wysokość nie może być mniejsza niż {limit, number} {limit, plural, one{piksela} few{pikseli} many{pikseli} other{piksela}}.',
    'The image "{file}" is too small. The width cannot be smaller than {limit, number} {limit, plural, one{pixel} other{pixels}}.' => 'Obraz "{file}" jest za mały. Szerokość nie może być mniejsza niż {limit, number} {limit, plural, one{piksela} few{pikseli} many{pikseli} other{piksela}}.',
    'The requested view "{name}" was not found.' => 'Żądany widok "{name}" nie został odnaleziony.',
    'The verification code is incorrect.' => 'Kod weryfikacyjny jest nieprawidłowy.',
    'Total <b>{count, number}</b> {count, plural, one{item} other{items}}.' => 'Razem <b>{count, number}</b> {count, plural, one{rekord} few{rekordy} many{rekordów} other{rekordu}}.',
    'Unable to verify your data submission.' => 'Nie udało się zweryfikować przesłanych danych.',
    'Unknown option: --{name}' => 'Nieznana opcja: --{name}',
    'Update' => 'Aktualizuj',
    'View' => 'Zobacz szczegóły',
    'Yes' => 'Tak',
    'You are not allowed to perform this action.' => 'Brak upoważnienia do wykonania tej czynności.',
    'You can upload at most {limit, number} {limit, plural, one{file} other{files}}.' => 'Możliwe wgranie najwyżej {limit, number} {limit, plural, one{pliku} few{plików} many{plików} other{pliku}}.',
    'in {delta, plural, =1{a day} other{# days}}' => 'za {delta, plural, =1{jeden dzień} other{# dni}}',
    'in {delta, plural, =1{a minute} other{# minutes}}' => 'za {delta, plural, =1{minutę} few{# minuty} many{# minut} other{# minuty}}',
    'in {delta, plural, =1{a month} other{# months}}' => 'za {delta, plural, =1{miesiąc} few{# miesiące} many{# miesięcy} other{# miesiąca}}',
    'in {delta, plural, =1{a second} other{# seconds}}' => 'za {delta, plural, =1{sekundę} few{# sekundy} many{# sekund} other{# sekundy}}',
    'in {delta, plural, =1{a year} other{# years}}' => 'za {delta, plural, =1{rok} few{# lata} many{# lat} other{# dni}}',
    'in {delta, plural, =1{an hour} other{# hours}}' => 'za {delta, plural, =1{godzinę} few{# godziny} many{# godzin} other{# godziny}}',
    'just now' => 'przed chwilą',
    'the input value' => 'wartość wejściowa',
    '{attribute} "{value}" has already been taken.' => '{attribute} "{value}" jest już w użyciu.',
    '{attribute} cannot be blank.' => '{attribute} nie może pozostać bez wartości.',
    '{attribute} contains wrong subnet mask.' => '{attribute} posiada złą maskę podsieci.',
    '{attribute} is invalid.' => '{attribute} zawiera nieprawidłową wartość.',
    '{attribute} is not a valid URL.' => '{attribute} nie zawiera prawidłowego adresu URL.',
    '{attribute} is not a valid email address.' => '{attribute} nie zawiera prawidłowego adresu email.',
    '{attribute} is not in the allowed range.' => '{attribute} nie jest w dozwolonym zakresie.',
    '{attribute} must be "{requiredValue}".' => '{attribute} musi mieć wartość "{requiredValue}".',
    '{attribute} must be a number.' => '{attribute} musi być liczbą.',
    '{attribute} must be a string.' => '{attribute} musi być tekstem.',
    '{attribute} must be a valid IP address.' => '{attribute} musi być poprawnym adresem IP.',
    '{attribute} must be an IP address with specified subnet.' => '{attribute} musi być adresem IP w określonej podsieci.',
    '{attribute} must be an integer.' => '{attribute} musi być liczbą całkowitą.',
    '{attribute} must be either "{true}" or "{false}".' => '{attribute} musi mieć wartość "{true}" lub "{false}".',
    '{attribute} must be equal to "{compareValueOrAttribute}".' => '{attribute} musi mieć tę samą wartość co "{compareValueOrAttribute}".',
    '{attribute} must be greater than "{compareValueOrAttribute}".' => '{attribute} musi mieć wartość większą od "{compareValueOrAttribute}".',
    '{attribute} must be greater than or equal to "{compareValueOrAttribute}".' => '{attribute} musi mieć wartość większą lub równą "{compareValueOrAttribute}".',
    '{attribute} must be less than "{compareValueOrAttribute}".' => '{attribute} musi mieć wartość mniejszą od "{compareValueOrAttribute}".',
    '{attribute} must be less than or equal to "{compareValueOrAttribute}".' => '{attribute} musi mieć wartość mniejszą lub równą "{compareValueOrAttribute}".',
    '{attribute} must be no greater than {max}.' => '{attribute} musi wynosić nie więcej niż {max}.',
    '{attribute} must be no less than {min}.' => '{attribute} musi wynosić nie mniej niż {min}.',
    '{attribute} must not be a subnet.' => '{attribute} nie może być podsiecią.',
    '{attribute} must not be an IPv4 address.' => '{attribute} nie może być adresem IPv4.',
    '{attribute} must not be an IPv6 address.' => '{attribute} nie może być adresem IPv6.',
    '{attribute} must not be equal to "{compareValueOrAttribute}".' => '{attribute} musi mieć wartość różną od "{compareValueOrAttribute}".',
    '{attribute} should contain at least {min, number} {min, plural, one{character} other{characters}}.' => '{attribute} powinien zawierać co najmniej {min, number} {min, plural, one{znak} few{znaki} many{znaków} other{znaku}}.',
    '{attribute} should contain at most {max, number} {max, plural, one{character} other{characters}}.' => '{attribute} powinien zawierać nie więcej niż {max, number} {max, plural, one{znak} few{znaki} many{znaków} other{znaku}}.',
    '{attribute} should contain {length, number} {length, plural, one{character} other{characters}}.' => '{attribute} powinien zawierać dokładnie {length, number} {length, plural, one{znak} few{znaki} many{znaków} other{znaku}}.',
    '{delta, plural, =1{1 day} other{# days}}' => '{delta, plural, =1{1 dzień} other{# dni} other{# dnia}}',
    '{delta, plural, =1{1 hour} other{# hours}}' => '{delta, plural, =1{1 godzina} few{# godziny} many{# godzin} other{# godziny}}',
    '{delta, plural, =1{1 minute} other{# minutes}}' => '{delta, plural, =1{1 minuta} few{# minuty} many{# minut} other{# minuty}}',
    '{delta, plural, =1{1 month} other{# months}}' => '{delta, plural, =1{1 miesiąc} few{# miesiące} many{# miesięcy} other{# miesiąca}}',
    '{delta, plural, =1{1 second} other{# seconds}}' => '{delta, plural, =1{1 sekunda} few{# sekundy} many{# sekund} other{# sekundy}}',
    '{delta, plural, =1{1 year} other{# years}}' => '{delta, plural, =1{1 rok} few{# lata} many{# lat} other{# roku}}',
    '{delta, plural, =1{a day} other{# days}} ago' => '{delta, plural, =1{jeden dzień} other{# dni} other{# dnia}} temu',
    '{delta, plural, =1{a minute} other{# minutes}} ago' => '{delta, plural, =1{minutę} few{# minuty} many{# minut} other{# minuty}} temu',
    '{delta, plural, =1{a month} other{# months}} ago' => '{delta, plural, =1{miesiąc} few{# miesiące} many{# miesięcy} other{# miesiąca}} temu',
    '{delta, plural, =1{a second} other{# seconds}} ago' => '{delta, plural, =1{sekundę} few{# sekundy} many{# sekund} other{# sekundy}} temu',
    '{delta, plural, =1{a year} other{# years}} ago' => '{delta, plural, =1{rok} few{# lata} many{# lat} other{# roku}} temu',
    '{delta, plural, =1{an hour} other{# hours}} ago' => '{delta, plural, =1{godzinę} few{# godziny} many{# godzin} other{# godziny}} temu',
    '{nFormatted} B' => '{nFormatted} B',
    '{nFormatted} GB' => '{nFormatted} GB',
    '{nFormatted} GiB' => '{nFormatted} GiB',
    '{nFormatted} KB' => '{nFormatted} KB',
    '{nFormatted} KiB' => '{nFormatted} KiB',
    '{nFormatted} MB' => '{nFormatted} MB',
    '{nFormatted} MiB' => '{nFormatted} MiB',
    '{nFormatted} PB' => '{nFormatted} PB',
    '{nFormatted} PiB' => '{nFormatted} PiB',
    '{nFormatted} TB' => '{nFormatted} TB',
    '{nFormatted} TiB' => '{nFormatted} TiB',
    '{nFormatted} {n, plural, =1{byte} other{bytes}}' => '{nFormatted} {n, plural, =1{bajt} few{bajty} many{bajtów} other{bajta}}',
    '{nFormatted} {n, plural, =1{gibibyte} other{gibibytes}}' => '{nFormatted} {n, plural, =1{gibibajt} few{gigabajty} many{gibiajtów} other{gibiajta}}',
    '{nFormatted} {n, plural, =1{gigabyte} other{gigabytes}}' => '{nFormatted} {n, plural, =1{gigabajt} few{gigabajty} many{gigabajtów} other{gigabaja}}',
    '{nFormatted} {n, plural, =1{kibibyte} other{kibibytes}}' => '{nFormatted} {n, plural, =1{kibibajt} few{kibibajty} many{kibibajtów} other{kibibajtów}}',
    '{nFormatted} {n, plural, =1{kilobyte} other{kilobytes}}' => '{nFormatted} {n, plural, =1{kilobajt} few{kilobajty} many{kilobajtów} other{kilobajtów}}',
    '{nFormatted} {n, plural, =1{mebibyte} other{mebibytes}}' => '{nFormatted} {n, plural, =1{mebibajt} few{mebibajty} many{mebibajtów} other{mebibajta}}',
    '{nFormatted} {n, plural, =1{megabyte} other{megabytes}}' => '{nFormatted} {n, plural, =1{megabajt} few{megabajty} many{megabajtów} other{megabajta}}',
    '{nFormatted} {n, plural, =1{pebibyte} other{pebibytes}}' => '{nFormatted} {n, plural, =1{pebibajt} few{pebibajty} many{pebibajtów} other{pebibajta}}',
    '{nFormatted} {n, plural, =1{petabyte} other{petabytes}}' => '{nFormatted} {n, plural, =1{petabajt} few{petabajty} many{petabajtów} other{petabajta}}',
    '{nFormatted} {n, plural, =1{tebibyte} other{tebibytes}}' => '{nFormatted} {n, plural, =1{tebibajt} few{tebibajty} many{tebibajtów} other{tebibajta}}',
    '{nFormatted} {n, plural, =1{terabyte} other{terabytes}}' => '{nFormatted} {n, plural, =1{terabajt} few{terabajty} many{terabajtów} other{terabajta}}',
];
