<?php
/**
 * Message translations.
 *
 * This file is automatically generated by 'yii message' command.
 * It contains the localizable messages extracted from source code.
 * You may modify this file by translating the extracted messages.
 *
 * Each array element represents the translation (value) of a message (key).
 * If the value is empty, the message is considered as not translated.
 * Messages that no longer need translation will have their translations
 * enclosed between a pair of '@@' marks.
 *
 * Message string can be used with plural forms format. Check i18n section
 * of the guide for details.
 *
 * NOTE: this file must be saved in UTF-8 encoding.
 */
return [
    '(not set)' => '(nen<PERSON>)',
    'An internal server error occurred.' => 'Vyskytla se vnitřní chyba serveru.',
    'Are you sure you want to delete this item?' => 'Opravdu chcete smazat tuto položku?',
    'Delete' => 'Smazat',
    'Error' => 'Chyba',
    'File upload failed.' => 'Nepodařilo se nahrát soubor.',
    'Home' => 'Úvod',
    'Invalid data received for parameter "{param}".' => 'Přijata neplatná data pro parametr "{param}".',
    'Login Required' => 'Je zapotřebí se přihlásit.',
    'Missing required arguments: {params}' => 'Chybí povinné argumenty: {params}',
    'Missing required parameters: {params}' => 'Chybí povinné parametry: {params}',
    'No' => 'Ne',
    'No help for unknown command "{command}".' => 'K neznámému příkazu "{command}" neexistuje nápověda.',
    'No help for unknown sub-command "{command}".' => 'K neznámému pod-příkazu "{command}" neexistuje nápověda.',
    'No results found.' => 'Nenalezeny žádné záznamy.',
    'Only files with these MIME types are allowed: {mimeTypes}.' => 'Povolené jsou pouze soubory následujících MIME typů: {mimeTypes}.',
    'Only files with these extensions are allowed: {extensions}.' => 'Povolené jsou pouze soubory s následujícími příponami: {extensions}.',
    'Page not found.' => 'Stránka nenalezena.',
    'Please fix the following errors:' => 'Opravte prosím následující chyby:',
    'Please upload a file.' => 'Nahrajte prosím soubor.',
    'Showing <b>{begin, number}-{end, number}</b> of <b>{totalCount, number}</b> {totalCount, plural, one{item} other{items}}.' => '{totalCount, plural, one{Zobrazen} few{Zobrazeny} other{Zobrazeno}} <b>{totalCount, plural, one{{begin, number}} other{{begin, number}-{end, number}}}</b> z <b>{totalCount, number}</b> {totalCount, plural, one{záznamu} other{záznamů}}.',
    'The file "{file}" is not an image.' => 'Soubor "{file}" není obrázek.',
    'The file "{file}" is too big. Its size cannot exceed {formattedLimit}.' => 'Soubor "{file}" je příliš velký. Velikost souboru nesmí přesáhnout {formattedLimit}.',
    'The file "{file}" is too small. Its size cannot be smaller than {formattedLimit}.' => 'Soubor "{file}" je příliš malý. Velikost souboru nesmí být méně než {formattedLimit}.',
    'The format of {attribute} is invalid.' => 'Formát údaje {attribute} je neplatný.',
    'The image "{file}" is too large. The height cannot be larger than {limit, number} {limit, plural, one{pixel} other{pixels}}.' => 'Obrázek "{file}" je příliš velký. Výška nesmí přesáhnout {limit, number} {limit, plural, one{pixel} few{pixely} other{pixelů}}.',
    'The image "{file}" is too large. The width cannot be larger than {limit, number} {limit, plural, one{pixel} other{pixels}}.' => 'Obrázek "{file}" je příliš velký. Šířka nesmí přesáhnout {limit, number} {limit, plural, one{pixel} few{pixely} other{pixelů}}.',
    'The image "{file}" is too small. The height cannot be smaller than {limit, number} {limit, plural, one{pixel} other{pixels}}.' => 'Obrázek "{file}" je příliš malý. Výška nesmí být méně než {limit, number} {limit, plural, one{pixel} few{pixely} other{pixelů}}.',
    'The image "{file}" is too small. The width cannot be smaller than {limit, number} {limit, plural, one{pixel} other{pixels}}.' => 'Obrázek "{file}" je příliš malý. Šířka nesmí být méně než {limit, number} {limit, plural, one{pixel} few{pixely} other{pixelů}}.',
    'The requested view "{name}" was not found.' => 'Nebyl nalezen požadovaný náhled "{name}".',
    'The verification code is incorrect.' => 'Nesprávný ověřovací kód.',
    'Total <b>{count, number}</b> {count, plural, one{item} other{items}}.' => 'Celkem <b>{count, number}</b> {count, plural, one{záznam} few{záznamy} other{záznamů}}.',
    'Unable to verify your data submission.' => 'Nebylo možné ověřit odeslané údaje.',
    'Unknown command "{command}".' => 'Neznámý příkaz "{command}".',
    'Unknown option: --{name}' => 'Neznámá volba: --{name}',
    'Update' => 'Upravit',
    'View' => 'Náhled',
    'Yes' => 'Ano',
    'You are not allowed to perform this action.' => 'Nemáte oprávnění pro požadovanou akci.',
    'You can upload at most {limit, number} {limit, plural, one{file} other{files}}.' => 'Nahrát můžete nanejvýš {limit, number} {limit, plural, one{soubor} few{soubory} other{souborů}}.',
    'in {delta, plural, =1{a day} other{# days}}' => 'za {delta, plural, =1{den} few{# dny} other{# dnů}}',
    'in {delta, plural, =1{a minute} other{# minutes}}' => 'za {delta, plural, =1{minutu} few{# minuty} other{# minut}}',
    'in {delta, plural, =1{a month} other{# months}}' => 'za {delta, plural, =1{měsíc} few{# měsíce} other{# měsíců}}',
    'in {delta, plural, =1{a second} other{# seconds}}' => 'za {delta, plural, =1{sekundu} few{# sekundy} other{# sekund}}',
    'in {delta, plural, =1{a year} other{# years}}' => 'za {delta, plural, =1{rok} few{# roky} other{# let}}',
    'in {delta, plural, =1{an hour} other{# hours}}' => 'za {delta, plural, =1{hodinu} few{# hodiny} other{# hodin}}',
    'just now' => 'právě teď',
    'the input value' => 'vstupní hodnota',
    '{attribute} "{value}" has already been taken.' => 'Hodnota "{value}" pro údaj {attribute} již byla dříve použita.',
    '{attribute} cannot be blank.' => 'Je zapotřebí vyplnit {attribute}.',
    '{attribute} is invalid.' => 'Neplatná hodnota pro {attribute}.',
    '{attribute} is not a valid URL.' => '{attribute} není platná URL.',
    '{attribute} is not a valid email address.' => 'Pro {attribute} nebyla použita platná emailová adresa.',
    '{attribute} must be "{requiredValue}".' => '{attribute} musí být "{requiredValue}".',
    '{attribute} must be a number.' => '{attribute} musí být číslo.',
    '{attribute} must be a string.' => '{attribute} musí být řetězec.',
    '{attribute} must be an integer.' => '{attribute} musí být celé číslo.',
    '{attribute} must be either "{true}" or "{false}".' => '{attribute} musí být buď "{true}" nebo "{false}".',
    '{attribute} must be greater than "{compareValue}".' => '{attribute} musí být větší než "{compareValue}".',
    '{attribute} must be greater than or equal to "{compareValue}".' => '{attribute} musí být větší nebo roven "{compareValue}".',
    '{attribute} must be less than "{compareValue}".' => '{attribute} musí být menší než "{compareValue}".',
    '{attribute} must be less than or equal to "{compareValue}".' => '{attribute} musí být menší nebo roven "{compareValue}".',
    '{attribute} must be no greater than {max}.' => '{attribute} nesmí být větší než {max}.',
    '{attribute} must be no less than {min}.' => '{attribute} nesmí být menší než {min}.',
    '{attribute} must be repeated exactly.' => 'Údaj {attribute} je třeba zopakovat přesně.',
    '{attribute} must not be equal to "{compareValue}".' => '{attribute} se nesmí rovnat "{compareValue}".',
    '{attribute} should contain at least {min, number} {min, plural, one{character} other{characters}}.' => '{attribute} musí obsahovat alespoň {min, number} {min, plural, one{znak} few{znaky} other{znaků}}.',
    '{attribute} should contain at most {max, number} {max, plural, one{character} other{characters}}.' => '{attribute} může obsahovat nanejvýš {max, number} {max, plural, one{znak} few{znaky} other{znaků}}.',
    '{attribute} should contain {length, number} {length, plural, one{character} other{characters}}.' => '{attribute}  musí obsahovat {length, number} {length, plural, one{znak} few{znaky} other{znaků}}.',
    '{delta, plural, =1{a day} other{# days}} ago' => '{delta, plural, =1{včera} other{před # dny}}',
    '{delta, plural, =1{a minute} other{# minutes}} ago' => 'před {delta, plural, =1{minutou} other{# minutami}}',
    '{delta, plural, =1{a month} other{# months}} ago' => 'před {delta, plural, =1{měsícem} other{# měsíci}}',
    '{delta, plural, =1{a second} other{# seconds}} ago' => 'před {delta, plural, =1{sekundou} other{# sekundami}}',
    '{delta, plural, =1{a year} other{# years}} ago' => 'před {delta, plural, =1{rokem} other{# lety}}',
    '{delta, plural, =1{an hour} other{# hours}} ago' => 'před {delta, plural, =1{hodinou} other{# hodinami}}',
    '{nFormatted} {n, plural, =1{byte} other{bytes}}' => '{nFormatted} {n, plural, =1{byte} few{byty} other{bytů}}',
    '{nFormatted} {n, plural, =1{gibibyte} other{gibibytes}}' => '{nFormatted} {n, plural, =1{gibibyte} few{gibibyty} other{gibibytů}}',
    '{nFormatted} {n, plural, =1{gigabyte} other{gigabytes}}' => '{nFormatted} {n, plural, =1{gigabyte} few{gigabyty} other{gigabytů}}',
    '{nFormatted} {n, plural, =1{kibibyte} other{kibibytes}}' => '{nFormatted} {n, plural, =1{kibibyte} few{kibibyty} other{kibibytů}}',
    '{nFormatted} {n, plural, =1{kilobyte} other{kilobytes}}' => '{nFormatted} {n, plural, =1{kilobyte} few{kilobyty} other{kilobytů}}',
    '{nFormatted} {n, plural, =1{mebibyte} other{mebibytes}}' => '{nFormatted} {n, plural, =1{mebibyte} few{mebibyty} other{mebibytů}}',
    '{nFormatted} {n, plural, =1{megabyte} other{megabytes}}' => '{nFormatted} {n, plural, =1{megabyte} few{megabyty} other{megabytů}}',
    '{nFormatted} {n, plural, =1{pebibyte} other{pebibytes}}' => '{nFormatted} {n, plural, =1{pebibyte} few{pebibyty} other{pebibytů}}',
    '{nFormatted} {n, plural, =1{petabyte} other{petabytes}}' => '{nFormatted} {n, plural, =1{petabyte} few{petabyty} other{petabytů}}',
    '{nFormatted} {n, plural, =1{tebibyte} other{tebibytes}}' => '{nFormatted} {n, plural, =1{tebibyte} few{tebibyty} other{tebibytů}}',
    '{nFormatted} {n, plural, =1{terabyte} other{terabytes}}' => '{nFormatted} {n, plural, =1{terabyte} few{terabyty} other{terabytů}}',
];
