<?php
/**
 * Message translations.
 *
 * This file is automatically generated by 'yii message/extract' command.
 * It contains the localizable messages extracted from source code.
 * You may modify this file by translating the extracted messages.
 *
 * Each array element represents the translation (value) of a message (key).
 * If the value is empty, the message is considered as not translated.
 * Messages that no longer need translation will have their translations
 * enclosed between a pair of '@@' marks.
 *
 * Message string can be used with plural forms format. Check i18n section
 * of the guide for details.
 *
 * NOTE: this file must be saved in UTF-8 encoding.
 */
return [
    '(not set)' => '(未設定)',
    'An internal server error occurred.' => '内部サーバーエラーが発生しました。',
    'Are you sure you want to delete this item?' => 'このアイテムを削除したいというのは本当ですか?',
    'Delete' => '削除',
    'Error' => 'エラー',
    'File upload failed.' => 'ファイルアップロードに失敗しました。',
    'Home' => 'ホーム',
    'Invalid data received for parameter "{param}".' => 'パラメータ "{param}" に不正なデータを受け取りました。',
    'Login Required' => 'ログインが必要です',
    'Missing required arguments: {params}' => '必要な引数がありません: {params}',
    'Missing required parameters: {params}' => '必要なパラメータがありません: {params}',
    'No' => 'いいえ',
    'No results found.' => '結果が得られませんでした。',
    'Only files with these MIME types are allowed: {mimeTypes}.' => '以下の MIME タイプのファイルだけが許可されています: {mimeTypes}',
    'Only files with these extensions are allowed: {extensions}.' => '次の拡張子を持つファイルだけが許可されています : {extensions}',
    'Page not found.' => 'ページが見つかりません。',
    'Please fix the following errors:' => '次のエラーを修正してください :',
    'Please upload a file.' => 'ファイルをアップロードしてください。',
    'Showing <b>{begin, number}-{end, number}</b> of <b>{totalCount, number}</b> {totalCount, plural, one{item} other{items}}.' => '<b>{totalCount, number}</b> 件中 <b>{begin, number}</b> から <b>{end, number}</b> までを表示しています。',
    'The file "{file}" is not an image.' => 'ファイル "{file}" は画像ではありません。',
    'The file "{file}" is too big. Its size cannot exceed {formattedLimit}.' => 'ファイル "{file}" は大きすぎます。サイズが {formattedLimit} を超えてはいけません。',
    'The file "{file}" is too small. Its size cannot be smaller than {formattedLimit}.' => 'ファイル "{file}" は小さすぎます。サイズが {formattedLimit} より小さくてはいけません。',
    'The format of {attribute} is invalid.' => '{attribute} の書式が正しくありません。',
    'The image "{file}" is too large. The height cannot be larger than {limit, number} {limit, plural, one{pixel} other{pixels}}.' => '画像 "{file}" が大きすぎます。高さが {limit} ピクセルより大きくてはいけません。',
    'The image "{file}" is too large. The width cannot be larger than {limit, number} {limit, plural, one{pixel} other{pixels}}.' => '画像 "{file}" が大きすぎます。幅が {limit} ピクセルより大きくてはいけません。',
    'The image "{file}" is too small. The height cannot be smaller than {limit, number} {limit, plural, one{pixel} other{pixels}}.' => '画像 "{file}" が小さすぎます。高さが {limit} ピクセルより小さくてはいけません。',
    'The image "{file}" is too small. The width cannot be smaller than {limit, number} {limit, plural, one{pixel} other{pixels}}.' => '画像 "{file}" が小さすぎます。幅が {limit} ピクセルより小さくてはいけません。',
    'The requested view "{name}" was not found.' => 'リクエストされたビュー "{name}" が見つかりませんでした。',
    'The verification code is incorrect.' => '検証コードが正しくありません。',
    'Total <b>{count, number}</b> {count, plural, one{item} other{items}}.' => '合計 <b>{count}</b> 件。',
    'Unable to verify your data submission.' => 'データ送信を検証できませんでした。',
    'Unknown option: --{name}' => '不明なオプション: --{name}',
    'Update' => '更新',
    'View' => '閲覧',
    'Yes' => 'はい',
    'You are not allowed to perform this action.' => 'このアクションの実行は許可されていません。',
    'You can upload at most {limit, number} {limit, plural, one{file} other{files}}.' => '最大で {limit, number} 個のファイルをアップロードできます。',
    'in {delta, plural, =1{a day} other{# days}}' => '{delta} 日後',
    'in {delta, plural, =1{a minute} other{# minutes}}' => '{delta} 分後',
    'in {delta, plural, =1{a month} other{# months}}' => '{delta} ヶ月後',
    'in {delta, plural, =1{a second} other{# seconds}}' => '{delta} 秒後',
    'in {delta, plural, =1{a year} other{# years}}' => '{delta} 年後',
    'in {delta, plural, =1{an hour} other{# hours}}' => '{delta} 時間後',
    'just now' => '現在',
    'the input value' => '入力値',
    '{attribute} "{value}" has already been taken.' => '{attribute} で "{value}" は既に使われています。',
    '{attribute} cannot be blank.' => '{attribute} は空白ではいけません。',
    '{attribute} contains wrong subnet mask.' => '{attribute} は無効なサブネットマスクを含んでいます。',
    '{attribute} is invalid.' => '{attribute} は無効です。',
    '{attribute} is not a valid URL.' => '{attribute} は有効な URL 書式ではありません。',
    '{attribute} is not a valid email address.' => '{attribute} は有効なメールアドレス書式ではありません。',
    '{attribute} is not in the allowed range.' => '{attribute} は許容される範囲内にありません。',
    '{attribute} must be "{requiredValue}".' => '{attribute} は "{requiredValue}" である必要があります。',
    '{attribute} must be a number.' => '{attribute} は数字でなければいけません。',
    '{attribute} must be a string.' => '{attribute} は文字列でなければいけません。',
    '{attribute} must be a valid IP address.' => '{attribute} は有効な IP アドレスでなければいけません。',
    '{attribute} must be an IP address with specified subnet.' => '{attribute} は指定されたサブネットを持つ IP アドレスでなければいけません。',
    '{attribute} must be an integer.' => '{attribute} は整数でなければいけません。',
    '{attribute} must be either "{true}" or "{false}".' => '{attribute} は "{true}" か "{false}" のいずれかでなければいけません。',
    '{attribute} must be equal to "{compareValueOrAttribute}".' => '{attribute} は "{compareValueOrAttribute}" と等しくなければいけません。',
    '{attribute} must be greater than "{compareValueOrAttribute}".' => '{attribute} は "{compareValueOrAttribute}" より大きくなければいけません。',
    '{attribute} must be greater than or equal to "{compareValueOrAttribute}".' => '{attribute} は "{compareValueOrAttribute}" と等しいか、または、大きくなければいけません。',
    '{attribute} must be less than "{compareValueOrAttribute}".' => '{attribute} は "{compareValueOrAttribute}" より小さくなければいけません。',
    '{attribute} must be less than or equal to "{compareValueOrAttribute}".' => '{attribute} は "{compareValueOrAttribute}" と等しいか、または、小さくなければいけません。',
    '{attribute} must be no greater than {max}.' => '{attribute} は {max} より大きくてはいけません。',
    '{attribute} must be no less than {min}.' => '{attribute} は {min} より小さくてはいけません。',
    '{attribute} must not be a subnet.' => '{attribute} はサブネットであってはいけません。',
    '{attribute} must not be an IPv4 address.' => '{attribute} は IPv4 アドレスであってはいけません。',
    '{attribute} must not be an IPv6 address.' => '{attribute} は IPv6 アドレスであってはいけません。',
    '{attribute} must not be equal to "{compareValueOrAttribute}".' => '{attribute} は "{compareValueOrAttribute}" と等しくてはいけません。',
    '{attribute} should contain at least {min, number} {min, plural, one{character} other{characters}}.' => '{attribute} は {min} 文字以上でなければいけません。',
    '{attribute} should contain at most {max, number} {max, plural, one{character} other{characters}}.' => '{attribute} は {max} 文字以下でなければいけません。',
    '{attribute} should contain {length, number} {length, plural, one{character} other{characters}}.' => '{attribute} は {length} 文字でなければいけません。',
    '{delta, plural, =1{1 day} other{# days}}' => '{delta} 日間',
    '{delta, plural, =1{1 hour} other{# hours}}' => '{delta} 時間',
    '{delta, plural, =1{1 minute} other{# minutes}}' => '{delta} 分間',
    '{delta, plural, =1{1 month} other{# months}}' => '{delta} ヶ月間',
    '{delta, plural, =1{1 second} other{# seconds}}' => '{delta} 秒間',
    '{delta, plural, =1{1 year} other{# years}}' => '{delta} 年間',
    '{delta, plural, =1{a day} other{# days}} ago' => '{delta} 日前',
    '{delta, plural, =1{a minute} other{# minutes}} ago' => '{delta} 分前',
    '{delta, plural, =1{a month} other{# months}} ago' => '{delta} ヶ月前',
    '{delta, plural, =1{a second} other{# seconds}} ago' => '{delta} 秒前',
    '{delta, plural, =1{a year} other{# years}} ago' => '{delta} 年前',
    '{delta, plural, =1{an hour} other{# hours}} ago' => '{delta} 時間前',
    '{nFormatted} B' => '{nFormatted} B',
    '{nFormatted} GB' => '{nFormatted} GB',
    '{nFormatted} GiB' => '{nFormatted} GiB',
    '{nFormatted} KB' => '{nFormatted} KB',
    '{nFormatted} KiB' => '{nFormatted} KiB',
    '{nFormatted} MB' => '{nFormatted} MB',
    '{nFormatted} MiB' => '{nFormatted} MiB',
    '{nFormatted} PB' => '{nFormatted} PB',
    '{nFormatted} PiB' => '{nFormatted} PiB',
    '{nFormatted} TB' => '{nFormatted} TB',
    '{nFormatted} TiB' => '{nFormatted} TiB',
    '{nFormatted} {n, plural, =1{byte} other{bytes}}' => '{nFormatted} バイト',
    '{nFormatted} {n, plural, =1{gibibyte} other{gibibytes}}' => '{nFormatted} ギビバイト',
    '{nFormatted} {n, plural, =1{gigabyte} other{gigabytes}}' => '{nFormatted} ギガバイト',
    '{nFormatted} {n, plural, =1{kibibyte} other{kibibytes}}' => '{nFormatted} キビバイト',
    '{nFormatted} {n, plural, =1{kilobyte} other{kilobytes}}' => '{nFormatted} キロバイト',
    '{nFormatted} {n, plural, =1{mebibyte} other{mebibytes}}' => '{nFormatted} メビバイト',
    '{nFormatted} {n, plural, =1{megabyte} other{megabytes}}' => '{nFormatted} メガバイト',
    '{nFormatted} {n, plural, =1{pebibyte} other{pebibytes}}' => '{nFormatted} ペビバイト',
    '{nFormatted} {n, plural, =1{petabyte} other{petabytes}}' => '{nFormatted} ペタバイト',
    '{nFormatted} {n, plural, =1{tebibyte} other{tebibytes}}' => '{nFormatted} テビバイト',
    '{nFormatted} {n, plural, =1{terabyte} other{terabytes}}' => '{nFormatted} テラバイト',
];
