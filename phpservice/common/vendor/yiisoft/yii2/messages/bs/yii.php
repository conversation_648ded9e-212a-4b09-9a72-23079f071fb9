<?php
/**
 * Message translations.
 *
 * This file is automatically generated by 'yii message' command.
 * It contains the localizable messages extracted from source code.
 * You may modify this file by translating the extracted messages.
 *
 * Each array element represents the translation (value) of a message (key).
 * If the value is empty, the message is considered as not translated.
 * Messages that no longer need translation will have their translations
 * enclosed between a pair of '@@' marks.
 *
 * Message string can be used with plural forms format. Check i18n section
 * of the guide for details.
 *
 * NOTE: this file must be saved in UTF-8 encoding.
 */
return [
    '(not set)' => '(bez vrijednosti)',
    'An internal server error occurred.' => 'Do<PERSON>lo je do interne greške na serveru.',
    'Are you sure you want to delete this item?' => 'Jeste li sigurni da želite obrisati ovu stavku?',
    'Delete' => 'Obriši',
    'Error' => 'Greška',
    'File upload failed.' => 'Slanje datoteke nije uspjelo.',
    'Home' => 'Početna',
    'Invalid data received for parameter "{param}".' => 'Neispravan podatak dobijen u parametru "{param}"',
    'Login Required' => 'Prijava je obavezna',
    'Missing required arguments: {params}' => 'Nedostaju obavezni argumenti: {params}',
    'Missing required parameters: {params}' => 'Nedostaju obavezni parametri: {params}',
    'No' => 'Ne',
    'No results found.' => 'Nema rezulatata.',
    'Only files with these MIME types are allowed: {mimeTypes}.' => 'Samo datoteke sa sljedećim MIME tipovima su dozvoljeni: {mimeTypes}.',
    'Only files with these extensions are allowed: {extensions}.' => 'Samo datoteke sa sljedećim ekstenzijama su dozvoljeni: {extensions}.',
    'Page not found.' => 'Stranica nije pronađena.',
    'Please fix the following errors:' => 'Molimo ispravite sljedeće greške:',
    'Please upload a file.' => 'Molimo da pošaljete datoteku.',
    'Showing <b>{begin, number}-{end, number}</b> of <b>{totalCount, number}</b> {totalCount, plural, one{item} other{items}}.' => 'Prikazano <b>{begin, number}-{end, number}</b> od <b>{totalCount, number}</b> {totalCount, plural, one{stavke} other{stavki}}.',
    'The file "{file}" is not an image.' => 'Datoteka "{file}" nije slika.',
    'The file "{file}" is too big. Its size cannot exceed {formattedLimit}.' => 'Datoteka "{file}" je prevelika. Veličina ne smije biti veća od {formattedLimit}.',
    'The file "{file}" is too small. Its size cannot be smaller than {formattedLimit}.' => 'Datoteka "{file}" је premala. Veličina ne smije biti manja od {formattedLimit}.',
    'The format of {attribute} is invalid.' => 'Format atributa "{attribute}" je neispravan.',
    'The image "{file}" is too large. The height cannot be larger than {limit, number} {limit, plural, one{pixel} other{pixels}}.' => 'Slika "{file}" je prevelika. Visina ne smije biti veća od {limit, number} {limit, plural, one{piksel} other{piksela}}.',
    'The image "{file}" is too large. The width cannot be larger than {limit, number} {limit, plural, one{pixel} other{pixels}}.' => 'Slika "{file}" je prevelika. Širina ne smije biti veća od {limit, number} {limit, plural, one{piksel} other{piksela}}.',
    'The image "{file}" is too small. The height cannot be smaller than {limit, number} {limit, plural, one{pixel} other{pixels}}.' => 'Slika "{file}" je premala. Visina ne smije biti manja od {limit, number} {limit, plural, one{piksel} other{piksela}}.',
    'The image "{file}" is too small. The width cannot be smaller than {limit, number} {limit, plural, one{pixel} other{pixels}}.' => 'Slika "{file}" je premala. Širina ne smije biti manja od {limit, number} {limit, plural, one{piksel} other{piksela}}.',
    'The requested view "{name}" was not found.' => 'Stranica "{name} nije pronađena."',
    'The verification code is incorrect.' => 'Potvrdni kod nije ispravan.',
    'Total <b>{count, number}</b> {count, plural, one{item} other{items}}.' => 'Ukupno <b>{count, number}</b> {count, plural, one{stavka} other{stavki}}.',
    'Unable to verify your data submission.' => 'Nije moguće provjeriti poslane podatke.',
    'Unknown option: --{name}' => 'Nepoznata opcija: --{name}',
    'Update' => 'Ažurirati',
    'View' => 'Pregled',
    'Yes' => 'Da',
    'You are not allowed to perform this action.' => 'Nemate prava da izvršite ovu akciju.',
    'You can upload at most {limit, number} {limit, plural, one{file} other{files}}.' => 'Možete poslati najviše {limit, number} {limit, plural, one{datoteku} other{datoteka}}.',
    'in {delta, plural, =1{a day} other{# days}}' => 'za {delta, plural, =1{dan} one{# dan} few{# dana} many{# dana} other{# dana}}',
    'in {delta, plural, =1{a minute} other{# minutes}}' => 'za {delta, plural, =1{minut} one{# minut} few{# minuta} many{# minuta} other{# minuta}}',
    'in {delta, plural, =1{a month} other{# months}}' => 'za {delta, plural, =1{mjesec} one{# mjesec} few{# mjeseci} many{# mjeseci} other{# mjeseci}}',
    'in {delta, plural, =1{a second} other{# seconds}}' => 'za {delta, plural, =1{sekundu} one{# sekundu} few{# sekundi} many{# sekundi} other{# sekundi}',
    'in {delta, plural, =1{a year} other{# years}}' => 'za {delta, plural, =1{godinu} one{# godinu} few{# godini} many{# godina} other{# godina}}',
    'in {delta, plural, =1{an hour} other{# hours}}' => 'za {delta, plural, =1{sat} one{# sat} few{# sati} many{# sati} other{# sati}}',
    'just now' => 'upravo sada',
    'the input value' => 'ulazna vrijednost',
    '{attribute} "{value}" has already been taken.' => '{attribute} "{value}" je već zauzet.',
    '{attribute} cannot be blank.' => '{attribute} ne smije biti prazan.',
    '{attribute} is invalid.' => '{attribute} je neispravan.',
    '{attribute} is not a valid URL.' => '{attribute} ne sadrži ispravan URL.',
    '{attribute} is not a valid email address.' => '{attribute} ne sadrži ispravnu email adresu.',
    '{attribute} must be "{requiredValue}".' => '{attribute} mora biti "{requiredValue}".',
    '{attribute} must be a number.' => '{attribute} mora biti broj.',
    '{attribute} must be a string.' => '{attribute} mora biti tekst.',
    '{attribute} must be an integer.' => '{attribute} mora biti cijeli broj.',
    '{attribute} must be either "{true}" or "{false}".' => '{attribute} mora biti "{true}" ili "{false}".',
    '{attribute} must be greater than "{compareValue}".' => '{attribute} mora biti veći od "{compareValue}".',
    '{attribute} must be greater than or equal to "{compareValue}".' => '{attribute} mora biti veći ili jednak od "{compareValue}".',
    '{attribute} must be less than "{compareValue}".' => '{attribute} mora biti manji od "{compareValue}".',
    '{attribute} must be less than or equal to "{compareValue}".' => '{attribute} mora biti manji ili jednak od "{compareValue}".',
    '{attribute} must be no greater than {max}.' => '{attribute} ne smije biti veći od "{max}"',
    '{attribute} must be no less than {min}.' => '{attribute} ne smije biti manji od {min}.',
    '{attribute} must be repeated exactly.' => '{attribute} mora biti ponovljen ispravno.',
    '{attribute} must not be equal to "{compareValue}".' => '{attribute} ne smije biti jednak"{compareValue}".',
    '{attribute} should contain at least {min, number} {min, plural, one{character} other{characters}}.' => '{attribute} treba sadržavati najmanje {min, number} {min, plural, one{znak} other{znakova}}.',
    '{attribute} should contain at most {max, number} {max, plural, one{character} other{characters}}.' => '{attribute} treba sadržavati najviše {max, number} {max, plural, one{znak} other{znakova}}.',
    '{attribute} should contain {length, number} {length, plural, one{character} other{characters}}.' => '{attribute} treba sadržavati {length, number} {length, plural, one{znak} other{znakova}}.',
    '{delta, plural, =1{a day} other{# days}} ago' => 'prije {delta, plural, =1{dan} one{# dan} few{# dana} many{# dana} other{# dana}}',
    '{delta, plural, =1{a minute} other{# minutes}} ago' => 'prije {delta, plural, =1{minut} one{# minut} few{# minuta} many{# minuta} other{# minuta}}',
    '{delta, plural, =1{a month} other{# months}} ago' => 'prije {delta, plural, =1{mjesec} one{# mjesec} few{# mjeseci} many{# mjeseci} other{# mjeseci}}',
    '{delta, plural, =1{a second} other{# seconds}} ago' => 'prije {delta, plural, =1{sekundu} one{# sekundu} few{# sekundi} many{# sekundi} other{# sekundi}',
    '{delta, plural, =1{a year} other{# years}} ago' => 'prije {delta, plural, =1{godinu} one{# godinu} few{# godina} many{# godina} other{# godina}}',
    '{delta, plural, =1{an hour} other{# hours}} ago' => 'prije {delta, plural, =1{sat} one{# sat} few{# sati} many{# sati} other{# sati}}',
    '{nFormatted} B' => '{nFormatted} B',
    '{nFormatted} GB' => '{nFormatted} GB',
    '{nFormatted} GiB' => '{nFormatted} GiB',
    '{nFormatted} KB' => '{nFormatted} KB',
    '{nFormatted} KiB' => '{nFormatted} KiB',
    '{nFormatted} MB' => '{nFormatted} MB',
    '{nFormatted} MiB' => '{nFormatted} MiB',
    '{nFormatted} PB' => '{nFormatted} PB',
    '{nFormatted} PiB' => '{nFormatted} PiB',
    '{nFormatted} TB' => '{nFormatted} TB',
    '{nFormatted} TiB' => '{nFormatted} TiB',
    '{nFormatted} {n, plural, =1{byte} other{bytes}}' => '{nFormatted} {n, plural, =1{bajt} other{bajtova}}',
    '{nFormatted} {n, plural, =1{gibibyte} other{gibibytes}}' => '{nFormatted} {n, plural, =1{gibibajt} other{gibibajta}}',
    '{nFormatted} {n, plural, =1{gigabyte} other{gigabytes}}' => '{nFormatted} {n, plural, =1{gigabajt} other{gigabajta}}',
    '{nFormatted} {n, plural, =1{kibibyte} other{kibibytes}}' => '{nFormatted} {n, plural, =1{kibibajt} other{kibibajta}}',
    '{nFormatted} {n, plural, =1{kilobyte} other{kilobytes}}' => '{nFormatted} {n, plural, =1{kilobajt} other{kilobajta}}',
    '{nFormatted} {n, plural, =1{mebibyte} other{mebibytes}}' => '{nFormatted} {n, plural, =1{mebibajt} other{mebibajta}}',
    '{nFormatted} {n, plural, =1{megabyte} other{megabytes}}' => '{nFormatted} {n, plural, =1{megabajt} other{megabajta}}',
    '{nFormatted} {n, plural, =1{pebibyte} other{pebibytes}}' => '{nFormatted} {n, plural, =1{pebibajt} other{pebibajta}}',
    '{nFormatted} {n, plural, =1{petabyte} other{petabytes}}' => '{nFormatted} {n, plural, =1{petabajt} other{petabajta}}',
    '{nFormatted} {n, plural, =1{tebibyte} other{tebibytes}}' => '{nFormatted} {n, plural, =1{tebibajt} other{tebibajta}}',
    '{nFormatted} {n, plural, =1{terabyte} other{terabytes}}' => '{nFormatted} {n, plural, =1{terabajt} other{terabajta}}',
];