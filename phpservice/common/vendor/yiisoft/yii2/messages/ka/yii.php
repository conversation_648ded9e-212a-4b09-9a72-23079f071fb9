<?php
/**
 * Message translations.
 *
 * This file is automatically generated by 'yii message' command.
 * It contains the localizable messages extracted from source code.
 * You may modify this file by translating the extracted messages.
 *
 * Each array element represents the translation (value) of a message (key).
 * If the value is empty, the message is considered as not translated.
 * Messages that no longer need translation will have their translations
 * enclosed between a pair of '@@' marks.
 *
 * Message string can be used with plural forms format. Check i18n section
 * of the guide for details.
 *
 * NOTE: this file must be saved in UTF-8 encoding.
 */
return [
    '(not set)' => '(არ არის მითითებული)',
    'An internal server error occurred.' => 'წარმოიშვა სერვერის შიდა შეცდომა.',
    'Are you sure you want to delete this item?' => 'დარწმუნებული ხართ, რომ გინდათ ამ ელემენტის წაშლა?',
    'Delete' => 'წაშლა',
    'Error' => 'შეცდომა',
    'File upload failed.' => 'ფაილის ჩამოტვირთვა ვერ მოხერხდა.',
    'Home' => 'მთავარი',
    'Invalid data received for parameter "{param}".' => 'პარამეტრის "{param}" არასწორი მნიშვნელობა.',
    'Login Required' => 'საჭიროა შესვლა.',
    'Missing required arguments: {params}' => 'არ არსებობენ აუცილებელი პარამეტრები: {params}',
    'Missing required parameters: {params}' => 'არ არსებობენ აუცილებელი პარამეტრები: {params}',
    'No' => 'არა',
    'No results found.' => 'არაფერი მოიძებნა.',
    'Only files with these MIME types are allowed: {mimeTypes}.' => 'ნებადართულია ფაილების ჩამოტვირთვა მხოლოდ შემდეგი MIME-ტიპებით: {mimeTypes}.',
    'Only files with these extensions are allowed: {extensions}.' => 'ნებადართულია ფაილების ჩამოტვირთვა მხოლოდ შემდეგი გაფართოებებით: {extensions}.',
    'Page not found.' => 'გვერდი ვერ მოიძებნა.',
    'Please fix the following errors:' => 'შეასწორეთ შემდეგი შეცდომები:',
    'Please upload a file.' => 'ჩამოტვირთეთ ფაილი.',
    'Showing <b>{begin, number}-{end, number}</b> of <b>{totalCount, number}</b> {totalCount, plural, one{item} other{items}}.' => 'ნაჩვენებია ჩანაწერები <b>{begin, number}-{end, number}</b> из <b>{totalCount, number}</b>.',
    'The file "{file}" is not an image.' => 'ფაილი «{file}» არ წარმოადგენს გამოსახულებას.',
    'The file "{file}" is too big. Its size cannot exceed {formattedLimit}.' => 'ფაილი «{file}» ძალიან დიდია. ზომა არ უნდა აღემატებოდეს {formattedLimit}.',
    'The file "{file}" is too small. Its size cannot be smaller than {formattedLimit}.' => 'ფაილი «{file}» ძალიან პატარაა. ზომა უნდა აღემატებოდეს {formattedLimit}.',
    'The format of {attribute} is invalid.' => 'მნიშვნელობის «{attribute}» არასწორი ფორმატი.',
    'The image "{file}" is too large. The height cannot be larger than {limit, number} {limit, plural, one{pixel} other{pixels}}.' => 'ფაილი «{file}» ძალიან დიდია. სიმაღლე არ უნდა აღემატებოდეს {limit, number} {limit, plural, one{პიქსელს} few{პიქსელს} many{პიქსელს} other{პიქსელს}}.',
    'The image "{file}" is too large. The width cannot be larger than {limit, number} {limit, plural, one{pixel} other{pixels}}.' => 'ფაილი «{file}» ძალიან დიდია. სიგანე არ უნდა აღემატებოდეს {limit, number} {limit, plural, one{პიქსელს} few{პიქსელს} many{პიქსელს} other{პიქსელს}}.',
    'The image "{file}" is too small. The height cannot be smaller than {limit, number} {limit, plural, one{pixel} other{pixels}}.' => 'ფაილი «{file}» ძალიან პატარაა. სიმაღლე უნდა აღემატებოდეს {limit, number} {limit, plural, one{პიქსელს} few{პიქსელს} many{პიქსელს} other{პიქსელს}}.',
    'The image "{file}" is too small. The width cannot be smaller than {limit, number} {limit, plural, one{pixel} other{pixels}}.' => 'ფაილი «{file}» ძალიან პატარაა. სიგანე უნდა აღემატებოდეს {limit, number} {limit, plural, one{პიქსელს} few{პიქსელს} many{პიქსელს} other{პიქსელს}}.',
    'The requested view "{name}" was not found.' => 'მოთხოვნილი წარმოდგენის "{name}" ფაილი ვერ მოიძებნა.',
    'The verification code is incorrect.' => 'არასწორი შემამოწმებელი კოდი.',
    'Total <b>{count, number}</b> {count, plural, one{item} other{items}}.' => 'სულ <b>{count, number}</b> {count, plural, one{ჩანაწერი} few{ჩანაწერი} many{ჩანაწერი}} other{ჩანაწერი}}.',
    'Unable to verify your data submission.' => 'ვერ მოხერხდა გადაცემული მონაცემების შემოწმება.',
    'Unknown option: --{name}' => 'უცნობი ოფცია: --{name}',
    'Update' => 'რედაქტირება',
    'View' => 'ნახვა',
    'Yes' => 'დიახ',
    'You are not allowed to perform this action.' => 'თქვენ არ გაქვთ მოცემული ქმედების შესრულების ნებართვა.',
    'You can upload at most {limit, number} {limit, plural, one{file} other{files}}.' => 'თქვენ არ შეგიძლიათ ჩამოტვირთოთ {limit, number}-ზე მეტი {limit, plural, one{ფაილი} few{ფაილი} many{ფაილი} other{ფაილი}}.',
    'in {delta, plural, =1{a day} other{# days}}' => '{delta, plural, =1{დღის} one{# დღის} few{# დღის} many{# დღის} other{# დღის}} შემდეგ',
    'in {delta, plural, =1{a minute} other{# minutes}}' => '{delta, plural, =1{წუთის} one{# წუთის} few{# წუთის} many{# წუთის} other{# წუთის}} შემდეგ',
    'in {delta, plural, =1{a month} other{# months}}' => '{delta, plural, =1{თვის} one{# თვის} few{# თვის} many{# თვის} other{# თვის}} შემდეგ',
    'in {delta, plural, =1{a second} other{# seconds}}' => '{delta, plural, =1{წამის} one{# წამის} few{# წამის} many{# წამის} other{# წამის}} შემდეგ',
    'in {delta, plural, =1{a year} other{# years}}' => '{delta, plural, =1{წლის} one{# წლის} few{# წლის} many{# წლის} other{# წლის}} შემდეგ',
    'in {delta, plural, =1{an hour} other{# hours}}' => '{delta, plural, =1{საათის} one{# საათის} few{# საათის} many{# საათის} other{# საათის}} შემდეგ',
    'just now' => 'ახლავე',
    'the input value' => 'შეყვანილი მნიშვნელობა',
    '{attribute} "{value}" has already been taken.' => '{attribute} «{value}» უკვე დაკავებულია.',
    '{attribute} cannot be blank.' => 'საჭიროა შევსება «{attribute}».',
    '{attribute} is invalid.' => 'მნიშვნელობა «{attribute}» არასწორია.',
    '{attribute} is not a valid URL.' => 'მნიშვნელობა «{attribute}» არ წარმოადგენს სწორ URL-ს.',
    '{attribute} is not a valid email address.' => 'მნიშვნელობა «{attribute}» არ წარმოადგენს სწორ email მისამართს.',
    '{attribute} must be "{requiredValue}".' => 'მნიშვნელობა «{attribute}» უნდა იყოს «{requiredValue}-ს ტოლი».',
    '{attribute} must be a number.' => 'მნიშვნელობა «{attribute}» უნდა იყოს რიცხვი.',
    '{attribute} must be a string.' => 'მნიშვნელობა «{attribute}» უნდა იყოს სტრიქონი.',
    '{attribute} must be an integer.' => 'მნიშვნელობა «{attribute}» უნდა იყოს მთელი რიცხვი.',
    '{attribute} must be either "{true}" or "{false}".' => 'მნიშვნელობა «{attribute}» უნდა იყოს «{true}»-ს ან «{false}»-ს ტოლი.',
    '{attribute} must be greater than "{compareValue}".' => 'მნიშვნელობა «{attribute}» უნდა იყოს «{compareValue}» მნიშვნელობაზე მეტი.',
    '{attribute} must be greater than or equal to "{compareValue}".' => 'მნიშვნელობა «{attribute}» უნდა იყოს «{compareValue}» მნიშვნელობაზე მეტი ან ტოლი.',
    '{attribute} must be less than "{compareValue}".' => 'მნიშვნელობა «{attribute}» უნდა იყოს «{compareValue}» მნიშვნელობაზე ნაკლები.',
    '{attribute} must be less than or equal to "{compareValue}".' => 'მნიშვნელობა «{attribute}» უნდა იყოს «{compareValue}» მნიშვნელობაზე ნაკლები ან ტოლი.',
    '{attribute} must be no greater than {max}.' => 'მნიშვნელობა «{attribute}» არ უნდა აღემატებოდეს {max}.',
    '{attribute} must be no less than {min}.' => 'მნიშვნელობა «{attribute}» უნდა იყოს არანაკლები {min}.',
    '{attribute} must be repeated exactly.' => 'მნიშვნელობა «{attribute}» უნდა განმეორდეს ზუსტად.',
    '{attribute} must not be equal to "{compareValue}".' => 'მნიშვნელობა «{attribute}» არ უნდა იყოს «{compareValue}»-ს ტოლი.',
    '{attribute} should contain at least {min, number} {min, plural, one{character} other{characters}}.' => 'მნიშვნელობა «{attribute}» უნდა შეიცავდეს მინიმუმ {min, number} {min, plural, one{სიმბოლს} few{სიმბოლს} many{სიმბოლს} other{სიმბოლს}}.',
    '{attribute} should contain at most {max, number} {max, plural, one{character} other{characters}}.' => 'მნიშვნელობა «{attribute}» უნდა შეიცავდეს მაქსიმუმ {max, number} {max, plural, one{სიმბოლს} few{სიმბოლს} many{სიმბოლს} other{სიმბოლს}}.',
    '{attribute} should contain {length, number} {length, plural, one{character} other{characters}}.' => 'მნიშვნელობა «{attribute}» უნდა შეიცავდეს {length, number} {length, plural, one{სიმბოლს} few{სიმბოლს} many{სიმბოლს} other{სიმბოლს}}.',
    '{delta, plural, =1{a day} other{# days}} ago' => '{delta, plural, =1{დღის} one{# დღის} few{# დღის} many{# დღის} other{# დღის}} უკან',
    '{delta, plural, =1{a minute} other{# minutes}} ago' => '{delta, plural, =1{წუთის} one{# წუთის} few{# წუთის} many{# წუთის} other{# წუთის}} უკან',
    '{delta, plural, =1{a month} other{# months}} ago' => '{delta, plural, =1{თვის} one{# თვის} few{# თვის} many{# თვის} other{# თვის}} უკან',
    '{delta, plural, =1{a second} other{# seconds}} ago' => '{delta, plural, =1{წამის} one{# წამის} few{# წამის} many{# წამის} other{# წამის}} უკან',
    '{delta, plural, =1{a year} other{# years}} ago' => '{delta, plural, =1{წლის} one{# წლის} few{# წლის} many{# წლის} other{# წლის}} უკან',
    '{delta, plural, =1{an hour} other{# hours}} ago' => '{delta, plural, =1{საათის} one{# საათის} few{# საათის} many{# საათის} other{# საათის}} უკან',
    '{nFormatted} B' => '{nFormatted} ბ',
    '{nFormatted} GB' => '{nFormatted} გბ',
    '{nFormatted} GiB' => '{nFormatted} გიბ',
    '{nFormatted} KB' => '{nFormatted} კბ',
    '{nFormatted} KiB' => '{nFormatted} კიბ',
    '{nFormatted} MB' => '{nFormatted} მბ',
    '{nFormatted} MiB' => '{nFormatted} მიბ',
    '{nFormatted} PB' => '{nFormatted} პბ',
    '{nFormatted} PiB' => '{nFormatted} პიბ',
    '{nFormatted} TB' => '{nFormatted} ტბ',
    '{nFormatted} TiB' => '{nFormatted} ტიბ',
    '{nFormatted} {n, plural, =1{byte} other{bytes}}' => '{nFormatted} {n, plural, one{ბაიტი} few{ბაიტს} many{ბაიტი} other{ბაიტს}}',
    '{nFormatted} {n, plural, =1{gibibyte} other{gibibytes}}' => '{nFormatted} {n, plural, one{გიბიბაიტი} few{გიბიბაიტს} many{გიბიბაიტი} other{გიბიბაიტს}}',
    '{nFormatted} {n, plural, =1{gigabyte} other{gigabytes}}' => '{nFormatted} {n, plural, one{გიგაბაიტი} few{გიგაბაიტს} many{გიგაბაიტი} other{გიგაბაიტს}}',
    '{nFormatted} {n, plural, =1{kibibyte} other{kibibytes}}' => '{nFormatted} {n, plural, one{კიბიბაიტი} few{კიბიბაიტს} many{კიბიბაიტი} other{კიბიბაიტს}}',
    '{nFormatted} {n, plural, =1{kilobyte} other{kilobytes}}' => '{nFormatted} {n, plural, one{კილობაიტი} few{კილობაიტს} many{კილობაიტი} other{კილობაიტს}}',
    '{nFormatted} {n, plural, =1{mebibyte} other{mebibytes}}' => '{nFormatted} {n, plural, one{მებიბაიტი} few{მებიბაიტს} many{მებიბაიტი} other{მებიბაიტს}}',
    '{nFormatted} {n, plural, =1{megabyte} other{megabytes}}' => '{nFormatted} {n, plural, one{მეგაბაიტი} few{მეგაბაიტს} many{მეგაბაიტი} other{მეგაბაიტს}}',
    '{nFormatted} {n, plural, =1{pebibyte} other{pebibytes}}' => '{nFormatted} {n, plural, one{პებიბაიტი} few{პებიბაიტს} many{პებიბაიტი} other{პებიბაიტს}}',
    '{nFormatted} {n, plural, =1{petabyte} other{petabytes}}' => '{nFormatted} {n, plural, one{პეტაბაიტი} few{პეტაბაიტს} many{პეტაბაიტი} other{პეტაბაიტს}}',
    '{nFormatted} {n, plural, =1{tebibyte} other{tebibytes}}' => '{nFormatted} {n, plural, one{ტებიბაიტი} few{ტებიბაიტს} many{ტებიბაიტი} other{ტებიბაიტს}}',
    '{nFormatted} {n, plural, =1{terabyte} other{terabytes}}' => '{nFormatted} {n, plural, one{ტერაბაიტი} few{ტერაბაიტს} many{ტერაბაიტი} other{ტერაბაიტს}}',
];
