<?php
/**
 * Message translations.
 *
 * This file is automatically generated by 'yii message' command.
 * It contains the localizable messages extracted from source code.
 * You may modify this file by translating the extracted messages.
 *
 * Each array element represents the translation (value) of a message (key).
 * If the value is empty, the message is considered as not translated.
 * Messages that no longer need translation will have their translations
 * enclosed between a pair of '@@' marks.
 *
 * Message string can be used with plural forms format. Check i18n section
 * of the guide for details.
 *
 * NOTE: this file must be saved in UTF-8 encoding.
 */
return [
    '{delta, plural, =1{1 day} other{# days}}' => '{delta, plural, =1{1 jour} other{# jours}}',
    '{delta, plural, =1{1 hour} other{# hours}}' => '{delta, plural, =1{1 heure} other{# heures}}',
    '{delta, plural, =1{1 minute} other{# minutes}}' => '{delta, plural, =1{1 minute} other{# minutes}}',
    '{delta, plural, =1{1 month} other{# months}}' => 'mois',
    '{delta, plural, =1{1 second} other{# seconds}}' => '{delta, plural, =1{1 seconde} other{# secondes}}',
    '{delta, plural, =1{1 year} other{# years}}' => '{delta, plural, =1{1 année} other{# années}}',
    '(not set)' => '(non défini)',
    'An internal server error occurred.' => 'Une erreur de serveur interne s\'est produite.',
    'Are you sure you want to delete this item?' => 'Êtes-vous sûr de vouloir supprimer cet élément ?',
    'Delete' => 'Supprimer',
    'Error' => 'Erreur',
    'File upload failed.' => 'Le téléchargement du fichier a échoué.',
    'Home' => 'Accueil',
    'Invalid data received for parameter "{param}".' => 'Données non valides reçues pour le paramètre « {param} ».',
    'Login Required' => 'Identifiant requis',
    'Missing required arguments: {params}' => 'Arguments manquants requis : {params}',
    'Missing required parameters: {params}' => 'Paramètres manquants requis : {params}',
    'No' => 'Non',
    'No results found.' => 'Aucun résultat trouvé.',
    'Only files with these MIME types are allowed: {mimeTypes}.' => 'Seulement les fichiers ayant ces types MIME sont autorisés : {mimeTypes}.',
    'Only files with these extensions are allowed: {extensions}.' => 'Les extensions de fichiers autorisées sont : {extensions}.',
    'Page not found.' => 'Page non trouvée.',
    'Please fix the following errors:' => 'Veuillez vérifier les erreurs suivantes :',
    'Please upload a file.' => 'Veuillez télécharger un fichier.',
    'Showing <b>{begin, number}-{end, number}</b> of <b>{totalCount, number}</b> {totalCount, plural, one{item} other{items}}.' => 'Affichage de <b>{begin, number}-{end, number}</b> sur <b>{totalCount, number}</b> {totalCount, plural, one{élément} other{éléments}}.',
    'The file "{file}" is not an image.' => 'Le fichier « {file} » n\'est pas une image.',
    'The file "{file}" is too big. Its size cannot exceed {formattedLimit}.' => 'Le fichier « {file} » est trop gros. Sa taille ne peut dépasser {formattedLimit}.',
    'The file "{file}" is too small. Its size cannot be smaller than {formattedLimit}.' => 'Le fichier « {file} » est trop petit. Sa taille ne peut être inférieure à {formattedLimit}.',
    'The format of {attribute} is invalid.' => 'Le format de {attribute} est invalide',
    'The image "{file}" is too large. The height cannot be larger than {limit, number} {limit, plural, one{pixel} other{pixels}}.' => 'L\'image « {file} » est trop grande. La hauteur ne peut être supérieure à {limit, number} {limit, plural, one{pixel} other{pixels}}.',
    'The image "{file}" is too large. The width cannot be larger than {limit, number} {limit, plural, one{pixel} other{pixels}}.' => 'L\'image « {file} » est trop large. La largeur ne peut être supérieure à {limit, number} {limit, plural, one{pixel} other{pixels}}.',
    'The image "{file}" is too small. The height cannot be smaller than {limit, number} {limit, plural, one{pixel} other{pixels}}.' => 'L\'image « {file} » est trop petite. La hauteur ne peut être inférieure à {limit, number} {limit, plural, one{pixel} other{pixels}}.',
    'The image "{file}" is too small. The width cannot be smaller than {limit, number} {limit, plural, one{pixel} other{pixels}}.' => 'L\'image « {file} » est trop petite. La largeur ne peut être inférieure à {limit, number} {limit, plural, one{pixel} other{pixels}}.',
    'The requested view "{name}" was not found.' => 'La vue demandée « {name} » n\'a pas été trouvée.',
    'The verification code is incorrect.' => 'Le code de vérification est incorrect.',
    'Total <b>{count, number}</b> {count, plural, one{item} other{items}}.' => 'Total <b>{count, number}</b> {count, plural, one{élément} other{éléments}}.',
    'Unable to verify your data submission.' => 'Impossible de vérifier votre envoi de données.',
    'Unknown option: --{name}' => 'Option inconnue : --{name}',
    'Update' => 'Modifier',
    'View' => 'Voir',
    'Yes' => 'Oui',
    'You are not allowed to perform this action.' => 'Vous n\'êtes pas autorisé à effectuer cette action.',
    'You can upload at most {limit, number} {limit, plural, one{file} other{files}}.' => 'Vous pouvez télécharger au maximum {limit, number} {limit, plural, one{fichier} other{fichiers}}.',
    'in {delta, plural, =1{a day} other{# days}}' => 'dans {delta, plural, =1{un jour} other{# jours}}',
    'in {delta, plural, =1{a minute} other{# minutes}}' => 'dans {delta, plural, =1{une minute} other{# minutes}}',
    'in {delta, plural, =1{a month} other{# months}}' => 'dans {delta, plural, =1{un mois} other{# mois}}',
    'in {delta, plural, =1{a second} other{# seconds}}' => 'dans {delta, plural, =1{une seconde} other{# secondes}}',
    'in {delta, plural, =1{a year} other{# years}}' => 'dans {delta, plural, =1{un an} other{# ans}}',
    'in {delta, plural, =1{an hour} other{# hours}}' => 'dans {delta, plural, =1{une heure} other{# heures}}',
    'just now' => 'à l\'instant',
    'the input value' => 'la valeur d\'entrée',
    '{attribute} "{value}" has already been taken.' => '{attribute} « {value} » a déjà été pris.',
    '{attribute} cannot be blank.' => '{attribute} ne peut être vide.',
    '{attribute} contains wrong subnet mask.' => '{attribute} contient un masque de sous-réseau non valide.',
    '{attribute} is invalid.' => '{attribute} est invalide.',
    '{attribute} is not a valid URL.' => '{attribute} n\'est pas une URL valide.',
    '{attribute} is not a valid email address.' => '{attribute} n\'est pas une adresse email valide.',
    '{attribute} is not in the allowed range.' => '{attribute} n\'est pas dans la plage autorisée.',
    '{attribute} must be "{requiredValue}".' => '{attribute} doit êre « {requiredValue} ».',
    '{attribute} must be a number.' => '{attribute} doit être un nombre.',
    '{attribute} must be a string.' => '{attribute} doit être au format texte.',
    '{attribute} must be a valid IP address.' => '{attribute} doit être une adresse IP valide.',
    '{attribute} must be an IP address with specified subnet.' => '{attribute} doit être une adresse IP avec un sous-réseau.',
    '{attribute} must be an integer.' => '{attribute} doit être un entier.',
    '{attribute} must be either "{true}" or "{false}".' => '{attribute} doit être soit « {true} » soit « {false} ».',
    '{attribute} must be greater than "{compareValueOrAttribute}".' => '{attribute} doit être supérieur à « {compareValueOrAttribute} ».',
    '{attribute} must be greater than or equal to "{compareValueOrAttribute}".' => '{attribute} doit être supérieur ou égal à « {compareValueOrAttribute} ».',
    '{attribute} must be less than "{compareValueOrAttribute}".' => '{attribute} doit être inférieur à « {compareValueOrAttribute} ».',
    '{attribute} must be less than or equal to "{compareValueOrAttribute}".' => '{attribute} doit être inférieur ou égal à « {compareValueOrAttribute} ».',
    '{attribute} must be no greater than {max}.' => '{attribute} ne doit pas être supérieur à {max}.',
    '{attribute} must be no less than {min}.' => '{attribute} ne doit pas être inférieur à {min}.',
    '{attribute} must be repeated exactly.' => '{attribute} doit être identique.',
    '{attribute} must not be a subnet.' => '{attribute} ne doit pas être un sous-réseau.',
    '{attribute} must not be an IPv4 address.' => '{attribute} ne doit pas être une adresse IPv4.',
    '{attribute} must not be an IPv6 address.' => '{attribute} ne doit pas être une adresse IPv6.',
    '{attribute} must not be equal to "{compareValueOrAttribute}".' => '{attribute} ne doit pas être égal à « {compareValueOrAttribute} ».',
    '{attribute} should contain at least {min, number} {min, plural, one{character} other{characters}}.' => '{attribute} doit comporter au moins {min, number} {min, plural, one{caractère} other{caractères}}.',
    '{attribute} should contain at most {max, number} {max, plural, one{character} other{characters}}.' => '{attribute} doit comporter au plus {max, number} {max, plural, one{caractère} other{caractères}}.',
    '{attribute} should contain {length, number} {length, plural, one{character} other{characters}}.' => '{attribute} doit comporter {length, number} {length, plural, one{caractère} other{caractères}}.',
    '{delta, plural, =1{a day} other{# days}} ago' => 'il y a {delta, plural, =1{un jour} other{# jours}}',
    '{delta, plural, =1{a minute} other{# minutes}} ago' => 'il y a {delta, plural, =1{une minute} other{# minutes}}',
    '{delta, plural, =1{a month} other{# months}} ago' => 'il y a {delta, plural, =1{un mois} other{# mois}}',
    '{delta, plural, =1{a second} other{# seconds}} ago' => 'il y a {delta, plural, =1{une seconde} other{# secondes}}',
    '{delta, plural, =1{a year} other{# years}} ago' => 'il y a {delta, plural, =1{un an} other{# ans}}',
    '{delta, plural, =1{an hour} other{# hours}} ago' => 'il y a {delta, plural, =1{une heure} other{# heures}}',
    '{nFormatted} B' => '{nFormatted} o',
    '{nFormatted} GB' => '{nFormatted} Go',
    '{nFormatted} GiB' => '{nFormatted} Gio',
    '{nFormatted} KB' => '{nFormatted} Ko',
    '{nFormatted} KiB' => '{nFormatted} Kio',
    '{nFormatted} MB' => '{nFormatted} Mo',
    '{nFormatted} MiB' => '{nFormatted} Mio',
    '{nFormatted} PB' => '{nFormatted} Po',
    '{nFormatted} PiB' => '{nFormatted} Pio',
    '{nFormatted} TB' => '{nFormatted} To',
    '{nFormatted} TiB' => '{nFormatted} Tio',
    '{nFormatted} {n, plural, =1{byte} other{bytes}}' => '{nFormatted} {n, plural, =1{octet} other{octets}}',
    '{nFormatted} {n, plural, =1{gibibyte} other{gibibytes}}' => '{nFormatted} {n, plural, =1{# gigaoctet} other{# gigaoctets}}',
    '{nFormatted} {n, plural, =1{gigabyte} other{gigabytes}}' => '{nFormatted} {n, plural, =1{gibioctet} other{gibioctets}}',
    '{nFormatted} {n, plural, =1{kibibyte} other{kibibytes}}' => '{nFormatted} {n, plural, =1{kibioctet} other{kibioctets}}',
    '{nFormatted} {n, plural, =1{kilobyte} other{kilobytes}}' => '{nFormatted} {n, plural, =1{# kilooctet} other{# kilooctets}}',
    '{nFormatted} {n, plural, =1{mebibyte} other{mebibytes}}' => '{nFormatted} {n, plural, =1{mebioctet} other{mebioctets}}',
    '{nFormatted} {n, plural, =1{megabyte} other{megabytes}}' => '{nFormatted} {n, plural, =1{# megaoctet} other{# megaoctets}}',
    '{nFormatted} {n, plural, =1{pebibyte} other{pebibytes}}' => '{nFormatted} {n, plural, =1{pebioctet} other{pebioctets}}',
    '{nFormatted} {n, plural, =1{petabyte} other{petabytes}}' => '{nFormatted} {n, plural, =1{# petaoctet} other{# petaoctets}}',
    '{nFormatted} {n, plural, =1{tebibyte} other{tebibytes}}' => '{nFormatted} {n, plural, =1{# teraoctet} other{# teraoctets}}',
    '{nFormatted} {n, plural, =1{terabyte} other{terabytes}}' => '{nFormatted} {n, plural, =1{# teraoctet} other{# teraoctets}}',
];
