<?php
/**
 * Message translations.
 *
 * This file is automatically generated by 'yii message/extract' command.
 * It contains the localizable messages extracted from source code.
 * You may modify this file by translating the extracted messages.
 *
 * Each array element represents the translation (value) of a message (key).
 * If the value is empty, the message is considered as not translated.
 * Messages that no longer need translation will have their translations
 * enclosed between a pair of '@@' marks.
 *
 * Message string can be used with plural forms format. Check i18n section
 * of the guide for details.
 *
 * NOTE: this file must be saved in UTF-8 encoding.
 */
return [
    '{attribute} contains wrong subnet mask.' => '{attribute} obsahuje neplatnú masku podsiete.',
    '(not set)' => '(nie je nastavené)',
    'An internal server error occurred.' => 'Vyskytla sa interná chyba servera.',
    'Are you sure you want to delete this item?' => 'Skutočne chcete odstrániť tento záznam?',
    'Delete' => 'Zmazať',
    'Error' => 'Chyba',
    'File upload failed.' => 'Súbor sa nepodarilo nahrať.',
    'Home' => 'Úvod',
    'Invalid data received for parameter "{param}".' => 'Neplatné údaje pre parameter "{param}".',
    'Login Required' => 'Je potrebné sa prihlásiť',
    'Missing required arguments: {params}' => 'Chýbajú povinné argumenty: {params}',
    'Missing required parameters: {params}' => 'Chýbajú povinné parametre: {params}',
    'No' => 'Nie',
    'No results found.' => 'Neboli nájdené žiadne záznamy.',
    'Only files with these MIME types are allowed: {mimeTypes}.' => 'Povolené sú len súbory nasledovných MIME typov: {mimeTypes}.',
    'Only files with these extensions are allowed: {extensions}.' => 'Povolené sú len súbory s nasledovnými príponami: {extensions}.',
    'Page not found.' => 'Stránka nebola nájdená.',
    'Please fix the following errors:' => 'Opravte prosím nasledujúce chyby:',
    'Please upload a file.' => 'Nahrajte prosím súbor.',
    'Showing <b>{begin, number}-{end, number}</b> of <b>{totalCount, number}</b> {totalCount, plural, one{item} other{items}}.' => 'Zobrazujem <b>{begin, number}-{end, number}</b> z <b>{totalCount, number}</b> {totalCount, plural, one{záznam} other{záznamov}}.',
    'The file "{file}" is not an image.' => 'Súbor "{file}" nie je obrázok.',
    'The file "{file}" is too big. Its size cannot exceed {formattedLimit}.' => 'Súbor "{file}" je príliš veľký. Veľkosť súboru nesmie byť viac ako {formattedLimit}.',
    'The file "{file}" is too small. Its size cannot be smaller than {formattedLimit}.' => 'Súbor "{file}" je príliš malý. Veľkosť súboru nesmie byť menej ako {formattedLimit}.',
    'The format of {attribute} is invalid.' => 'Formát atribútu {attribute} je neplatný.',
    'The image "{file}" is too large. The height cannot be larger than {limit, number} {limit, plural, one{pixel} other{pixels}}.' => 'Obrázok "{file}" je príliš veľký. Výška nesmie presiahnuť {limit, number} {limit, plural, one{pixel} other{pixlov}}.',
    'The image "{file}" is too large. The width cannot be larger than {limit, number} {limit, plural, one{pixel} other{pixels}}.' => 'Obrázok "{file}" je príliš veľký. Šírka nesmie presiahnuť {limit, number} {limit, plural, one{pixel} other{pixlov}}.',
    'The image "{file}" is too small. The height cannot be smaller than {limit, number} {limit, plural, one{pixel} other{pixels}}.' => 'Obrázok "{file}" je príliš malý. Výška nesmie byť menšia ako {limit, number} {limit, plural, one{pixel} other{pixlov}}.',
    'The image "{file}" is too small. The width cannot be smaller than {limit, number} {limit, plural, one{pixel} other{pixels}}.' => 'Obrázok "{file}" je príliš malý. Šírka nesmie byť menšia ako {limit, number} {limit, plural, one{pixel} other{pixlov}}.',
    'The requested view "{name}" was not found.' => 'Požadovaná stránka "{name}" nebola nájdená.',
    'The verification code is incorrect.' => 'Kód pre overenie je neplatný.',
    'Total <b>{count, number}</b> {count, plural, one{item} other{items}}.' => 'Celkovo <b>{count, number}</b> {count, plural, one{záznam} other{záznamov}}.',
    'Unable to verify your data submission.' => 'Nebolo možné preveriť odoslané údaje.',
    'Unknown option: --{name}' => 'Neznáme nastavenie: --{name}',
    'Update' => 'Upraviť',
    'View' => 'Náhľad',
    'Yes' => 'Áno',
    'You are not allowed to perform this action.' => 'Nemáte oprávnenie pre požadovanú akciu.',
    'You can upload at most {limit, number} {limit, plural, one{file} other{files}}.' => 'Nahrať môžete najviac {limit, number} {limit, plural, one{súbor} other{súborov}}.',
    'in {delta, plural, =1{a day} other{# days}}' => 'o {delta, plural, =1{deň} other{# dni}}',
    'in {delta, plural, =1{a minute} other{# minutes}}' => 'o {delta, plural, =1{minútu} other{# minút}}',
    'in {delta, plural, =1{a month} other{# months}}' => 'o {delta, plural, =1{mesiac} other{# mesiacov}}',
    'in {delta, plural, =1{a second} other{# seconds}}' => 'o {delta, plural, =1{sekundu} other{# sekúnd}}',
    'in {delta, plural, =1{a year} other{# years}}' => 'o {delta, plural, =1{rok} other{# rokov}}',
    'in {delta, plural, =1{an hour} other{# hours}}' => 'o {delta, plural, =1{hodinu} other{# hodín}}',
    'just now' => 'práve teraz',
    'the input value' => 'vstupná hodnota',
    '{attribute} "{value}" has already been taken.' => '{attribute} "{value}" je už použité.',
    '{attribute} cannot be blank.' => 'Pole {attribute} nesmie byť prázdne.',
    '{attribute} is invalid.' => 'Pole {attribute} je neplatné.',
    '{attribute} is not a valid URL.' => '{attribute} nie je platná URL.',
    '{attribute} is not a valid email address.' => '{attribute} nie je platná emailová adresa.',
    '{attribute} is not in the allowed range.' => '{attribute} je mimo povoleného rozsahu.',
    '{attribute} must be "{requiredValue}".' => '{attribute} musí byť "{requiredValue}".',
    '{attribute} must be a number.' => '{attribute} musí byť číslo.',
    '{attribute} must be a string.' => '{attribute} musí byť reťazec.',
    '{attribute} must be a valid IP address.' => '{attribute} musí byť platná IP adresa.',
    '{attribute} must be an IP address with specified subnet.' => '{attribute} musí byť IP adresa so špecifikovanou podsieťou.',
    '{attribute} must be an integer.' => '{attribute} musí byť integer.',
    '{attribute} must be either "{true}" or "{false}".' => '{attribute} musí byť "{true}" alebo "{false}".',
    '{attribute} must be equal to "{compareValueOrAttribute}".' => '{attribute} musí byť "{compareValueOrAttribute}".',
    '{attribute} must be greater than "{compareValueOrAttribute}".' => '{attribute} musí byť väčšie ako "{compareValueOrAttribute}".',
    '{attribute} must be greater than or equal to "{compareValueOrAttribute}".' => '{attribute} musí byť väčšie alebo rovné "{compareValueOrAttribute}".',
    '{attribute} must be less than "{compareValueOrAttribute}".' => '{attribute} musí byť menšie ako "{compareValueOrAttribute}".',
    '{attribute} must be less than or equal to "{compareValueOrAttribute}".' => '{attribute} musí byť menšie alebo rovné "{compareValueOrAttribute}".',
    '{attribute} must be no greater than {max}.' => '{attribute} nesmie byť vyšší ako {max}.',
    '{attribute} must be no less than {min}.' => '{attribute} nesmie byť nižší ako {min}.',
    '{attribute} must not be a subnet.' => '{attribute} nesmie byť podsieť.',
    '{attribute} must not be an IPv4 address.' => '{attribute} nesmie byť IPv4 adresa.',
    '{attribute} must not be an IPv6 address.' => '{attribute} nesmie byť IPv6 adresa.',
    '{attribute} must not be equal to "{compareValueOrAttribute}".' => '{attribute} sa nesmie rovnať "{compareValueOrAttribute}".',
    '{attribute} should contain at least {min, number} {min, plural, one{character} other{characters}}.' => '{attribute} musí obsahovať aspoň {min, number} {min, plural, one{znak} other{znakov}}.',
    '{attribute} should contain at most {max, number} {max, plural, one{character} other{characters}}.' => '{attribute} môže obsahovať najviac {max, number} {max, plural, one{znak} other{znakov}}.',
    '{attribute} should contain {length, number} {length, plural, one{character} other{characters}}.' => '{attribute} musí obsahovať {length, number} {length, plural, one{znak} other{znakov}}.',
    '{delta, plural, =1{1 day} other{# days}}' => '{delta, plural, =1{1 deň} =2{2 dni} =3{3 dni} =4{4 dni} other{# dní}}',
    '{delta, plural, =1{1 hour} other{# hours}}' => '{delta, plural, =1{1 hodina} =2{2 hodiny} =3{3 hodiny} =4{4 hodiny} other{# hodín}}',
    '{delta, plural, =1{1 minute} other{# minutes}}' => '{delta, plural, =1{1 minúta} =2{2 minúty} =3{3 minúty} =4{4 minúty} other{# minút}}',
    '{delta, plural, =1{1 month} other{# months}}' => '{delta, plural, =1{1 mesiac} =2{2 mesiace} =3{3 mesiace} =4{4 mesiace} other{# mesiacov}}',
    '{delta, plural, =1{1 second} other{# seconds}}' => '{delta, plural, =1{1 sekunda} =2{2 sekundy} =3{3 sekundy} =4{4 sekundy} other{# sekúnd}}',
    '{delta, plural, =1{1 year} other{# years}}' => '{delta, plural, =1{1 rok} =2{2 roky} =3{3 roky} =4{4 roky} other{# rokov}}',
    '{delta, plural, =1{a day} other{# days}} ago' => '{delta, plural, =1{včera} other{pred # dňami}}',
    '{delta, plural, =1{a minute} other{# minutes}} ago' => 'pred {delta, plural, =1{minútou} other{# minútami}}',
    '{delta, plural, =1{a month} other{# months}} ago' => 'pred {delta, plural, =1{mesiacom} other{# mesiacmi}}',
    '{delta, plural, =1{a second} other{# seconds}} ago' => 'pred {delta, plural, =1{sekundou} other{# sekundami}}',
    '{delta, plural, =1{a year} other{# years}} ago' => 'pred {delta, plural, =1{rokom} other{# rokmi}}',
    '{delta, plural, =1{an hour} other{# hours}} ago' => 'pred {delta, plural, =1{hodinou} other{# hodinami}}',
    '{nFormatted} B' => '{nFormatted} B',
    '{nFormatted} GB' => '{nFormatted} GB',
    '{nFormatted} GiB' => '{nFormatted} GiB',
    '{nFormatted} KB' => '{nFormatted} KB',
    '{nFormatted} KiB' => '{nFormatted} KiB',
    '{nFormatted} MB' => '{nFormatted} MB',
    '{nFormatted} MiB' => '{nFormatted} MiB',
    '{nFormatted} PB' => '{nFormatted} PB',
    '{nFormatted} PiB' => '{nFormatted} PiB',
    '{nFormatted} TB' => '{nFormatted} TB',
    '{nFormatted} TiB' => '{nFormatted} TiB',
    '{nFormatted} {n, plural, =1{byte} other{bytes}}' => '{nFormatted} {n, plural, =1{bajt} =2{bajty} =3{bajty} =4{bajty} other{bajtov}}',
    '{nFormatted} {n, plural, =1{gibibyte} other{gibibytes}}' => '{nFormatted} {n, plural, =1{gibibajt} =2{gibibajty} =3{gibibajty} =4{gibibajty} other{gibibajtov}}',
    '{nFormatted} {n, plural, =1{gigabyte} other{gigabytes}}' => '{nFormatted} {n, plural, =1{gigabajt} =2{gigabajty} =3{gigabajty} =4{gigabajty} other{gigabajtov}}',
    '{nFormatted} {n, plural, =1{kibibyte} other{kibibytes}}' => '{nFormatted} {n, plural, =1{kibibajt} =2{kibibajty} =3{kibibajty} =4{kibibajty} other{kibibajtov}}',
    '{nFormatted} {n, plural, =1{kilobyte} other{kilobytes}}' => '{nFormatted} {n, plural, =1{kilobajt} =2{kilobajty} =3{kilobajty} =4{kilobajty} other{kilobajtov}}',
    '{nFormatted} {n, plural, =1{mebibyte} other{mebibytes}}' => '{nFormatted} {n, plural, =1{mebibajt} =2{mebibajty} =3{mebibajty} =4{mebibajty} other{mebibajtov}}',
    '{nFormatted} {n, plural, =1{megabyte} other{megabytes}}' => '{nFormatted} {n, plural, =1{megabajt} =2{megabajty} =3{megabajty} =4{megabajty} other{megabajtov}}',
    '{nFormatted} {n, plural, =1{pebibyte} other{pebibytes}}' => '{nFormatted} {n, plural, =1{pebibajt} =2{pebibajty} =3{pebibajty} =4{pebibajty} other{pebibajtov}}',
    '{nFormatted} {n, plural, =1{petabyte} other{petabytes}}' => '{nFormatted} {n, plural, =1{petabajt} =2{petabajty} =3{petabajty} =4{petabajty} other{petabajtov}}',
    '{nFormatted} {n, plural, =1{tebibyte} other{tebibytes}}' => '{nFormatted} {n, plural, =1{tebibajt} =2{tebibajty} =3{tebibajty} =4{tebibajty} other{tebibajtov}}',
    '{nFormatted} {n, plural, =1{terabyte} other{terabytes}}' => '{nFormatted} {n, plural, =1{terabajt} =2{terabajty} =3{terabajty} =4{terabajty} other{terabajtov}}',
];
