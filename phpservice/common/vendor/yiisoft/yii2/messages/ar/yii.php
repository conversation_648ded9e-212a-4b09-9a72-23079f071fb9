<?php
/**
 * Message translations.
 *
 * This file is automatically generated by 'yii message' command.
 * It contains the localizable messages extracted from source code.
 * You may modify this file by translating the extracted messages.
 *
 * Each array element represents the translation (value) of a message (key).
 * If the value is empty, the message is considered as not translated.
 * Messages that no longer need translation will have their translations
 * enclosed between a pair of '@@' marks.
 *
 * Message string can be used with plural forms format. Check i18n section
 * of the guide for details.
 *
 * NOTE: this file must be saved in UTF-8 encoding.
 */
return [
    '(not set)' => '(لم يحدد)',
    'An internal server error occurred.' => '.حدث خطأ داخلي في الخادم',
    'Delete' => 'حذف',
    'Error' => 'خطأ',
    'File upload failed.' => '.فشل في تحميل الملف',
    'Home' => 'الرئيسية',
    'Invalid data received for parameter "{param}".' => 'بيانات غير صالحة قد وردت في "{param}".',
    'Login Required' => 'تسجبل الدخول مطلوب',
    'Missing required arguments: {params}' => 'البيانات المطلوبة ضرورية: {params}',
    'Missing required parameters: {params}' => 'البيانات المطلوبة ضرورية: {params}',
    'No' => 'لا',
    'No help for unknown command "{command}".' => 'ليس هناك مساعدة لأمر غير معروف "{command}".',
    'No help for unknown sub-command "{command}".' => 'ليس هناك مساعدة لأمر فرعي غير معروف "{command}".',
    'No results found.' => 'لم يتم العثور على نتائج',
    'Only files with these extensions are allowed: {extensions}.' => 'فقط الملفات التي تحمل هذه الصيغ مسموح بها: {extensions}.',
    'Only files with these MIME types are allowed: {mimeTypes}.' => 'فقط الملفات من هذه الأنواع مسموح بها: {mimeTypes}.',
    'Page not found.' => 'لم يتم العثور على الصفحة',
    'Please fix the following errors:' => 'الرجاء تصحيح الأخطاء التالية:',
    'Please upload a file.' => 'الرجاء تحميل ملف.',
    'Showing <b>{begin, number}-{end, number}</b> of <b>{totalCount, number}</b> {totalCount, plural, one{item} other{items}}.' => 'عرض <b>{begin, number}-{end, number}</b> من أصل <b>{totalCount, number}</b> {totalCount, plural, one{مُدخل} few{مُدخلات} many{مُدخل} other{مُدخلات}}.',
    'The file "{file}" is not an image.' => 'الملف "{file}" ليس صورة.',
    'The file "{file}" is too big. Its size cannot exceed {formattedLimit}.' => 'الملف "{file}" كبير الحجم. حجمه لا يجب أن يتخطى {formattedLimit}.',
    'The file "{file}" is too small. Its size cannot be smaller than {formattedLimit}.' => 'الملف "{file}"  صغير جداً. حجمه لا يجب أن يكون أصغر من {formattedLimit}.',
    'The format of {attribute} is invalid.' => 'شكل {attribute} غير صالح',
    'The image "{file}" is too large. The height cannot be larger than {limit, number} {limit, plural, one{pixel} other{pixels}}.' => 'الصورة "{file}" كبيرة جداً. ارتفاعها لا يمكن أن يتخطى {limit, number} {limit, plural, other{بكسل}}.',
    'The image "{file}" is too large. The width cannot be larger than {limit, number} {limit, plural, one{pixel} other{pixels}}.' => 'الصورة "{file}" كبيرة جداً. عرضها لا يمكن أن يتخطى {limit, number} {limit, plural, other{بكسل}}.',
    'The image "{file}" is too small. The height cannot be smaller than {limit, number} {limit, plural, one{pixel} other{pixels}}.' => 'الصورة "{file}" صغيرة جداً. ارتفاعها لا يمكن أن يقل عن  {limit, number} {limit, plural, other{بكسل}}.',
    'The image "{file}" is too small. The width cannot be smaller than {limit, number} {limit, plural, one{pixel} other{pixels}}.' => 'الصورة "{file}" كبيرة جداً. عرضها لا يمكن أن يقل عن  {limit, number} {limit, plural, other{بكسل}}.',
    'The verification code is incorrect.' => 'رمز التحقق غير صحيح',
    'Total <b>{count, number}</b> {count, plural, one{item} other{items}}.' => 'مجموع <b>{count, number}</b> {count, plural, one{مُدخل} few{مُدخلات} many{مُدخل}}.',
    'Unable to verify your data submission.' => 'لم نستطع التأكد من البيانات المقدمة.',
    'Unknown command "{command}".' => 'أمر غير معروف. "{command}"',
    'Unknown option: --{name}' => 'خيار غير معروف: --{name}',
    'Update' => 'تحديث',
    'View' => 'عرض',
    'Yes' => 'نعم',
    'You are not allowed to perform this action.' => 'غير مصرح لك القيام بهذا العمل',
    'You can upload at most {limit, number} {limit, plural, one{file} other{files}}.' => 'تستطيع كأقصى حد تحميل {limit, number} {limit, plural, one{ملف} few{ملفات} many{ملف} other{ملفات}}.',
    'the input value' => 'قيمة المُدخل',
    '{attribute} "{value}" has already been taken.' => '{attribute} "{value}" سبق استعماله',
    '{attribute} cannot be blank.' => '{attribute} لا يمكن تركه فارغًا.',
    '{attribute} is invalid.' => '{attribute} غير صالح.',
    '{attribute} is not a valid URL.' => '{attribute} ليس بعنوان صحيح.',
    '{attribute} is not a valid email address.' => '{attribute} ليس ببريد إلكتروني صحيح.',
    '{attribute} must be "{requiredValue}".' => '{attribute} يجب أن يكون "{requiredValue}".',
    '{attribute} must be a number.' => '{attribute} يجب أن يكون رقمًا',
    '{attribute} must be a string.' => '{attribute} يجب أن يكون كلمات',
    '{attribute} must be an integer.' => '{attribute} يجب أن يكون رقمًا صحيحًا',
    '{attribute} must be either "{true}" or "{false}".' => '{attribute} يجب أن يكن إما "{true}" أو "{false}".',
    '{attribute} must be equal to "{compareValueOrAttribute}".' => '{attribute} يجب أن يساوي "{compareValueOrAttribute}".',
    '{attribute} must be greater than "{compareValue}".' => '{attribute} يجب أن يكون أكبر من "{compareValue}".',
    '{attribute} must be greater than or equal to "{compareValue}".' => '{attribute} يجب أن يكون أكبر من أو يساوي  "{compareValue}".',
    '{attribute} must be less than "{compareValue}".' => '{attribute} يجب أن يكون أصغر من "{compareValue}".',
    '{attribute} must be less than or equal to "{compareValue}".' => '{attribute} يجب أن يكون أصغر من أو يساوي "{compareValue}".',
    '{attribute} must be no greater than {max}.' => '{attribute} يجب أن لا يكون أكبر من "{max}".',
    '{attribute} must be no less than {min}.' => '{attribute} يجب أن لا يكون أصغر من "{min}".',
    '{attribute} must be repeated exactly.' => '{attribute} يجب أن يكون متطابق.',
    '{attribute} must not be equal to "{compareValue}".' => '{attribute} يجب ان لا يساوي "{compareValue}"',
    '{attribute} should contain at least {min, number} {min, plural, one{character} other{characters}}.' => '{attribute} يجب أن يحتوي على أكثر من {min, number} {min, plural, one{حرف} few{حروف} other{حرف}}.',
    '{attribute} should contain at most {max, number} {max, plural, one{character} other{characters}}.' => '{attribute} يجب أن لا يحتوي على أكثر من {max, number} {max, plural, one{حرف} few{حروف} other{حرف}}.',
    '{attribute} should contain {length, number} {length, plural, one{character} other{characters}}.' => '{attribute}  يجب أن يحتوي على {length, number} {length, plural, one{حرف} few{حروف} other{حرف}}.',
    '{nFormatted} B' => '{nFormatted} بايت',
    '{nFormatted} GB' => '{nFormatted} جيجابايت',
    '{nFormatted} GiB' => '{nFormatted} جيبيبايت',
    '{nFormatted} KB' => '{nFormatted} كيلوبايت',
    '{nFormatted} KiB' => '{nFormatted} كيبيبايت',
    '{nFormatted} MB' => '{nFormatted} ميجابايت',
    '{nFormatted} MiB' => '{nFormatted} ميبيبايت',
    '{nFormatted} PB' => '{nFormatted} بيتابايت',
    '{nFormatted} PiB' => '{nFormatted} بيبيبايت',
    '{nFormatted} TB' => '{nFormatted} تيرابايت',
    '{nFormatted} TiB' => '{nFormatted} تيبيبايت',
    '{nFormatted} {n, plural, =1{byte} other{bytes}}' => '{nFormatted} بايت',
    '{nFormatted} {n, plural, =1{gibibyte} other{gibibytes}}' => '{nFormatted} جيبيبايت',
    '{nFormatted} {n, plural, =1{gigabyte} other{gigabytes}}' => '{nFormatted} جيجابايت',
    '{nFormatted} {n, plural, =1{kibibyte} other{kibibytes}}' => '{nFormatted} كيبيبايت',
    '{nFormatted} {n, plural, =1{kilobyte} other{kilobytes}}' => '{nFormatted} كيلوبايت',
    '{nFormatted} {n, plural, =1{mebibyte} other{mebibytes}}' => '{nFormatted} ميبيبايت',
    '{nFormatted} {n, plural, =1{megabyte} other{megabytes}}' => '{nFormatted} ميجابايت',
    '{nFormatted} {n, plural, =1{pebibyte} other{pebibytes}}' => '{nFormatted} بيبيبايت',
    '{nFormatted} {n, plural, =1{petabyte} other{petabytes}}' => '{nFormatted} بيتابايت',
    '{nFormatted} {n, plural, =1{tebibyte} other{tebibytes}}' => '{nFormatted} تيبيبايت',
    '{nFormatted} {n, plural, =1{terabyte} other{terabytes}}' => '{nFormatted} تيرابايت',
];
