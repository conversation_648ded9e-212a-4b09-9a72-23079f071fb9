<?php
/**
 * Message translations.
 *
 * This file is automatically generated by 'yii message' command.
 * It contains the localizable messages extracted from source code.
 * You may modify this file by translating the extracted messages.
 *
 * Each array element represents the translation (value) of a message (key).
 * If the value is empty, the message is considered as not translated.
 * Messages that no longer need translation will have their translations
 * enclosed between a pair of '@@' marks.
 *
 * Message string can be used with plural forms format. Check i18n section
 * of the guide for details.
 *
 * NOTE: this file must be saved in UTF-8 encoding.
 */
return [
    'Unknown alias: -{name}' => '未知的别名: -{name}',
    '(not set)' => '(未设置)',
    'An internal server error occurred.' => '服务器内部错误。',
    'Are you sure you want to delete this item?' => '您确定要删除此项吗？',
    'Delete' => '删除',
    'Error' => '错误',
    'File upload failed.' => '文件上传失败。',
    'Home' => '首页',
    'Invalid data received for parameter "{param}".' => '"{param}"参数接收到无效的数据。',
    'Login Required' => '需要登录',
    'Missing required arguments: {params}' => '函数缺少参数：{params}',
    'Missing required parameters: {params}' => '缺少参数：{params}',
    'No' => '否',
    'No help for unknown command "{command}".' => '命令"{command}"发生未知的错误。',
    'No help for unknown sub-command "{command}".' => '子命令"{command}"发生未知的错误。',
    'No results found.' => '没有找到数据。',
    'Only files with these MIME types are allowed: {mimeTypes}.' => '只允许这些MIME类型的文件: {mimeTypes}。',
    'Only files with these extensions are allowed: {extensions}.' => '只允许使用以下文件扩展名的文件：{extensions}。',
    'Page not found.' => '页面未找到。',
    'Please fix the following errors:' => '请修复以下错误',
    'Please upload a file.' => '请上传一个文件。',
    'Powered by {yii}' => '技术支持 {yii}',
    'Showing <b>{begin, number}-{end, number}</b> of <b>{totalCount, number}</b> {totalCount, plural, one{item} other{items}}.' => '第<b>{begin, number}-{end, number}</b>条，共<b>{totalCount, number}</b>条数据.',
    'The file "{file}" is not an image.' => '文件 "{file}" 不是一个图像文件。',
    'The file "{file}" is too big. Its size cannot exceed {formattedLimit}.' => '文件"{file}"太大了。它的大小不能超过{formattedLimit}。',
    'The file "{file}" is too small. Its size cannot be smaller than {formattedLimit}.' => '文件"{file}"太小了。它的大小不能小于{formattedLimit}。',
    'The file "{file}" is too big. Its size cannot exceed {limit, number} {limit, plural, one{byte} other{bytes}}.' => '文件"{file}"太大。它的大小不能超过{limit, number}字节。',
    'The file "{file}" is too small. Its size cannot be smaller than {limit, number} {limit, plural, one{byte} other{bytes}}.' => '该文件"{file}"太小。它的大小不得小于{limit, number}字节。',
    'The format of {attribute} is invalid.' => '属性 {attribute} 的格式无效。',
    'The image "{file}" is too large. The height cannot be larger than {limit, number} {limit, plural, one{pixel} other{pixels}}.' => '图像"{file}"太大。他的高度不得超过{limit, number}像素。',
    'The image "{file}" is too large. The width cannot be larger than {limit, number} {limit, plural, one{pixel} other{pixels}}.' => '图像"{file}"太大。他的宽度不得超过{limit, number}像素。',
    'The image "{file}" is too small. The height cannot be smaller than {limit, number} {limit, plural, one{pixel} other{pixels}}.' => '图像"{file}"太小。他的高度不得小于{limit, number}像素。',
    'The image "{file}" is too small. The width cannot be smaller than {limit, number} {limit, plural, one{pixel} other{pixels}}.' => '图像"{file}"太小。他的宽度不得小于{limit, number}像素。',
    'The requested view "{name}" was not found.' => '所请求的视图不存在"{name}"。',
    'The verification code is incorrect.' => '验证码不正确。',
    'Total <b>{count, number}</b> {count, plural, one{item} other{items}}.' => '总计<b>{count, number}</b>条数据。',
    'Unable to verify your data submission.' => '您提交的数据无法被验证。',
    'Unknown command "{command}".' => '未知的命令 "{command}"。',
    'Unknown option: --{name}' => '未知的选项：--{name}',
    'Update' => '更新',
    'View' => '查看',
    'Yes' => '是',
    'Yii Framework' => 'Yii 框架',
    'You are not allowed to perform this action.' => '您没有执行此操作的权限。',
    'You can upload at most {limit, number} {limit, plural, one{file} other{files}}.' => '您最多上传{limit, number}个文件。',
    'the input value' => '该输入',
    '{attribute} "{value}" has already been taken.' => '{attribute}的值"{value}"已经被占用了。',
    '{attribute} cannot be blank.' => '{attribute}不能为空。',
    '{attribute} contains wrong subnet mask.' => '{attribute} 属性包含错误的子网掩码。',
    '{attribute} is invalid.' => '{attribute}是无效的。',
    '{attribute} is not a valid URL.' => '{attribute}不是一条有效的URL。',
    '{attribute} is not a valid email address.' => '{attribute}不是有效的邮箱地址。',
    '{attribute} is not in the allowed range.' => '{attribute} 不在允许的范围内。',
    '{attribute} must be "{requiredValue}".' => '{attribute}必须为"{requiredValue}"。',
    '{attribute} must be a number.' => '{attribute}必须是一个数字。',
    '{attribute} must be a string.' => '{attribute}必须是一条字符串。',
    '{attribute} must be a valid IP address.' => '{attribute} 必须是一个有效的IP地址。',
    '{attribute} must be an IP address with specified subnet.' => '{attribute} 必须指定一个IP地址和子网。',
    '{attribute} must be an integer.' => '{attribute}必须是整数。',
    '{attribute} must be either "{true}" or "{false}".' => '{attribute}的值必须要么为"{true}"，要么为"{false}"。',
    '{attribute} must be equal to "{compareValueOrAttribute}".' => '{attribute}的值必须等于"{compareValueOrAttribute}"。',
    '{attribute} must not be equal to "{compareValueOrAttribute}".' => '{attribute}的值不得等于"{compareValueOrAttribute}"。',
    '{attribute} must be greater than "{compareValueOrAttribute}".' => '{attribute}的值必须大于"{compareValueOrAttribute}"。',
    '{attribute} must be greater than or equal to "{compareValueOrAttribute}".' => '{attribute}的值必须大于或等于"{compareValueOrAttribute}"。',
    '{attribute} must be less than "{compareValueOrAttribute}".' => '{attribute}的值必须小于"{compareValueOrAttribute}"。',
    '{attribute} must be less than or equal to "{compareValueOrAttribute}".' => '{attribute}的值必须小于或等于"{compareValueOrAttribute}"。',
    '{attribute} must be no greater than {max}.' => '{attribute}的值必须不大于{max}。',
    '{attribute} must be no less than {min}.' => '{attribute}的值必须不小于{min}。',
    '{attribute} must not be a subnet.' => '{attribute} 必须不是一个子网。',
    '{attribute} must not be an IPv4 address.' => '{attribute} 必须不是一个IPv4地址。',
    '{attribute} must not be an IPv6 address.' => '{attribute} 必须不是一个IPv6地址。',
    '{attribute} must be repeated exactly.' => '{attribute}必须重复。',
    '{attribute} should contain at least {min, number} {min, plural, one{character} other{characters}}.' => '{attribute}应该包含至少{min, number}个字符。',
    '{attribute} should contain at most {max, number} {max, plural, one{character} other{characters}}.' => '{attribute}只能包含至多{max, number}个字符。',
    '{attribute} should contain {length, number} {length, plural, one{character} other{characters}}.' => '{attribute}应该包含{length, number}个字符。',
    'in {delta, plural, =1{a year} other{# years}}' => '{delta}年后',
    'in {delta, plural, =1{a month} other{# months}}' => '{delta}个月后',
    'in {delta, plural, =1{a day} other{# days}}' => '{delta}天后',
    'in {delta, plural, =1{an hour} other{# hours}}' => '{delta}小时后',
    'in {delta, plural, =1{a minute} other{# minutes}}' => '{delta}分钟后',
    'in {delta, plural, =1{a second} other{# seconds}}' => '{delta}秒后',
    '{delta, plural, =1{1 day} other{# days}}' => '{delta} 天',
    '{delta, plural, =1{1 hour} other{# hours}}' => '{delta} 小时',
    '{delta, plural, =1{1 minute} other{# minutes}}' => '{delta} 分钟',
    '{delta, plural, =1{1 month} other{# months}}' => '{delta} 月',
    '{delta, plural, =1{1 second} other{# seconds}}' => '{delta} 秒',
    '{delta, plural, =1{1 year} other{# years}}' => '{delta} 年',
    '{delta, plural, =1{a year} other{# years}} ago' => '{delta}年前',
    '{delta, plural, =1{a month} other{# months}} ago' => '{delta}个月前',
    '{delta, plural, =1{a day} other{# days}} ago' => '{delta}天前',
    '{delta, plural, =1{an hour} other{# hours}} ago' => '{delta}小时前',
    '{delta, plural, =1{a minute} other{# minutes}} ago' => '{delta}分钟前',
    'just now' => '刚刚',
    '{delta, plural, =1{a second} other{# seconds}} ago' => '{delta}秒前',
    '{nFormatted} B' => '{nFormatted} B',
    '{nFormatted} GB' => '{nFormatted} GB',
    '{nFormatted} GiB' => '{nFormatted} GiB',
    '{nFormatted} KB' => '{nFormatted} KB',
    '{nFormatted} KiB' => '{nFormatted} KiB',
    '{nFormatted} MB' => '{nFormatted} MB',
    '{nFormatted} MiB' => '{nFormatted} MiB',
    '{nFormatted} PB' => '{nFormatted} PB',
    '{nFormatted} PiB' => '{nFormatted} PiB',
    '{nFormatted} TB' => '{nFormatted} TB',
    '{nFormatted} TiB' => '{nFormatted} TiB',
    '{nFormatted} {n, plural, =1{byte} other{bytes}}' => '{nFormatted} 字节',
    '{nFormatted} {n, plural, =1{gibibyte} other{gibibytes}}' => '{nFormatted} 千兆二进制字节',
    '{nFormatted} {n, plural, =1{gigabyte} other{gigabytes}}' => '{nFormatted} 千兆字节',
    '{nFormatted} {n, plural, =1{kibibyte} other{kibibytes}}' => '{nFormatted} 千位二进制字节',
    '{nFormatted} {n, plural, =1{kilobyte} other{kilobytes}}' => '{nFormatted} 千字节',
    '{nFormatted} {n, plural, =1{mebibyte} other{mebibytes}}' => '{nFormatted} 兆二进制字节',
    '{nFormatted} {n, plural, =1{megabyte} other{megabytes}}' => '{nFormatted} 兆字节',
    '{nFormatted} {n, plural, =1{pebibyte} other{pebibytes}}' => '{nFormatted} 拍二进制字节',
    '{nFormatted} {n, plural, =1{petabyte} other{petabytes}}' => '{nFormatted} 拍字节',
    '{nFormatted} {n, plural, =1{tebibyte} other{tebibytes}}' => '{nFormatted} 太二进制字节',
    '{nFormatted} {n, plural, =1{terabyte} other{terabytes}}' => '{nFormatted} 太字节',
];
