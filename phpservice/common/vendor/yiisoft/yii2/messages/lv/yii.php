<?php
/**
 * Message translations.
 *
 * This file is automatically generated by 'yii message' command.
 * It contains the localizable messages extracted from source code.
 * You may modify this file by translating the extracted messages.
 *
 * Each array element represents the translation (value) of a message (key).
 * If the value is empty, the message is considered as not translated.
 * Messages that no longer need translation will have their translations
 * enclosed between a pair of '@@' marks.
 *
 * Message string can be used with plural forms format. Check i18n section
 * of the guide for details.
 *
 * NOTE: this file must be saved in UTF-8 encoding.
 */
return [
    '{nFormatted} B'   => '{nFormatted} B',
    '{nFormatted} GB'  => '{nFormatted} Gb',
    '{nFormatted} GiB' => '{nFormatted} GiB',
    '{nFormatted} KB'  => '{nFormatted} KB',
    '{nFormatted} KiB' => '{nFormatted} KiB',
    '{nFormatted} MB'  => '{nFormatted} MB',
    '{nFormatted} MiB' => '{nFormatted} MiB',
    '{nFormatted} PB'  => '{nFormatted} PB',
    '{nFormatted} PiB' => '{nFormatted} PiB',
    '{nFormatted} TB'  => '{nFormatted} TB',
    '{nFormatted} TiB' => '{nFormatted} TiB',
    '{nFormatted} {n, plural, =1{byte} other{bytes}}'         => '{nFormatted} {n, plural, zero{baitu} one{baits} other{baiti}}',
    '{nFormatted} {n, plural, =1{gibibyte} other{gibibytes}}' => '{nFormatted} gibi{n, plural, zero{baitu} one{baits} other{baiti}}',
    '{nFormatted} {n, plural, =1{gigabyte} other{gigabytes}}' => '{nFormatted} giga{n, plural, zero{baitu} one{baits} other{baiti}}',
    '{nFormatted} {n, plural, =1{kibibyte} other{kibibytes}}' => '{nFormatted} kibi{n, plural, zero{baitu} one{baits} other{baiti}}',
    '{nFormatted} {n, plural, =1{kilobyte} other{kilobytes}}' => '{nFormatted} kilo{n, plural, zero{baitu} one{baits} other{baiti}}',
    '{nFormatted} {n, plural, =1{mebibyte} other{mebibytes}}' => '{nFormatted} mebi{n, plural, zero{baitu} one{baits} other{baiti}}',
    '{nFormatted} {n, plural, =1{megabyte} other{megabytes}}' => '{nFormatted} mega{n, plural, zero{baitu} one{baits} other{baiti}}',
    '{nFormatted} {n, plural, =1{pebibyte} other{pebibytes}}' => '{nFormatted} pebi{n, plural, zero{baitu} one{baits} other{baiti}}',
    '{nFormatted} {n, plural, =1{petabyte} other{petabytes}}' => '{nFormatted} peta{n, plural, zero{baitu} one{baits} other{baiti}}',
    '{nFormatted} {n, plural, =1{tebibyte} other{tebibytes}}' => '{nFormatted} tebi{n, plural, zero{baitu} one{baits} other{baiti}}',
    '{nFormatted} {n, plural, =1{terabyte} other{terabytes}}' => '{nFormatted} tera{n, plural, zero{baitu} one{baits} other{baiti}}',
    '(not set)' => '(nav uzstādīts)',
    'An internal server error occurred.' => 'Notika servera iekšēja kļūda.',
    'Are you sure you want to delete this item?' => 'Vai jūs esat pārliecināti, ka vēlaties nodzēst šo elementu?',
    'Delete' => 'Dzēst',
    'Error' => 'Kļūda',
    'File upload failed.' => 'Neizdevās augšupielādēt failu.',
    'Home' => 'Galvenā',
    'Invalid data received for parameter "{param}".' => 'Tika saņemta nepareiza vērtība parametram "{param}".',
    'Login Required' => 'Nepieciešama autorizācija.',
    'Missing required arguments: {params}' => 'Trūkst nepieciešamos argumentus: {params}',
    'Missing required parameters: {params}' => 'Trūkst nepieciešamos parametrus: {params}',
    'No' => 'Nē',
    'No help for unknown command "{command}".' => 'Palīdzība nezināmai komandai "{command}" nav pieejama.',
    'No help for unknown sub-command "{command}".' => 'Palīdzība nezināmai sub-komandai "{command}" nav pieejama',
    'No results found.' => 'Nekas nav atrasts.',
    'Only files with these MIME types are allowed: {mimeTypes}.' => 'Ir atļauts augšupielādēt failus tikai ar sekojošiem MIME-tipiem: {mimeTypes}.',
    'Only files with these extensions are allowed: {extensions}.' => 'Ir atļauts augšupielādēt failus tikai ar sekojošiem paplašinājumiem: {extensions}.',
    'Page not found.' => 'Pieprasīta lapa netika atrasta.',
    'Please fix the following errors:' => 'Nepieciešams izlabot sekojošas kļūdas:',
    'Please upload a file.' => 'Lūdzu, augšupielādiet failu.',
    'Showing <b>{begin, number}-{end, number}</b> of <b>{totalCount, number}</b> {totalCount, plural, one{item} other{items}}.' => 'Tiek rādīti ieraksti <b>{begin, number}-{end, number}</b> no <b>{totalCount, number}</b>.',
    'The file "{file}" is not an image.' => 'Fails „{file}” nav uzskatīts par attēlu.',
    'The file "{file}" is too big. Its size cannot exceed {formattedLimit}.' => 'Fails „{file}” pārsniedz pieļaujamo ierobežojumu. Izmēram nedrīkst pārsniegt {formattedLimit}.',
    'The file "{file}" is too small. Its size cannot be smaller than {formattedLimit}.' => 'Fails „{file}” ir pārāk mazs. Izmēram ir jābūt vairāk par {formattedLimit}.',
    'The format of {attribute} is invalid.' => 'Vērtībai „{attribute}” ir nepareizs formāts.',
    'The image "{file}" is too large. The height cannot be larger than {limit, number} {limit, plural, one{pixel} other{pixels}}.' => 'Attēls „{file}” ir pārāk liels. Augstumam ir jābūt mazākam par {limit, number} {limit, plural, one{pikseļi} other{pikseļiem}}.',
    'The image "{file}" is too large. The width cannot be larger than {limit, number} {limit, plural, one{pixel} other{pixels}}.' => 'Attēls „{file}” ir pārāk liels. Platumam ir jābūt mazākam par {limit, number} {limit, plural, one{pikseļi} other{pikseļiem}}.',
    'The image "{file}" is too small. The height cannot be smaller than {limit, number} {limit, plural, one{pixel} other{pixels}}.' => 'Attēls „{file}” ir pārāk mazs. Augstumam ir jābūt lielākam par {limit, number} {limit, plural, one{pikseļi} other{pikseļiem}}.',
    'The image "{file}" is too small. The width cannot be smaller than {limit, number} {limit, plural, one{pixel} other{pixels}}.' => 'Attēls „{file}” ir pārāk mazs. Platumam ir jābūt lielākam par {limit, number} {limit, plural, one{pikseļi} other{pikseļiem}}.',
    'The requested view "{name}" was not found.' => 'Pieprasīts priekšstata fails „{name}” nav atrasts.',
    'The verification code is incorrect.' => 'Nepareizs pārbaudes kods.',
    'Total <b>{count, number}</b> {count, plural, one{item} other{items}}.' => 'Kopā <b>{count, number}</b> {count, plural, zero{ierakstu} one{ieraksts} other{ieraksti}}.',
    'Unable to verify your data submission.' => 'Neizdevās pārbaudīt nosūtītos datus.',
    'Unknown command "{command}".' => 'Nezināma komanda "{command}".',
    'Unknown option: --{name}' => 'Nezināma opcija: --{name}',
    'Update' => 'Labot',
    'View' => 'Skatīties',
    'Yes' => 'Jā',
    'You are not allowed to perform this action.' => 'Jūs neesat autorizēts veikt šo darbību.',
    'You can upload at most {limit, number} {limit, plural, one{file} other{files}}.' => 'Jūs nevarat augšupielādēt vairāk par {limit, number} {limit, plural, one{failu} other{failiem}}.',
    'in {delta, plural, =1{a day} other{# days}}'       => 'pēc {delta, plural, =1{dienas} one{#. dienas} other{#. dienām}}',
    'in {delta, plural, =1{a minute} other{# minutes}}' => 'pēc {delta, plural, =1{minūtes} one{#. minūtes} other{#. minūtēm}}',
    'in {delta, plural, =1{a month} other{# months}}'   => 'pēc {delta, plural, =1{mēneša} one{#. mēneša} other{# mēnešiem}}',
    'in {delta, plural, =1{a second} other{# seconds}}' => 'pēc {delta, plural, =1{sekundes} one{#. sekundes} other{#. sekundēm}}',
    'in {delta, plural, =1{a year} other{# years}}'     => 'pēc {delta, plural, =1{gada} one{#. gada} other{#. gadām}}',
    'in {delta, plural, =1{an hour} other{# hours}}'    => 'pēc {delta, plural, =1{stundas} one{#. stundas} other{#. stundām}}',
    'the input value' => 'ievadīta vērtība',
    '{attribute} "{value}" has already been taken.' => '{attribute} „{value}” jau ir aizņemts.',
    '{attribute} cannot be blank.' => 'Ir jāaizpilda „{attribute}”.',
    '{attribute} is invalid.' => '„{attribute}” vērtība ir nepareiza.',
    '{attribute} is not a valid URL.' => '„{attribute}” vērtība netiek uzskatīta par pareizu URL.',
    '{attribute} is not a valid email address.' => '„{attribute}” vērtība netiek uzskatīta par pareizu e-pasta adresi.',
    '{attribute} must be "{requiredValue}".' => '„{attribute}” vērtībai ir jābūt vienādai ar „{requiredValue}”.',
    '{attribute} must be a number.' => '„{attribute}” vērtībai ir jābūt skaitlim.',
    '{attribute} must be a string.' => '„{attribute}” vērtībai ir jābūt virknei.',
    '{attribute} must be an integer.' => '„{attribute}” vērtībai ir jābūt veselam skaitlim.',
    '{attribute} must be either "{true}" or "{false}".' => '„{attribute}” vērtībai ir jābūt „{true}” vai „{false}”.',
    '{attribute} must be greater than "{compareValueOrAttribute}".' => '„{attribute}” vērtībai ir jābūt lielākai par „{compareValueOrAttribute}” vērtību.',
    '{attribute} must be greater than or equal to "{compareValueOrAttribute}".' => '„{attribute}” vērtībai ir jābūt lielākai vai vienādai ar „{compareValueOrAttribute}” vērtību.',
    '{attribute} must be less than "{compareValueOrAttribute}".' => '„{attribute}” vērtībai ir jābūt mazākai par „{compareValueOrAttribute}” vērtību.',
    '{attribute} must be less than or equal to "{compareValueOrAttribute}".' => '„{attribute}” vērtībai ir jābūt mazākai vai vienādai ar „{compareValueOrAttribute}” vērtību.',
    '{attribute} must be no greater than {max}.' => '„{attribute}” vērtībai nedrīkst pārsniegt {max}.',
    '{attribute} must be no less than {min}.' => '„{attribute}” vērtībai ir jāpārsniedz {min}.',
    '{attribute} must be equal to "{compareValueOrAttribute}".' => '„{attribute}” vērtībai jābūt vienādai ar „{compareValueOrAttribute}”.',
    '{attribute} must not be equal to "{compareValueOrAttribute}".' => '„{attribute}” vērtībai nedrīkst būt vienādai ar „{compareValueOrAttribute}”.',
    '{attribute} should contain at least {min, number} {min, plural, one{character} other{characters}}.' => '„{attribute}” vērtībai ir jāietver vismaz {min, number} {min, plural, one{simbolu} other{simbolus}}.',
    '{attribute} should contain at most {max, number} {max, plural, one{character} other{characters}}.' => '„{attribute}” vērtībai ir jāietver ne vairāk par {max, number} {max, plural, one{simbolu} other{simbolus}}.',
    '{attribute} should contain {length, number} {length, plural, one{character} other{characters}}.' => '„{attribute}” vērtībai ir jāietver {length, number} {length, plural, one{simbolu} other{simbolus}}.',
    '{delta, plural, =1{a day} other{# days}} ago'       => '{delta, plural, =1{diena} zero{# dienas} one{#. diena} other{#. dienas}} atpakaļ',
    '{delta, plural, =1{a minute} other{# minutes}} ago' => '{delta, plural, =1{minūte} zero{# minūtes} one{#. minūte} other{#. minūtes}} atpakaļ',
    '{delta, plural, =1{a month} other{# months}} ago'   => '{delta, plural, =1{mēness} zero{# mēnešu} one{#. mēness} other{#. mēnešu}} atpakaļ',
    '{delta, plural, =1{a second} other{# seconds}} ago' => '{delta, plural, =1{sekunde} zero{# sekundes} one{#. sekunde} other{#. sekundes}} atpakaļ',
    '{delta, plural, =1{a year} other{# years}} ago'     => '{delta, plural, =1{gads} zero{# gadi} one{#. gads} other{#. gadi}} atpakaļ',
    '{delta, plural, =1{an hour} other{# hours}} ago'    => '{delta, plural, =1{stunda} zero{# stundas} one{#. stunda} other{#. stundas}} atpakaļ',
];
