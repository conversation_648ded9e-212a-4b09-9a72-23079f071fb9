<?php
/**
 * Message translations.
 *
 * This file is automatically generated by 'yii message' command.
 * It contains the localizable messages extracted from source code.
 * You may modify this file by translating the extracted messages.
 *
 * Each array element represents the translation (value) of a message (key).
 * If the value is empty, the message is considered as not translated.
 * Messages that no longer need translation will have their translations
 * enclosed between a pair of '@@' marks.
 *
 * Message string can be used with plural forms format. Check i18n section
 * of the guide for details.
 *
 * NOTE: this file must be saved in UTF-8 encoding.
 */
return [
    '(not set)' => '(tidak ditetapkan)',
    'An internal server error occurred.' => 'Ralat dalaman pelayan web telah berlaku',
    'Are you sure you want to delete this item?' => 'Adakah anda pasti untuk menghapuskan item ini?',
    'Delete' => 'Padam',
    'Error' => 'Ralat',
    'File upload failed.' => 'Gagal memuat naik fail',
    'Home' => 'Utama',
    'Invalid data received for parameter "{param}".' => 'Data yang tidak sah untuk parameter "{param}".',
    'Login Required' => 'Wajib untuk Log Masuk',
    'Missing required arguments: {params}' => 'Kehilangan penyataan yang mandatori: {params}',
    'Missing required parameters: {params}' => 'Kehilangan parameter yang mandatori: {params}',
    'No' => 'Tidak',
    'No results found.' => 'Tiada keputusan dijumpai',
    'Only files with these MIME types are allowed: {mimeTypes}.' => 'Hanya fail yang berjenis MIME dibenarkan: {mimeTypes}.',
    'Only files with these extensions are allowed: {extensions}.' => 'Hanya fail yang mempunyai sambungan berikut dibenarkan: {extensions}.',
    'Page not found.' => 'Halaman tidak dijumpai.',
    'Please fix the following errors:' => 'Sila betulkan ralat berikut:',
    'Please upload a file.' => 'Sila muat naik fail',
    'Showing <b>{begin, number}-{end, number}</b> of <b>{totalCount, number}</b> {totalCount, plural, one{item} other{items}}.' => 
    'Memaparkan <b>{begin, number}-{end, number}</b> daripada <b>{totalCount, number}</b> {totalCount, plural, one{item} other{items}}.',
    'The file "{file}" is not an image.' => 'Fail ini "{file}" bukan bejenis gambar.',
    'The file "{file}" is too big. Its size cannot exceed {formattedLimit}.' =>
    'Fail ini "{file}" terlalu besar. Saiz tidak boleh lebih besar daripada {formattedLimit}.',
    'The file "{file}" is too small. Its size cannot be smaller than {formattedLimit}.' =>
    'Fail ini "{file}" terlalu kecil. Saiznya tidak boleh lebih kecil daripada {formattedLimit}.',
    'The format of {attribute} is invalid.' => 'Format untuk atribut ini {attribute} tidak sah.',
    'The image "{file}" is too large. The height cannot be larger than {limit, number} {limit, plural, one{pixel} other{pixels}}.' => 
    'Gambar "{file}" terlalu panjang. Panjang gambar tidak boleh lebih besar daripada {limit, number} {limit, plural, one{pixel} other{pixels}}.',
    'The image "{file}" is too large. The width cannot be larger than {limit, number} {limit, plural, one{pixel} other{pixels}}.' => 
    'Gambar "{file}" terlalu lebar. Gambar tidak boleh lebih lebar daripada {limit, number} {limit, plural, one{pixel} other{pixels}}.',
    'The image "{file}" is too small. The height cannot be smaller than {limit, number} {limit, plural, one{pixel} other{pixels}}.' => 
    'Gambar "{file}" terlalu singkat. Panjang tidak boleh lebih singkat daripada {limit, number} {limit, plural, one{pixel} other{pixels}}.',
    'The image "{file}" is too small. The width cannot be smaller than {limit, number} {limit, plural, one{pixel} other{pixels}}.' => 
    'Gambar "{file}" terlalu kecil. Lebar gambar tidak boleh kurang daripada {limit, number} {limit, plural, one{pixel} other{pixels}}.',
    'The requested view "{name}" was not found.' => 'Paparan yang diminta "{name}" tidak dijumpai.',
    'The verification code is incorrect.' => 'Kod penyesah tidak tepat.',
    'Total <b>{count, number}</b> {count, plural, one{item} other{items}}.' => 'Jumlah <b>{count, number}</b> {count, plural, one{item} other{items}}.',
    'Unable to verify your data submission.' => 'Tidak bejaya mengesahkan data yang dihantar.',
    'Unknown option: --{name}' => 'Pilihan lain: --{name}',
    'Update' => 'Kemaskini',
    'View' => 'Paparan',
    'Yes' => 'Ya',
    'You are not allowed to perform this action.' => 'Anda tidak dibenarkan untuk mengunakan fungsi ini.',
    'You can upload at most {limit, number} {limit, plural, one{file} other{files}}.' => 
    'Anda boleh memuat naik tidak lebih daripada  {limit, number} {limit, plural, one{file} other{files}}.',
    'in {delta, plural, =1{a day} other{# days}}' => 'dalam {delta, plural, =1{a day} other{# days}}',
    'in {delta, plural, =1{a minute} other{# minutes}}' => 'dalam {delta, plural, =1{a minute} other{# minutes}}',
    'in {delta, plural, =1{a month} other{# months}}' => 'dalam {delta, plural, =1{a month} other{# months}}',
    'in {delta, plural, =1{a second} other{# seconds}}' => 'dalam {delta, plural, =1{a second} other{# seconds}}',
    'in {delta, plural, =1{a year} other{# years}}' => 'dalam {delta, plural, =1{a year} other{# years}}',
    'in {delta, plural, =1{an hour} other{# hours}}' => 'dalam {delta, plural, =1{an hour} other{# hours}}',
    'just now' => 'baru sahaja',
    'the input value' => 'nilai input',
    '{attribute} "{value}" has already been taken.' => '{attribute} "{value}" telah digunakan.',
    '{attribute} cannot be blank.' => '{attribute} tidak boleh dibiarkan kosong.',
    '{attribute} is invalid.' => '{attribute} tidak sah.',
    '{attribute} is not a valid URL.' => '{attribute} alamat URL yang tidak sah.',
    '{attribute} is not a valid email address.' => '{attribute} adalah alamat email yang tidak sah.',
    '{attribute} must be "{requiredValue}".' => '{attribute} mestilah "{requiredValue}".',
    '{attribute} must be a number.' => '{attribute} mestilah nombor.',
    '{attribute} must be a string.' => '{attribute} mestilah perkataan.',
    '{attribute} must be an integer.' => '{attribute} mestilah nombor tanpa titik perpuluhan.',
    '{attribute} must be either "{true}" or "{false}".' => '{attribute} mestilah "{true}" atau "{false}".',
    '{attribute} must be greater than "{compareValue}".' => '{attribute} mestilah lebih besar daripada "{compareValue}".',
    '{attribute} must be greater than or equal to "{compareValue}".' => '{attribute} mestilah lebih besar atau sama dengan "{compareValue}".',
    '{attribute} must be less than "{compareValue}".' => '{attribute} mestilah kurang daripada "{compareValue}".',
    '{attribute} must be less than or equal to "{compareValue}".' => '{attribute} mestilah kurang daripada atau sama dengan "{compareValue}".',
    '{attribute} must be no greater than {max}.' => '{attribute} tidak boleh lebih besar daripada {max}.',
    '{attribute} must be no less than {min}.' => '{attribute} tidak boleh kurang daripada {min}.',
    '{attribute} must be repeated exactly.' => '{attribute} mestilah diulang dengan tepat.',
    '{attribute} must not be equal to "{compareValue}".' => '{attribute} mestilah tidak sama dengan "{compareValue}".',
    '{attribute} should contain at least {min, number} {min, plural, one{character} other{characters}}.' => 
    '{attribute} mesti mengandungi sekurang-kurangnya {min, number} {min, plural, one{character} other{characters}}.',
    '{attribute} should contain at most {max, number} {max, plural, one{character} other{characters}}.' => 
    '{attribute} mesti mengangungi paling banyak {max, number} {max, plural, one{character} other{characters}}.',
    '{attribute} should contain {length, number} {length, plural, one{character} other{characters}}.' => 
    '{attribute} mesti mengandungi {length, number} {length, plural, one{character} other{characters}}.',
    '{delta, plural, =1{a day} other{# days}} ago' => '{delta, plural, =1{a day} other{# days}} lalu',
    '{delta, plural, =1{a minute} other{# minutes}} ago' => '{delta, plural, =1{a minute} other{# minutes}} lalu',
    '{delta, plural, =1{a month} other{# months}} ago' => '{delta, plural, =1{a month} other{# months}} lalu',
    '{delta, plural, =1{a second} other{# seconds}} ago' => '{delta, plural, =1{a second} other{# seconds}} lalu',
    '{delta, plural, =1{a year} other{# years}} ago' => '{delta, plural, =1{a year} other{# years}} lalu',
    '{delta, plural, =1{an hour} other{# hours}} ago' => '{delta, plural, =1{an hour} other{# hours}} lalu',
    '{nFormatted} B' => '',
    '{nFormatted} GB' => '',
    '{nFormatted} GiB' => '',
    '{nFormatted} KB' => '',
    '{nFormatted} KiB' => '',
    '{nFormatted} MB' => '',
    '{nFormatted} MiB' => '',
    '{nFormatted} PB' => '',
    '{nFormatted} PiB' => '',
    '{nFormatted} TB' => '',
    '{nFormatted} TiB' => '',
    '{nFormatted} {n, plural, =1{byte} other{bytes}}' => '',
    '{nFormatted} {n, plural, =1{gibibyte} other{gibibytes}}' => '',
    '{nFormatted} {n, plural, =1{gigabyte} other{gigabytes}}' => '',
    '{nFormatted} {n, plural, =1{kibibyte} other{kibibytes}}' => '',
    '{nFormatted} {n, plural, =1{kilobyte} other{kilobytes}}' => '',
    '{nFormatted} {n, plural, =1{mebibyte} other{mebibytes}}' => '',
    '{nFormatted} {n, plural, =1{megabyte} other{megabytes}}' => '',
    '{nFormatted} {n, plural, =1{pebibyte} other{pebibytes}}' => '',
    '{nFormatted} {n, plural, =1{petabyte} other{petabytes}}' => '',
    '{nFormatted} {n, plural, =1{tebibyte} other{tebibytes}}' => '',
    '{nFormatted} {n, plural, =1{terabyte} other{terabytes}}' => '',
];
