<?php
/**
 * Message translations.
 *
 * This file is automatically generated by 'yii message' command.
 * It contains the localizable messages extracted from source code.
 * You may modify this file by translating the extracted messages.
 *
 * Each array element represents the translation (value) of a message (key).
 * If the value is empty, the message is considered as not translated.
 * Messages that no longer need translation will have their translations
 * enclosed between a pair of '@@' marks.
 *
 * Message string can be used with plural forms format. Check i18n section
 * of the guide for details.
 *
 * NOTE: this file must be saved in UTF-8 encoding.
 */
return array (
  '(not set)' => '(не е попълнено)',
  'An internal server error occurred.' => 'Възникна вътрешна грешка в сървъра.',
  'Are you sure you want to delete this item?' => 'Сигурни ли сте, че искате да изтриете записа?',
  'Delete' => 'Изтрий',
  'Error' => 'Грешка',
  'File upload failed.' => 'Грешка при качване на файл.',
  'Home' => 'Начало',
  'Invalid data received for parameter "{param}".' => 'Невалидни данни за параметъра "{param}".',
  'Login Required' => 'Трябва да влезете в системата.',
  'Missing required arguments: {params}' => 'Липсват задължителните аргументи: {params}',
  'Missing required parameters: {params}' => 'Липсват задължителните параметри: {params}',
  'No' => 'Не',
  'No help for unknown command "{command}".' => 'Няма помощна информация за команда "{command}".',
  'No help for unknown sub-command "{command}".' => 'Няма помощна информация под-команда "{command}".',
  'No results found.' => 'Няма намерени резултати.',
  'Only files with these MIME types are allowed: {mimeTypes}.' => 'Допускат се файлове са от типове: {mimeTypes}.',
  'Only files with these extensions are allowed: {extensions}.' => 'Допускат се файлове със следните разширения: {extensions}.',
  'Page not found.' => 'Страницата не беше намерена.',
  'Please fix the following errors:' => 'Моля, коригирайте следните грешки:',
  'Please upload a file.' => 'Моля, прикачете файл.',
  'Showing <b>{begin, number}-{end, number}</b> of <b>{totalCount, number}</b> {totalCount, plural, one{item} other{items}}.' => 'Показване на <b>{begin, number}-{end, number}</b> от <b>{totalCount, number}</b> {totalCount, plural, one{запис} other{записа}}.',
  'The file "{file}" is not an image.' => 'Файлът "{file}" не е изображение.',
  'The file "{file}" is too big. Its size cannot exceed {formattedLimit}.' => 'Фйлът "{file}" е твърде голям. Размерът на файла не трябва да превишава {formattedLimit}.',
  'The file "{file}" is too small. Its size cannot be smaller than {formattedLimit}.' => 'Файлът "{file}" е твърде малък. Размерът на файла трябва да е поне {formattedLimit}.',
  'The format of {attribute} is invalid.' => 'Невалиден формат за "{attribute}".',
  'The image "{file}" is too large. The height cannot be larger than {limit, number} {limit, plural, one{pixel} other{pixels}}.' => 'Изображението "{file}" е твърде голямо. Височината не трябва да е по-голяма от {limit, number} {limit, plural, one{пиксел} other{пиксела}}.',
  'The image "{file}" is too large. The width cannot be larger than {limit, number} {limit, plural, one{pixel} other{pixels}}.' => 'Изображението "{file}" е твърде голямо. Широчината не трябва да е по-голяма от {limit, number} {limit, plural, one{пиксел} other{пиксела}}.',
  'The image "{file}" is too small. The height cannot be smaller than {limit, number} {limit, plural, one{pixel} other{pixels}}.' => 'Изображението "{file}" е твърде малко. Височината трябва да е поне {limit, number} {limit, plural, one{пиксел} other{пиксела}}.',
  'The image "{file}" is too small. The width cannot be smaller than {limit, number} {limit, plural, one{pixel} other{pixels}}.' => 'Изображението "{file}" е твърде малко. Широчината трябва да е поне {limit, number} {limit, plural, one{пиксел} other{пиксела}}.',
  'The requested view "{name}" was not found.' => 'Завения view файл "{name}" не беше открит',
  'The verification code is incorrect.' => 'Неправилен код за проверка.',
  'Total <b>{count, number}</b> {count, plural, one{item} other{items}}.' => '<b>{count, number}</b> {count, plural, one{запис} other{записа}}.',
  'Unable to verify your data submission.' => 'Не може да се валидират подадените данни.',
  'Unknown command "{command}".' => 'Несъществуваща команда "{command}".',
  'Unknown option: --{name}' => 'Несъществуваща опция : --{name}',
  'Update' => 'Обнови',
  'View' => 'Виж',
  'Yes' => 'Да',
  'You are not allowed to perform this action.' => 'Нямате права да изпълните тази операция.',
  'You can upload at most {limit, number} {limit, plural, one{file} other{files}}.' => 'Може да прикачите най-много {limit, number} {limit, plural, one{файл} other{файла}}.',
  'in {delta, plural, =1{a day} other{# days}}' => 'след {delta, plural, =1{ден} other{# дни}}',
  'in {delta, plural, =1{a minute} other{# minutes}}' => 'след {delta, plural, =1{минута} other{# минути}}',
  'in {delta, plural, =1{a month} other{# months}}' => 'след {delta, plural, =1{месец} other{# месеца}}',
  'in {delta, plural, =1{a second} other{# seconds}}' => 'след {delta, plural, =1{секунда} other{# секунди}}',
  'in {delta, plural, =1{a year} other{# years}}' => 'след {delta, plural, =1{година} other{# години}}',
  'in {delta, plural, =1{an hour} other{# hours}}' => 'след {delta, plural, =1{час} other{# часа}}',
  'the input value' => 'стойността',
  '{attribute} "{value}" has already been taken.' => '{attribute} "{value}" вече е зает.',
  '{attribute} cannot be blank.' => 'Попълнете полето "{attribute}".',
  '{attribute} is invalid.' => 'Полето "{attribute}" е некоректно попълнено.',
  '{attribute} is not a valid URL.' => 'Полето "{attribute}" съдържа невалиден УЕБ адрес.',
  '{attribute} is not a valid email address.' => 'Полето "{attribute}" съдържа невалиден email адрес.',
  '{attribute} must be "{requiredValue}".' => 'Полето "{attribute}" трябва да съдържа "{requiredValue}".',
  '{attribute} must be a number.' => 'Полето "{attribute}" съдържа невалиден номер.',
  '{attribute} must be a string.' => 'Полето "{attribute}" трябва съдържа текст.',
  '{attribute} must be an integer.' => 'Полето "{attribute}" трябва да съдържа цяло число.',
  '{attribute} must be either "{true}" or "{false}".' => 'Полето "{attribute}"  трябва да бъде "{true}" или "{false}".',
  '{attribute} must be greater than "{compareValue}".' => 'Полето "{attribute}" трябва да е по-голямо от "{compareValue}".',
  '{attribute} must be greater than or equal to "{compareValue}".' => 'Полето "{attribute}" трябва да е по-големо или равно на «{compareValue}».',
  '{attribute} must be less than "{compareValue}".' => 'Полето "{attribute}" трябва да е по-малко от "{compareValue}".',
  '{attribute} must be less than or equal to "{compareValue}".' => 'Полето "{attribute}" трябва да е по-малко или равно на "{compareValue}".',
  '{attribute} must be no greater than {max}.' => 'Полето "{attribute}" не трябва да е по-голямо от {max}.',
  '{attribute} must be no less than {min}.' => 'Полето "{attribute}" не трябва да е по-малко от {min}.',
  '{attribute} must be repeated exactly.' => 'Полето "{attribute}" трябва да бъде повторено точно.',
  '{attribute} must not be equal to "{compareValue}".' => 'Полето «{attribute}» не трябва да е равно на "{compareValue}".',
  '{attribute} should contain at least {min, number} {min, plural, one{character} other{characters}}.' => 'Полето {attribute} трябва да съдържа поне {min, number} {min, plural, one{символ} other{символа}}.',
  '{attribute} should contain at most {max, number} {max, plural, one{character} other{characters}}.' => 'Полето "{attribute}" трябва да съдържа най-много {max, number} {max, plural, one{символ} other{символа}}.',
  '{attribute} should contain {length, number} {length, plural, one{character} other{characters}}.' => 'Полето "{attribute}" трябва да съдържа точно {length, number} {length, plural, one{символ} other{символа}}.',
  '{delta, plural, =1{a day} other{# days}} ago' => 'преди {delta, plural, =1{ден} other{# дни}}',
  '{delta, plural, =1{a minute} other{# minutes}} ago' => 'преди {delta, plural, =1{минута} other{# минути}}',
  '{delta, plural, =1{a month} other{# months}} ago' => 'преди {delta, plural, =1{месец} other{# месеца}}',
  '{delta, plural, =1{a second} other{# seconds}} ago' => 'преди {delta, plural, =1{секунда} other{# секунди}}',
  '{delta, plural, =1{a year} other{# years}} ago' => 'преди{delta, plural, =1{година} other{# години}}',
  '{delta, plural, =1{an hour} other{# hours}} ago' => '{delta, plural, =1{час} other{# часа}}',
  '{n, plural, =1{# byte} other{# bytes}}' => '{n, plural, =1{# байт} other{# байта}}',
  '{n, plural, =1{# gigabyte} other{# gigabytes}}' => '{n, plural, =1{# гигабайт} other{# гигабайта}}',
  '{n, plural, =1{# kilobyte} other{# kilobytes}}' => '{n, plural, =1{# килобайт} other{# килобайта}}',
  '{n, plural, =1{# megabyte} other{# megabytes}}' => '{n, plural, =1{# мегабайт} other{# мегабайта}}',
  '{n, plural, =1{# petabyte} other{# petabytes}}' => '{n, plural, =1{# петабайт} other{# петабайта}}',
  '{n, plural, =1{# terabyte} other{# terabytes}}' => '{n, plural, =1{# терабайт} other{# терабайта}}',
  '{n} B' => '{n} Б',
  '{n} GB' => '{n} ГБ',
  '{n} KB' => '{n} КБ',
  '{n} MB' => '{n} МБ',
  '{n} PB' => '{n} ПБ',
  '{n} TB' => '{n} ТБ',
);
