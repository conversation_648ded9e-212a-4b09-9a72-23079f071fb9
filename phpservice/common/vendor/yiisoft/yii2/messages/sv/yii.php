<?php
/**
 * Message translations.
 *
 * This file is automatically generated by 'yii message' command.
 * It contains the localizable messages extracted from source code.
 * You may modify this file by translating the extracted messages.
 *
 * Each array element represents the translation (value) of a message (key).
 * If the value is empty, the message is considered as not translated.
 * Messages that no longer need translation will have their translations
 * enclosed between a pair of '@@' marks.
 *
 * Message string can be used with plural forms format. Check i18n section
 * of the guide for details.
 *
 * NOTE: this file must be saved in UTF-8 encoding.
 */
return [
    '(not set)' => '(ej satt)',
    'An internal server error occurred.' => 'Ett internt serverfel har inträffat.',
    'Are you sure you want to delete this item?' => 'Är du säker på att du vill radera objektet?',
    'Delete' => 'Radera',
    'Error' => 'Error',
    'File upload failed.' => 'Uppladdningen misslyckades.',
    'Home' => 'Hem',
    'Invalid data received for parameter "{param}".' => 'Ogiltig data har mottagits för parameter "{param}".',
    'Login Required' => 'Inloggning krävs',
    'Missing required arguments: {params}' => 'Följande begärda variabler saknas: {params}',
    'Missing required parameters: {params}' => 'Följande begärda parametrar saknas: {params}',
    'No' => 'Nej',
    'No results found.' => 'Inga resultat hittades.',
    'Only files with these MIME types are allowed: {mimeTypes}.' => 'Endast filer med följande MIME-typer är tillåtna: {mimeTypes}',
    'Only files with these extensions are allowed: {extensions}.' => 'Endast filer med följande filnamnstillägg är tillåtna: {extensions}',
    'Page not found.' => 'Sidan hittades inte.',
    'Please fix the following errors:' => 'Var god fixa följande fel:',
    'Please upload a file.' => 'Var god ladda upp en fil.',
    'Showing <b>{begin, number}-{end, number}</b> of <b>{totalCount, number}</b> {totalCount, plural, one{item} other{items}}.' => 'Visar <b>{begin, number}-{end, number}</b> av <b>{totalCount, number}</b> objekt.',
    'The file "{file}" is not an image.' => 'Filen "{file}" är inte en bild.',
    'The file "{file}" is too big. Its size cannot exceed {formattedLimit}.' => 'Filen "{file}" är för stor. Filstorleken får inte överskrida {formattedLimit}.',
    'The file "{file}" is too small. Its size cannot be smaller than {formattedLimit}.' => 'Filen "{file}" är för liten. Filstorleken måste vara minst {formattedLimit}.',
    'The format of {attribute} is invalid.' => 'Formatet för "{attribute}" är ogiltigt.',
    'The image "{file}" is too large. The height cannot be larger than {limit, number} {limit, plural, one{pixel} other{pixels}}.' => 'Bilden "{file}" är för stor. Höjden får inte överskrida {limit, number} {limit, plural, =1{pixel} other{pixlar}}.',
    'The image "{file}" is too large. The width cannot be larger than {limit, number} {limit, plural, one{pixel} other{pixels}}.' => 'Bilden "{file}" är för stor. Bredden får inte överskrida {limit, number} {limit, plural, =1{pixel} other{pixlar}}.',
    'The image "{file}" is too small. The height cannot be smaller than {limit, number} {limit, plural, one{pixel} other{pixels}}.' => 'Bilden "{file}" är för liten. Bilden måste vara minst {limit, number} {limit, plural, =1{pixel} other{pixlar}} hög.',
    'The image "{file}" is too small. The width cannot be smaller than {limit, number} {limit, plural, one{pixel} other{pixels}}.' => 'Bilden "{file}" är för liten. Bilden måste vara minst {limit, number} {limit, plural, =1{pixel} other{pixlar}} bred.',
    'The requested view "{name}" was not found.' => 'Den begärda vyn "{name}" kunde inte hittas.',
    'The verification code is incorrect.' => 'Verifieringskoden är felaktig.',
    'Total <b>{count, number}</b> {count, plural, one{item} other{items}}.' => 'Totalt <b>{count, number}</b> objekt.',
    'Unable to verify your data submission.' => 'Det gick inte att verifiera skickad data.',
    'Unknown option: --{name}' => 'Okänt alternativ: --{name}',
    'Update' => 'Uppdatera',
    'View' => 'Visa',
    'Yes' => 'Ja',
    'You are not allowed to perform this action.' => 'Du har inte behörighet att utföra den här åtgärden.',
    'You can upload at most {limit, number} {limit, plural, one{file} other{files}}.' => 'Du får inte ladda upp mer än {limit, number} {limit, plural, =1{fil} other{filer}}.',
    'in {delta, plural, =1{a day} other{# days}}' => 'under {delta, plural, =1{en dag} other{# dagar}}',
    'in {delta, plural, =1{a minute} other{# minutes}}' => 'under {delta, plural, =1{en minut} other{# minuter}}',
    'in {delta, plural, =1{a month} other{# months}}' => 'under {delta, plural, =1{en månad} other{# månader}}',
    'in {delta, plural, =1{a second} other{# seconds}}' => 'under {delta, plural, =1{en sekund} other{# sekunder}}',
    'in {delta, plural, =1{a year} other{# years}}' => 'under {delta, plural, =1{ett år} other{# år}}',
    'in {delta, plural, =1{an hour} other{# hours}}' => 'under {delta, plural, =1{en timme} other{# timmar}}',
    'just now' => 'just nu',
    'the input value' => 'inmatningsvärdet',
    '{attribute} "{value}" has already been taken.' => '{attribute} "{value}" används redan.',
    '{attribute} cannot be blank.' => 'Värdet för {attribute} får inte vara tomt.',
    '{attribute} is invalid.' => 'Värdet för {attribute} är ogiltigt.',
    '{attribute} is not a valid URL.' => '{attribute} är inte en giltig URL.',
    '{attribute} is not a valid email address.' => '{attribute} är inte en giltig emailadress.',
    '{attribute} must be "{requiredValue}".' => '{attribute} måste vara satt till "{requiredValue}".',
    '{attribute} must be a number.' => '{attribute} måste vara ett nummer.',
    '{attribute} must be a string.' => '{attribute} måste vara en sträng.',
    '{attribute} must be an integer.' => '{attribute} måste vara ett heltal.',
    '{attribute} must be either "{true}" or "{false}".' => '{attribute} måste vara satt till antingen "{true}" eller "{false}".',
    '{attribute} must be greater than "{compareValue}".' => '{attribute} måste vara större än "{compareValue}".',
    '{attribute} must be greater than or equal to "{compareValue}".' => '{attribute} måste vara större än eller lika med "{compareValue}".',
    '{attribute} must be less than "{compareValue}".' => '{attribute} måste vara mindre än "{compareValue}".',
    '{attribute} must be less than or equal to "{compareValue}".' => '{attribute} måste vara mindre än eller lika med "{compareValue}".',
    '{attribute} must be no greater than {max}.' => '{attribute} får inte överskrida {max}.',
    '{attribute} must be no less than {min}.' => '{attribute} får som minst vara {min}.',
    '{attribute} must be repeated exactly.' => '{attribute} måste upprepas exakt.',
    '{attribute} must not be equal to "{compareValue}".' => '{attribute} får inte vara satt till "{compareValue}".',
    '{attribute} should contain at least {min, number} {min, plural, one{character} other{characters}}.' => '{attribute} bör innehålla minst {min, number} tecken.',
    '{attribute} should contain at most {max, number} {max, plural, one{character} other{characters}}.' => '{attribute} bör innehålla max {max, number} tecken.',
    '{attribute} should contain {length, number} {length, plural, one{character} other{characters}}.' => '{attribute} bör innehålla {length, number} tecken.',
    '{delta, plural, =1{a day} other{# days}} ago' => '{delta, plural, =1{en dag} other{# dagar}} sedan',
    '{delta, plural, =1{a minute} other{# minutes}} ago' => '{delta, plural, =1{en minut} other{# minuter}} sedan',
    '{delta, plural, =1{a month} other{# months}} ago' => '{delta, plural, =1{en månad} other{# månader}} sedan',
    '{delta, plural, =1{a second} other{# seconds}} ago' => '{delta, plural, =1{en sekund} other{# sekunder}} sedan',
    '{delta, plural, =1{a year} other{# years}} ago' => '{delta, plural, =1{ett år} other{# år}} sedan',
    '{delta, plural, =1{an hour} other{# hours}} ago' => '{delta, plural, =1{en timme} other{# timmar}} sedan',
    '{nFormatted} B' => '{nFormatted} B',
    '{nFormatted} GB' => '{nFormatted} GB',
    '{nFormatted} GiB' => '{nFormatted} GiB',
    '{nFormatted} KB' => '{nFormatted} KB',
    '{nFormatted} KiB' => '{nFormatted} KiB',
    '{nFormatted} MB' => '{nFormatted} MB',
    '{nFormatted} MiB' => '{nFormatted} MiB',
    '{nFormatted} PB' => '{nFormatted} PB',
    '{nFormatted} PiB' => '{nFormatted} PiB',
    '{nFormatted} TB' => '{nFormatted} TB',
    '{nFormatted} TiB' => '{nFormatted} TiB',
    '{nFormatted} {n, plural, =1{byte} other{bytes}}' => '{nFormatted} {n, plural, =1{byte} other{byte}}',
    '{nFormatted} {n, plural, =1{gibibyte} other{gibibytes}}' => '{nFormatted} {n, plural, =1{gibibyte} other{gibibyte}}',
    '{nFormatted} {n, plural, =1{gigabyte} other{gigabytes}}' => '{nFormatted} {n, plural, =1{gigabyte} other{gigabyte}}',
    '{nFormatted} {n, plural, =1{kibibyte} other{kibibytes}}' => '{nFormatted} {n, plural, =1{kibibyte} other{kibibyte}}',
    '{nFormatted} {n, plural, =1{kilobyte} other{kilobytes}}' => '{nFormatted} {n, plural, =1{kilobyte} other{kilobyte}}',
    '{nFormatted} {n, plural, =1{mebibyte} other{mebibytes}}' => '{nFormatted} {n, plural, =1{mebibyte} other{mebibyte}}',
    '{nFormatted} {n, plural, =1{megabyte} other{megabytes}}' => '{nFormatted} {n, plural, =1{megabyte} other{megabyte}}',
    '{nFormatted} {n, plural, =1{pebibyte} other{pebibytes}}' => '{nFormatted} {n, plural, =1{pebibyte} other{pebibyte}}',
    '{nFormatted} {n, plural, =1{petabyte} other{petabytes}}' => '{nFormatted} {n, plural, =1{petabyte} other{petabyte}}',
    '{nFormatted} {n, plural, =1{tebibyte} other{tebibytes}}' => '{nFormatted} {n, plural, =1{tebibyte} other{tebibyte}}',
    '{nFormatted} {n, plural, =1{terabyte} other{terabytes}}' => '{nFormatted} {n, plural, =1{terabyte} other{terabyte}}',
];
