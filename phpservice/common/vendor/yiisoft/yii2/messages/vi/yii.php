<?php
/**
 * Message translations.
 *
 * This file is automatically generated by 'yii message' command.
 * It contains the localizable messages extracted from source code.
 * You may modify this file by translating the extracted messages.
 *
 * Each array element represents the translation (value) of a message (key).
 * If the value is empty, the message is considered as not translated.
 * Messages that no longer need translation will have their translations
 * enclosed between a pair of '@@' marks.
 *
 * Message string can be used with plural forms format. Check i18n section
 * of the guide for details.
 *
 * NOTE: this file must be saved in UTF-8 encoding.
 */
return [
    '(not set)' => '(không có)',
    'An internal server error occurred.' => 'Máy chủ đã gặp sự cố nội bộ.',
    'Are you sure you want to delete this item?' => 'Bạn có chắc là sẽ xóa mục này không?',
    'Delete' => 'Xóa',
    'Error' => 'Lỗi',
    'File upload failed.' => 'Không tải được file lên.',
    'Home' => 'Trang chủ',
    'Invalid data received for parameter "{param}".' => '<PERSON><PERSON> liệu của tham số "{param}" không hợp lệ.',
    'Login Required' => 'Yêu cầu đăng nhập',
    'Missing required arguments: {params}' => 'Thiếu đối số: {params}',
    'Missing required parameters: {params}' => 'Thiếu tham số: {params}',
    'No' => 'Không',
    'No help for unknown command "{command}".' => 'Không có trợ giúp cho lệnh không rõ "{command}"',
    'No help for unknown sub-command "{command}".' => 'Không có trợ giúp cho tiểu lệnh không rõ "{command}"',
    'No results found.' => 'Không tìm thấy kết quả nào.',
    'Only files with these MIME types are allowed: {mimeTypes}.' => 'Chỉ các file với kiểu MIME sau đây được phép tải lên: {mimeTypes}',
    'Only files with these extensions are allowed: {extensions}.' => 'Chỉ các file với phần mở rộng sau đây được phép tải lên: {extensions}',
    'Page not found.' => 'Không tìm thấy trang.',
    'Please fix the following errors:' => 'Vui lòng sửa các lỗi sau đây:',
    'Please upload a file.' => 'Hãy tải file lên.',
    'Showing <b>{begin, number}-{end, number}</b> of <b>{totalCount, number}</b> {totalCount, plural, one{item} other{items}}.' => 'Trình bày <b>{begin, number}-{end, number}</b> trong số <b>{totalCount, number}</b> mục.',
    'The file "{file}" is not an image.' => 'File "{file}" phải là một ảnh.',
    'The file "{file}" is too big. Its size cannot exceed {formattedLimit}.' => 'File "{file}" quá lớn. Kích cỡ tối đa được phép tải lên là {formattedLimit}.',
    'The file "{file}" is too small. Its size cannot be smaller than {formattedLimit}.' => 'File "{file}" quá nhỏ. Kích cỡ tối thiểu được phép tải lên là {formattedLimit}.',
    'The format of {attribute} is invalid.' => 'Định dạng của {attribute} không hợp lệ.',
    'The image "{file}" is too large. The height cannot be larger than {limit, number} {limit, plural, one{pixel} other{pixels}}.' => 'File "{file}" có kích thước quá lớn. Chiều cao tối đa được phép là {limit, number} pixel.',
    'The image "{file}" is too large. The width cannot be larger than {limit, number} {limit, plural, one{pixel} other{pixels}}.' => 'Ảnh "{file}" có kích thước quá lớn. Chiều rộng tối đa được phép là {limit, number} pixel.',
    'The image "{file}" is too small. The height cannot be smaller than {limit, number} {limit, plural, one{pixel} other{pixels}}.' => 'Ảnh "{file}" quá nhỏ. Chiều cao của ảnh không được phép nhỏ hơn {limit, number} pixel.',
    'The image "{file}" is too small. The width cannot be smaller than {limit, number} {limit, plural, one{pixel} other{pixels}}.' => 'Ảnh "{file}" quá nhỏ. Chiều rộng của ảnh không được phép nhỏ hơn {limit, number} pixel.',
    'The requested view "{name}" was not found.' => 'Không tìm thấy file view "{name}"',
    'The verification code is incorrect.' => 'Mã xác thực không đúng.',
    'Total <b>{count, number}</b> {count, plural, one{item} other{items}}.' => 'Tổng số <b>{count, number}</b> mục.',
    'Unable to verify your data submission.' => 'Không kiểm tra được dữ liệu đã gửi lên.',
    'Unknown command "{command}".' => 'Lệnh không biết "{command}"',
    'Unknown option: --{name}' => 'Tùy chọn không biết: --{name}',
    'Update' => 'Sửa',
    'View' => 'Xem',
    'Yes' => 'Có',
    'You are not allowed to perform this action.' => 'Bạn không được phép truy cập chức năng này.',
    'You can upload at most {limit, number} {limit, plural, one{file} other{files}}.' => 'Chỉ có thể tải lên tối đa {limit, number} file.',
    'in {delta, plural, =1{a day} other{# days}}' => 'trong {delta} ngày',
    'in {delta, plural, =1{a minute} other{# minutes}}' => 'trong {delta} phút',
    'in {delta, plural, =1{a month} other{# months}}' => 'trong {delta} tháng',
    'in {delta, plural, =1{a second} other{# seconds}}' => 'trong {delta} giây',
    'in {delta, plural, =1{a year} other{# years}}' => 'trong {delta} năm',
    'in {delta, plural, =1{an hour} other{# hours}}' => 'trong {delta} giờ',
    'the input value' => 'giá trị đã nhập',
    '{attribute} "{value}" has already been taken.' => '{attribute} "{value}" đã bị sử dụng.',
    '{attribute} cannot be blank.' => '{attribute} không được để trống.',
    '{attribute} is invalid.' => '{attribute} không hợp lệ.',
    '{attribute} is not a valid URL.' => '{attribute} không phải là URL hợp lệ.',
    '{attribute} is not a valid email address.' => '{attribute} không phải là địa chỉ email hợp lệ.',
    '{attribute} must be "{requiredValue}".' => '{attribute} phải là "{requiredValue}"',
    '{attribute} must be a number.' => '{attribute} phải là số.',
    '{attribute} must be a string.' => '{attribute} phải là chuỗi.',
    '{attribute} must be an integer.' => '{attribute} phải là số nguyên.',
    '{attribute} must be either "{true}" or "{false}".' => '{attribute} phải là "{true}" hoặc "{false}".',
    '{attribute} must be greater than "{compareValue}".' => '{attribute} phải lớn hơn "{compareValue}"',
    '{attribute} must be greater than or equal to "{compareValue}".' => '{attribute} phải lớn hơn hoặc bằng "{compareValue}"',
    '{attribute} must be less than "{compareValue}".' => '{attribute} phải nhỏ hơn "{compareValue}"',
    '{attribute} must be less than or equal to "{compareValue}".' => '{attribute} phải nhỏ hơn hoặc bằng "{compareValue}"',
    '{attribute} must be no greater than {max}.' => '{attribute} không được lớn hơn {max}.',
    '{attribute} must be no less than {min}.' => '{attribute} không được nhỏ hơn {min}',
    '{attribute} must be repeated exactly.' => '{attribute} phải lặp lại chính xác.',
    '{attribute} must not be equal to "{compareValue}".' => '{attribute} không được phép bằng "{compareValue}"',
    '{attribute} should contain at least {min, number} {min, plural, one{character} other{characters}}.' => '{attribute} phải chứa ít nhất {min, number} ký tự.',
    '{attribute} should contain at most {max, number} {max, plural, one{character} other{characters}}.' => '{attribute} phải chứa nhiều nhất {max, number} ký tự.',
    '{attribute} should contain {length, number} {length, plural, one{character} other{characters}}.' => '{attribute} phải bao gồm {length, number} ký tự.',
    '{delta, plural, =1{a day} other{# days}} ago' => '{delta} ngày trước',
    '{delta, plural, =1{a minute} other{# minutes}} ago' => '{delta} phút trước',
    '{delta, plural, =1{a month} other{# months}} ago' => '{delta} tháng trước',
    '{delta, plural, =1{a second} other{# seconds}} ago' => '{delta} giây trước',
    '{delta, plural, =1{a year} other{# years}} ago' => '{delta} năm trước',
    '{delta, plural, =1{an hour} other{# hours}} ago' => '{delta} giờ trước',
    '{n, plural, =1{# byte} other{# bytes}}' => '{n} byte',
    '{n, plural, =1{# gigabyte} other{# gigabytes}}' => '{n} gigabyte',
    '{n, plural, =1{# kilobyte} other{# kilobytes}}' => '{n} kilobyte',
    '{n, plural, =1{# megabyte} other{# megabytes}}' => '{n} megabyte',
    '{n, plural, =1{# petabyte} other{# petabytes}}' => '{n} petabyte',
    '{n, plural, =1{# terabyte} other{# terabytes}}' => '{n} terabyte',
    '{n} B' => '{n} B',
    '{n} GB' => '{n} GB',
    '{n} KB' => '{n} KB',
    '{n} MB' => '{n} MB',
    '{n} PB' => '{n} PB',
    '{n} TB' => '{n} TB',
];
