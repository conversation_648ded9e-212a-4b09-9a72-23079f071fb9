<?php
/**
 * Message translations.
 *
 * This file is automatically generated by 'yii message' command.
 * It contains the localizable messages extracted from source code.
 * You may modify this file by translating the extracted messages.
 *
 * Each array element represents the translation (value) of a message (key).
 * If the value is empty, the message is considered as not translated.
 * Messages that no longer need translation will have their translations
 * enclosed between a pair of '@@' marks.
 *
 * Message string can be used with plural forms format. Check i18n section
 * of the guide for details.
 *
 * NOTE: this file must be saved in UTF-8 encoding.
 */
return [
    '{attribute} must be equal to "{compareValueOrAttribute}".' => 'Значение «{attribute}» должно быть равно «{compareValueOrAttribute}».',
    '{attribute} must be greater than "{compareValueOrAttribute}".' => 'Значение «{attribute}» должно быть больше значения «{compareValueOrAttribute}».',
    '{attribute} must be greater than or equal to "{compareValueOrAttribute}".' => 'Значение «{attribute}» должно быть больше или равно значения «{compareValueOrAttribute}».',
    '{attribute} must be less than "{compareValueOrAttribute}".' => 'Значение «{attribute}» должно быть меньше значения «{compareValueOrAttribute}».',
    '{attribute} must be less than or equal to "{compareValueOrAttribute}".' => 'Значение «{attribute}» должно быть меньше или равно значения «{compareValueOrAttribute}».',
    '{attribute} must not be equal to "{compareValueOrAttribute}".' => 'Значение «{attribute}» не должно быть равно «{compareValueOrAttribute}».',
    '(not set)' => '(не задано)',
    'An internal server error occurred.' => 'Возникла внутренняя ошибка сервера.',
    'Are you sure you want to delete this item?' => 'Вы уверены, что хотите удалить этот элемент?',
    'Delete' => 'Удалить',
    'Error' => 'Ошибка',
    'File upload failed.' => 'Загрузка файла не удалась.',
    'Home' => 'Главная',
    'Invalid data received for parameter "{param}".' => 'Неправильное значение параметра "{param}".',
    'Login Required' => 'Требуется вход.',
    'Missing required arguments: {params}' => 'Отсутствуют обязательные аргументы: {params}',
    'Missing required parameters: {params}' => 'Отсутствуют обязательные параметры: {params}',
    'No' => 'Нет',
    'No results found.' => 'Ничего не найдено.',
    'Only files with these MIME types are allowed: {mimeTypes}.' => 'Разрешена загрузка файлов только со следующими MIME-типами: {mimeTypes}.',
    'Only files with these extensions are allowed: {extensions}.' => 'Разрешена загрузка файлов только со следующими расширениями: {extensions}.',
    'Page not found.' => 'Страница не найдена.',
    'Please fix the following errors:' => 'Исправьте следующие ошибки:',
    'Please upload a file.' => 'Загрузите файл.',
    'Showing <b>{begin, number}-{end, number}</b> of <b>{totalCount, number}</b> {totalCount, plural, one{item} other{items}}.' => 'Показаны записи <b>{begin, number}-{end, number}</b> из <b>{totalCount, number}</b>.',
    'The file "{file}" is not an image.' => 'Файл «{file}» не является изображением.',
    'The file "{file}" is too big. Its size cannot exceed {formattedLimit}.' => 'Файл «{file}» слишком большой. Размер не должен превышать {formattedLimit}.',
    'The file "{file}" is too small. Its size cannot be smaller than {formattedLimit}.' => 'Файл «{file}» слишком маленький. Размер должен быть более {formattedLimit}.',
    'The format of {attribute} is invalid.' => 'Неверный формат значения «{attribute}».',
    'The image "{file}" is too large. The height cannot be larger than {limit, number} {limit, plural, one{pixel} other{pixels}}.' => 'Файл «{file}» слишком большой. Высота не должна превышать {limit, number} {limit, plural, one{пиксель} few{пикселя} many{пикселей} other{пикселя}}.',
    'The image "{file}" is too large. The width cannot be larger than {limit, number} {limit, plural, one{pixel} other{pixels}}.' => 'Файл «{file}» слишком большой. Ширина не должна превышать {limit, number} {limit, plural, one{пиксель} few{пикселя} many{пикселей} other{пикселя}}.',
    'The image "{file}" is too small. The height cannot be smaller than {limit, number} {limit, plural, one{pixel} other{pixels}}.' => 'Файл «{file}» слишком маленький. Высота должна быть более {limit, number} {limit, plural, one{пиксель} few{пикселя} many{пикселей} other{пикселя}}.',
    'The image "{file}" is too small. The width cannot be smaller than {limit, number} {limit, plural, one{pixel} other{pixels}}.' => 'Файл «{file}» слишком маленький. Ширина должна быть более {limit, number} {limit, plural, one{пиксель} few{пикселя} many{пикселей} other{пикселя}}.',
    'The requested view "{name}" was not found.' => 'Запрашиваемый файл представления "{name}" не найден.',
    'The verification code is incorrect.' => 'Неправильный проверочный код.',
    'Total <b>{count, number}</b> {count, plural, one{item} other{items}}.' => 'Всего <b>{count, number}</b> {count, plural, one{запись} few{записи} many{записей} other{записи}}.',
    'Unable to verify your data submission.' => 'Не удалось проверить переданные данные.',
    'Unknown option: --{name}' => 'Неизвестная опция: --{name}',
    'Update' => 'Редактировать',
    'View' => 'Просмотр',
    'Yes' => 'Да',
    'You are not allowed to perform this action.' => 'Вам не разрешено производить данное действие.',
    'You can upload at most {limit, number} {limit, plural, one{file} other{files}}.' => 'Вы не можете загружать более {limit, number} {limit, plural, one{файла} few{файлов} many{файлов} other{файла}}.',
    'in {delta, plural, =1{a day} other{# days}}' => 'через {delta, plural, =1{день} one{# день} few{# дня} many{# дней} other{# дня}}',
    'in {delta, plural, =1{a minute} other{# minutes}}' => 'через {delta, plural, =1{минуту} one{# минуту} few{# минуты} many{# минут} other{# минуты}}',
    'in {delta, plural, =1{a month} other{# months}}' => 'через {delta, plural, =1{месяц} one{# месяц} few{# месяца} many{# месяцев} other{# месяца}}',
    'in {delta, plural, =1{a second} other{# seconds}}' => 'через {delta, plural, =1{секунду} one{# секунду} few{# секунды} many{# секунд} other{# секунды}}',
    'in {delta, plural, =1{a year} other{# years}}' => 'через {delta, plural, =1{год} one{# год} few{# года} many{# лет} other{# года}}',
    'in {delta, plural, =1{an hour} other{# hours}}' => 'через {delta, plural, =1{час} one{# час} few{# часа} many{# часов} other{# часа}}',
    'just now' => 'прямо сейчас',
    'the input value' => 'введённое значение',
    '{attribute} "{value}" has already been taken.' => 'Значение «{value}» для «{attribute}» уже занято.',
    '{attribute} cannot be blank.' => 'Необходимо заполнить «{attribute}».',
    '{attribute} contains wrong subnet mask.' => 'Значение «{attribute}» содержит неверную маску подсети.',
    '{attribute} is invalid.' => 'Значение «{attribute}» неверно.',
    '{attribute} is not a valid URL.' => 'Значение «{attribute}» не является правильным URL.',
    '{attribute} is not a valid email address.' => 'Значение «{attribute}» не является правильным email адресом.',
    '{attribute} is not in the allowed range.' => 'Значение «{attribute}» не входит в список разрешенных диапазонов адресов.',
    '{attribute} must be "{requiredValue}".' => 'Значение «{attribute}» должно быть равно «{requiredValue}».',
    '{attribute} must be a number.' => 'Значение «{attribute}» должно быть числом.',
    '{attribute} must be a string.' => 'Значение «{attribute}» должно быть строкой.',
    '{attribute} must be a valid IP address.' => 'Значение «{attribute}» должно быть правильным IP адресом.',
    '{attribute} must be an IP address with specified subnet.' => 'Значение «{attribute}» должно быть IP адресом с подсетью.',
    '{attribute} must be an integer.' => 'Значение «{attribute}» должно быть целым числом.',
    '{attribute} must be either "{true}" or "{false}".' => 'Значение «{attribute}» должно быть равно «{true}» или «{false}».',
    '{attribute} must be no greater than {max}.' => 'Значение «{attribute}» не должно превышать {max}.',
    '{attribute} must be no less than {min}.' => 'Значение «{attribute}» должно быть не меньше {min}.',
    '{attribute} must not be a subnet.' => 'Значение «{attribute}» не должно быть подсетью.',
    '{attribute} must not be an IPv4 address.' => 'Значение «{attribute}» не должно быть IPv4 адресом.',
    '{attribute} must not be an IPv6 address.' => 'Значение «{attribute}» не должно быть IPv6 адресом.',
    '{attribute} should contain at least {min, number} {min, plural, one{character} other{characters}}.' => 'Значение «{attribute}» должно содержать минимум {min, number} {min, plural, one{символ} few{символа} many{символов} other{символа}}.',
    '{attribute} should contain at most {max, number} {max, plural, one{character} other{characters}}.' => 'Значение «{attribute}» должно содержать максимум {max, number} {max, plural, one{символ} few{символа} many{символов} other{символа}}.',
    '{attribute} should contain {length, number} {length, plural, one{character} other{characters}}.' => 'Значение «{attribute}» должно содержать {length, number} {length, plural, one{символ} few{символа} many{символов} other{символа}}.',
    '{delta, plural, =1{1 day} other{# days}}' => '{delta, plural, one{# день} few{# дня} many{# дней} other{# дней}}',
    '{delta, plural, =1{1 hour} other{# hours}}' => '{delta, plural, one{# час} few{# часа} many{# часов} other{# часов}}',
    '{delta, plural, =1{1 minute} other{# minutes}}' => '{delta, plural, one{# минута} few{# минуты} many{# минут} other{# минут}}',
    '{delta, plural, =1{1 month} other{# months}}' => '{delta, plural, one{# месяц} few{# месяца} many{# месяцев} other{# месяцев}}',
    '{delta, plural, =1{1 second} other{# seconds}}' => '{delta, plural, one{# секунда} few{# секунды} many{# секунд} other{# секунд}}',
    '{delta, plural, =1{1 year} other{# years}}' => '{delta, plural, one{# год} few{# года} many{# лет} other{# лет}}',
    '{delta, plural, =1{a day} other{# days}} ago' => '{delta, plural, =1{день} one{# день} few{# дня} many{# дней} other{# дня}} назад',
    '{delta, plural, =1{a minute} other{# minutes}} ago' => '{delta, plural, =1{минуту} one{# минуту} few{# минуты} many{# минут} other{# минуты}} назад',
    '{delta, plural, =1{a month} other{# months}} ago' => '{delta, plural, =1{месяц} one{# месяц} few{# месяца} many{# месяцев} other{# месяца}} назад',
    '{delta, plural, =1{a second} other{# seconds}} ago' => '{delta, plural, =1{секунду} one{# секунду} few{# секунды} many{# секунд} other{# секунды}} назад',
    '{delta, plural, =1{a year} other{# years}} ago' => '{delta, plural, =1{год} one{# год} few{# года} many{# лет} other{# года}} назад',
    '{delta, plural, =1{an hour} other{# hours}} ago' => '{delta, plural, =1{час} one{# час} few{# часа} many{# часов} other{# часа}} назад',
    '{nFormatted} B' => '{nFormatted} Б',
    '{nFormatted} GB' => '{nFormatted} ГБ',
    '{nFormatted} GiB' => '{nFormatted} ГиБ',
    '{nFormatted} KB' => '{nFormatted} КБ',
    '{nFormatted} KiB' => '{nFormatted} КиБ',
    '{nFormatted} MB' => '{nFormatted} МБ',
    '{nFormatted} MiB' => '{nFormatted} МиБ',
    '{nFormatted} PB' => '{nFormatted} ПБ',
    '{nFormatted} PiB' => '{nFormatted} ПиБ',
    '{nFormatted} TB' => '{nFormatted} ТБ',
    '{nFormatted} TiB' => '{nFormatted} ТиБ',
    '{nFormatted} {n, plural, =1{byte} other{bytes}}' => '{nFormatted} {n, plural, one{байт} few{байта} many{байтов} other{байта}}',
    '{nFormatted} {n, plural, =1{gibibyte} other{gibibytes}}' => '{nFormatted} {n, plural, one{гибибайт} few{гибибайта} many{гибибайтов} other{гибибайта}}',
    '{nFormatted} {n, plural, =1{gigabyte} other{gigabytes}}' => '{nFormatted} {n, plural, one{гигабайт} few{гигабайта} many{гигабайтов} other{гигабайта}}',
    '{nFormatted} {n, plural, =1{kibibyte} other{kibibytes}}' => '{nFormatted} {n, plural, one{кибибайт} few{кибибайта} many{кибибайтов} other{кибибайта}}',
    '{nFormatted} {n, plural, =1{kilobyte} other{kilobytes}}' => '{nFormatted} {n, plural, one{килобайт} few{килобайта} many{килобайтов} other{килобайта}}',
    '{nFormatted} {n, plural, =1{mebibyte} other{mebibytes}}' => '{nFormatted} {n, plural, one{мебибайт} few{мебибайта} many{мебибайтов} other{мебибайта}}',
    '{nFormatted} {n, plural, =1{megabyte} other{megabytes}}' => '{nFormatted} {n, plural, one{мегабайт} few{мегабайта} many{мегабайтов} other{мегабайта}}',
    '{nFormatted} {n, plural, =1{pebibyte} other{pebibytes}}' => '{nFormatted} {n, plural, one{пебибайт} few{пебибайта} many{пебибайтов} other{пебибайта}}',
    '{nFormatted} {n, plural, =1{petabyte} other{petabytes}}' => '{nFormatted} {n, plural, one{петабайт} few{петабайта} many{петабайтов} other{петабайта}}',
    '{nFormatted} {n, plural, =1{tebibyte} other{tebibytes}}' => '{nFormatted} {n, plural, one{тебибайт} few{тебибайта} many{тебибайтов} other{тебибайта}}',
    '{nFormatted} {n, plural, =1{terabyte} other{terabytes}}' => '{nFormatted} {n, plural, one{терабайт} few{терабайта} many{терабайтов} other{терабайта}}',
];
