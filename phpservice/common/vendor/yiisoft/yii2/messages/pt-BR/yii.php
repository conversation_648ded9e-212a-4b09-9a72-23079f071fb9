<?php
/**
 * Message translations.
 *
 * This file is automatically generated by 'yii message' command.
 * It contains the localizable messages extracted from source code.
 * You may modify this file by translating the extracted messages.
 *
 * Each array element represents the translation (value) of a message (key).
 * If the value is empty, the message is considered as not translated.
 * Messages that no longer need translation will have their translations
 * enclosed between a pair of '@@' marks.
 *
 * Message string can be used with plural forms format. Check i18n section
 * of the guide for details.
 *
 * NOTE: this file must be saved in UTF-8 encoding.
 */
return [
    'in {delta, plural, =1{a day} other{# days}}' => 'em {delta, plural, =1{1 dia} other{# dias}}',
    'in {delta, plural, =1{a minute} other{# minutes}}' => 'em {delta, plural, =1{1 minuto} other{# minutos}}',
    'in {delta, plural, =1{a month} other{# months}}' => 'em {delta, plural, =1{1 mês} other{# meses}}',
    'in {delta, plural, =1{a second} other{# seconds}}' => 'em {delta, plural, =1{1 segundo} other{# segundos}}',
    'in {delta, plural, =1{a year} other{# years}}' => 'em {delta, plural, =1{1 ano} other{# anos}}',
    'in {delta, plural, =1{an hour} other{# hours}}' => 'em {delta, plural, =1{1 hora} other{# horas}}',
    '{delta, plural, =1{a day} other{# days}} ago' => '{delta, plural, =1{há 1 dia} other{há # dias}}',
    '{delta, plural, =1{a minute} other{# minutes}} ago' => '{delta, plural, =1{há 1 minuto} other{há # minutos}}',
    '{delta, plural, =1{a month} other{# months}} ago' => '{delta, plural, =1{há 1 mês} other{há # meses}}',
    '{delta, plural, =1{a second} other{# seconds}} ago' => '{delta, plural, =1{há 1 segundo} other{há # segundos}}',
    '{delta, plural, =1{a year} other{# years}} ago' => '{delta, plural, =1{há 1 ano} other{há # anos}}',
    '{delta, plural, =1{an hour} other{# hours}} ago' => '{delta, plural, =1{há 1 hora} other{há # horas}}',
    '{nFormatted} B' => '{nFormatted} B',
    '{nFormatted} GB' => '{nFormatted} GB',
    '{nFormatted} GiB' => '{nFormatted} GiB',
    '{nFormatted} KB' => '{nFormatted} KB',
    '{nFormatted} KiB' => '{nFormatted} KiB',
    '{nFormatted} MB' => '{nFormatted} MB',
    '{nFormatted} MiB' => '{nFormatted} MiB',
    '{nFormatted} PB' => '{nFormatted} PB',
    '{nFormatted} PiB' => '{nFormatted} PiB',
    '{nFormatted} TB' => '{nFormatted} TB',
    '{nFormatted} TiB' => '{nFormatted} TiB',
	'{nFormatted} {n, plural, =1{byte} other{bytes}}' => '{nFormatted} {n, plural, =1{byte} other{bytes}}',
    '{nFormatted} {n, plural, =1{gibibyte} other{gibibytes}}' => '{nFormatted} {n, plural, =1{gibibyte} other{gibibytes}}',
    '{nFormatted} {n, plural, =1{gigabyte} other{gigabytes}}' => '{nFormatted} {n, plural, =1{gigabyte} other{gigabytes}}',
    '{nFormatted} {n, plural, =1{kibibyte} other{kibibytes}}' => '{nFormatted} {n, plural, =1{kibibyte} other{kibibytes}}',
    '{nFormatted} {n, plural, =1{kilobyte} other{kilobytes}}' => '{nFormatted} {n, plural, =1{kilobyte} other{kilobytes}}',
    '{nFormatted} {n, plural, =1{mebibyte} other{mebibytes}}' => '{nFormatted} {n, plural, =1{mebibyte} other{mebibytes}}',
    '{nFormatted} {n, plural, =1{megabyte} other{megabytes}}' => '{nFormatted} {n, plural, =1{megabyte} other{megabytes}}',
    '{nFormatted} {n, plural, =1{pebibyte} other{pebibytes}}' => '{nFormatted} {n, plural, =1{pebibyte} other{pebibytes}}',
    '{nFormatted} {n, plural, =1{petabyte} other{petabytes}}' => '{nFormatted} {n, plural, =1{petabyte} other{petabytes}}',
    '{nFormatted} {n, plural, =1{tebibyte} other{tebibytes}}' => '{nFormatted} {n, plural, =1{tebibyte} other{tebibytes}}',
    '{nFormatted} {n, plural, =1{terabyte} other{terabytes}}' => '{nFormatted} {n, plural, =1{terabyte} other{terabytes}}',
    '(not set)' => '(não definido)',
    'just now' => 'agora mesmo',
    'An internal server error occurred.' => 'Ocorreu um erro interno do servidor.',
    'Are you sure you want to delete this item?' => 'Confirma a exclusão deste item?',
    'Delete' => 'Excluir',
    'Error' => 'Erro',
    'File upload failed.' => 'O upload do arquivo falhou.',
    'Home' => 'Página Inicial',
    'Invalid data received for parameter "{param}".' => 'Dados inválidos recebidos para o parâmetro "{param}".',
    'Login Required' => 'Login Necessário.',
    'Missing required arguments: {params}' => 'Argumentos obrigatórios ausentes: {params}',
    'Missing required parameters: {params}' => 'Parâmetros obrigatórios ausentes: {params}',
    'No' => 'Não',
    'No results found.' => 'Nenhum resultado foi encontrado.',
    'Only files with these MIME types are allowed: {mimeTypes}.' => 'São permitidos somente arquivos com os seguintes tipos MIME: {mimeTypes}.',
    'Only files with these extensions are allowed: {extensions}.' => 'São permitidos somente arquivos com as seguintes extensões: {extensions}.',
    'Page not found.' => 'Página não encontrada.',
    'Please fix the following errors:' => 'Por favor, corrija os seguintes erros:',
    'Please upload a file.' => 'Por favor, faça upload de um arquivo.',
    'Showing <b>{begin, number}-{end, number}</b> of <b>{totalCount, number}</b> {totalCount, plural, one{item} other{items}}.' => 'Exibindo <b>{begin, number}-{end, number}</b> de <b>{totalCount, number}</b> {totalCount, plural, one{item} other{itens}}.',
    'The file "{file}" is not an image.' => 'O arquivo "{file}" não é uma imagem.',
    'The file "{file}" is too big. Its size cannot exceed {formattedLimit}.' => 'O arquivo "{file}" é grande demais. Seu tamanho não pode exceder {formattedLimit}.',
    'The file "{file}" is too small. Its size cannot be smaller than {formattedLimit}.' => 'O arquivo "{file}" é pequeno demais. Seu tamanho não pode ser menor que {formattedLimit}.',
    'The format of {attribute} is invalid.' => 'O formato de "{attribute}" é inválido.',
    'The image "{file}" is too large. The height cannot be larger than {limit, number} {limit, plural, one{pixel} other{pixels}}.' => 'O arquivo "{file}" é grande demais. A altura não pode ser maior que {limit, number} {limit, plural, one{pixel} other{pixels}}.',
    'The image "{file}" is too large. The width cannot be larger than {limit, number} {limit, plural, one{pixel} other{pixels}}.' => 'O arquivo "{file}" é grande demais. A largura não pode ser maior que {limit, number} {limit, plural, one{pixel} other{pixels}}.',
    'The image "{file}" is too small. The height cannot be smaller than {limit, number} {limit, plural, one{pixel} other{pixels}}.' => 'O arquivo "{file}" é pequeno demais. A altura não pode ser menor que {limit, number} {limit, plural, one{pixel} other{pixels}}.',
    'The image "{file}" is too small. The width cannot be smaller than {limit, number} {limit, plural, one{pixel} other{pixels}}.' => 'O arquivo "{file}" é pequeno demais. A largura não pode ser menor que {limit, number} {limit, plural, one{pixel} other{pixels}}.',
    'The requested view "{name}" was not found.' => 'A visão "{name}" solicitada não foi encontrada.',
    'The verification code is incorrect.' => 'O código de verificação está incorreto.',
    'Total <b>{count, number}</b> {count, plural, one{item} other{items}}.' => 'Total <b>{count, number}</b> {count, plural, one{item} other{itens}}.',
    'Unable to verify your data submission.' => 'Não foi possível verificar o seu envio de dados.',
    'Unknown option: --{name}' => 'Opção desconhecida : --{name}',
    'Update' => 'Alterar',
    'View' => 'Exibir',
    'Yes' => 'Sim',
    'You are not allowed to perform this action.' => 'Você não está autorizado a realizar essa ação.',
    'You can upload at most {limit, number} {limit, plural, one{file} other{files}}.' => 'Você pode fazer o upload de, no máximo, {limit, number} {limit, plural, one{arquivo} other{arquivos}}.',
    'the input value' => 'o valor de entrada',
    '{attribute} "{value}" has already been taken.' => '{attribute} "{value}" já foi utilizado.',
    '{attribute} cannot be blank.' => '"{attribute}" não pode ficar em branco.',
	'{attribute} contains wrong subnet mask.' => '{attribute} contém a máscara de sub-rede errado.',
    '{attribute} is invalid.' => '"{attribute}" é inválido.',
    '{attribute} is not a valid URL.' => '"{attribute}" não é uma URL válida.',
    '{attribute} is not a valid email address.' => '"{attribute}" não é um endereço de e-mail válido.',
	'{attribute} is not in the allowed range.' => '{attribute} intervalo não permitido.',
    '{attribute} must be "{requiredValue}".' => '"{attribute}" deve ser "{requiredValue}".',
    '{attribute} must be a number.' => '"{attribute}" deve ser um número.',
    '{attribute} must be a string.' => '"{attribute}" deve ser um texto.',
	'{attribute} must be a valid IP address.' => '{attribute} deve ser um endereço IP válido.',
    '{attribute} must be an IP address with specified subnet.' => '{attribute} deve ser um endereço IP com sub-rede especificada.',
	'{attribute} must not be a subnet.' => '{attribute} não deve ser uma sub-rede.',
    '{attribute} must not be an IPv4 address.' => '{attribute} não deve ser um endereço IPv4.',
    '{attribute} must not be an IPv6 address.' => '{attribute} não deve ser um endereço IPv6.',
    '{attribute} must be an integer.' => '"{attribute}" deve ser um número inteiro.',
    '{attribute} must be either "{true}" or "{false}".' => '"{attribute}" deve ser "{true}" ou "{false}".',
    '{attribute} must be greater than "{compareValueOrAttribute}".' => '"{attribute}" deve ser maior que "{compareValueOrAttribute}".',
    '{attribute} must be greater than or equal to "{compareValueOrAttribute}".' => '"{attribute}" deve ser maior ou igual a "{compareValueOrAttribute}".',
    '{attribute} must be less than "{compareValueOrAttribute}".' => '"{attribute}" deve ser menor que "{compareValueOrAttribute}".',
    '{attribute} must be less than or equal to "{compareValueOrAttribute}".' => '"{attribute}" deve ser menor ou igual a "{compareValueOrAttribute}".',
    '{attribute} must be no greater than {max}.' => '"{attribute}" não pode ser maior que {max}.',
    '{attribute} must be no less than {min}.' => '"{attribute}" não pode ser menor que {min}.',
    '{attribute} must not be equal to "{compareValueOrAttribute}".' => '"{attribute}" não deve ser igual a "{compareValueOrAttribute}".',
    '{attribute} should contain at least {min, number} {min, plural, one{character} other{characters}}.' => '"{attribute}" deve conter pelo menos {min, number} {min, plural, one{caractere} other{caracteres}}.',
    '{attribute} should contain at most {max, number} {max, plural, one{character} other{characters}}.' => '"{attribute}" deve conter no máximo {max, number} {max, plural, one{caractere} other{caracteres}}.',
    '{attribute} should contain {length, number} {length, plural, one{character} other{characters}}.' => '"{attribute}" deve conter {length, number} {length, plural, one{caractere} other{caracteres}}.',
	'{attribute} must be equal to "{compareValueOrAttribute}".' => '{attribute} deve ser igual a "{compareValueOrAttribute}".',
];
