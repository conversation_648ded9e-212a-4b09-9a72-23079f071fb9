<?php
/**
 * Message translations.
 *
 * This file is automatically generated by 'yii message' command.
 * It contains the localizable messages extracted from source code.
 * You may modify this file by translating the extracted messages.
 *
 * Each array element represents the translation (value) of a message (key).
 * If the value is empty, the message is considered as not translated.
 * Messages that no longer need translation will have their translations
 * enclosed between a pair of '@@' marks.
 *
 * Message string can be used with plural forms format. Check i18n section
 * of the guide for details.
 *
 * NOTE: this file must be saved in UTF-8 encoding.
 */
return [
    '(not set)' => '(ikke angitt)',
    'An internal server error occurred.' => 'En intern serverfeil oppstod.',
    'Are you sure you want to delete this item?' => 'Er du sikker på at du vil slette dette elementet?',
    'Delete' => 'Slett',
    'Error' => 'Feil',
    'File upload failed.' => 'Filopplasting feilet.',
    'Home' => 'Hjem',
    'Invalid data received for parameter "{param}".' => 'Ugyldig data mottatt for parameter "{param}".',
    'Login Required' => 'Innlogging påkrevet',
    'Missing required arguments: {params}' => 'Mangler obligatoriske argumenter: {params}',
    'Missing required parameters: {params}' => 'Mangler obligatoriske parametere: {params}',
    'No' => 'Nei',
    'No results found.' => 'Ingen resultater funnet.',
    'Only files with these MIME types are allowed: {mimeTypes}.' => 'Bare filer med disse MIME-typene er tillatt: {mimeTypes}.',
    'Only files with these extensions are allowed: {extensions}.' => 'Bare filer med disse filendelsene er tillatt: {mimeTypes}.',
    'Page not found.' => 'Siden finnes ikke.',
    'Please fix the following errors:' => 'Vennligs fiks følgende feil:',
    'Please upload a file.' => 'Vennligs last opp en fil.',
    'Showing <b>{begin, number}-{end, number}</b> of <b>{totalCount, number}</b> {totalCount, plural, one{item} other{items}}.' => 'Viser <b>{begin, number}-{end, number}</b> av <b>{totalCount, number}</b> {totalCount, plural, one{element} other{elementer}}.',
    'The file "{file}" is not an image.' => 'Filen "{file}" er ikke et bilde.',
    'The file "{file}" is too big. Its size cannot exceed {formattedLimit}.' => 'Filen "{file}" er for stor. Størrelsen kan ikke overskride {formattedLimit}.',
    'The file "{file}" is too small. Its size cannot be smaller than {formattedLimit}.' => 'Filen "{file}" er for liten. Størrelsen kan ikke være mindre enn {formattedLimit}.',
    'The format of {attribute} is invalid.' => 'Formatet til {attribute} er ugyldig.',
    'The image "{file}" is too large. The height cannot be larger than {limit, number} {limit, plural, one{pixel} other{pixels}}.' => 'Bildet "{file}" er for stort. Høyden kan ikke overskride {limit, number} {limit, plural, one{piksel} other{piksler}}.',
    'The image "{file}" is too large. The width cannot be larger than {limit, number} {limit, plural, one{pixel} other{pixels}}.' => 'Bildet "{file}" er for stort. Bredden kan ikke overskride {limit, number} {limit, plural, one{piksel} other{piksler}}.',
    'The image "{file}" is too small. The height cannot be smaller than {limit, number} {limit, plural, one{pixel} other{pixels}}.' => 'Bildet "{file}" er for lite. Høyden kan ikke være mindre enn {limit, number} {limit, plural, one{piksel} other{piksler}}.',
    'The image "{file}" is too small. The width cannot be smaller than {limit, number} {limit, plural, one{pixel} other{pixels}}.' => 'Bildet "{file}" er for lite. Bredden kan ikke være mindre enn {limit, number} {limit, plural, one{piksel} other{piksler}}.',
    'The requested view "{name}" was not found.' => 'Den forespurte visningen "{name}" ble ikke funnet.',
    'The verification code is incorrect.' => 'Verifiseringskoden er feil.',
    'Total <b>{count, number}</b> {count, plural, one{item} other{items}}.' => 'Totalt <b>{count, number}</b> {count, plural, one{element} other{elementer}}.',
    'Unable to verify your data submission.' => 'Kunne ikke verifisere innsendt data.',
    'Unknown option: --{name}' => 'Ukjent alternativ: --{name}',
    'Update' => 'Oppdater',
    'View' => 'Vis',
    'Yes' => 'Ja',
    'You are not allowed to perform this action.' => 'Du har ikke tilatelse til å gjennomføre denne handlingen.',
    'You can upload at most {limit, number} {limit, plural, one{file} other{files}}.' => 'Du kan laste opp maks {limit, number} {limit, plural, one{fil} other{filer}}.',
    'in {delta, plural, =1{a day} other{# days}}' => 'om {delta, plural, =1{en dag} other{# dager}}',
    'in {delta, plural, =1{a minute} other{# minutes}}' => 'om {delta, plural, =1{ett minutt} other{# minutter}}',
    'in {delta, plural, =1{a month} other{# months}}' => 'om {delta, plural, =1{en måned} other{# måneder}}',
    'in {delta, plural, =1{a second} other{# seconds}}' => 'om {delta, plural, =1{ett sekund} other{# sekunder}}',
    'in {delta, plural, =1{a year} other{# years}}' => 'om {delta, plural, =1{ett år} other{# år}}',
    'in {delta, plural, =1{an hour} other{# hours}}' => 'om {delta, plural, =1{en time} other{# timer}}',
    'just now' => 'akkurat nå',
    'the input value' => 'inndataverdien',
    '{attribute} "{value}" has already been taken.' => '{attribute} "{value}" er allerede tatt i bruk.',
    '{attribute} cannot be blank.' => '{attribute} kan ikke være tomt.',
    '{attribute} is invalid.' => '{attribute} er ugyldig.',
    '{attribute} is not a valid URL.' => '{attribute} er ikke en gyldig URL.',
    '{attribute} is not a valid email address.' => '{attribute} er ikke en gyldig e-postadresse.',
    '{attribute} must be "{requiredValue}".' => '{attribute} må være "{requiredValue}".',
    '{attribute} must be a number.' => '{attribute} må være et nummer.',
    '{attribute} must be a string.' => '{attribute} må være en tekststreng.',
    '{attribute} must be an integer.' => '{attribute} må være et heltall.',
    '{attribute} must be either "{true}" or "{false}".' => '{attribute} må være enten "{true}" eller "{false}".',
    '{attribute} must be greater than "{compareValue}".' => '{attribute} må være større enn "{compareValue}".',
    '{attribute} must be greater than or equal to "{compareValue}".' => '{attribute} må være større enn eller lik "{compareValue}".',
    '{attribute} must be less than "{compareValue}".' => '{attribute} må være mindre enn "{compareValue}".',
    '{attribute} must be less than or equal to "{compareValue}".' => '{attribute} må være mindre enn eller lik "{compareValue}".',
    '{attribute} must be no greater than {max}.' => '{attribute} kan ikke være større enn {max}.',
    '{attribute} must be no less than {min}.' => '{attribute} kan ikke være mindre enn {min}.',
    '{attribute} must be repeated exactly.' => '{attribute} må gjentas nøyaktig.',
    '{attribute} must not be equal to "{compareValue}".' => '{attribute} kan ikke være lik "{compareValue}".',
    '{attribute} should contain at least {min, number} {min, plural, one{character} other{characters}}.' => '{attribute} må inneholde minst {min, number} {min, plural, one{tegn} other{tegn}}.',
    '{attribute} should contain at most {max, number} {max, plural, one{character} other{characters}}.' => '{attribute} kan inneholde maks {max, number} {max, plural, one{tegn} other{tegn}}.',
    '{attribute} should contain {length, number} {length, plural, one{character} other{characters}}.' => '{attribute} må inneholde {length, number} {length, plural, one{tegn} other{tegn}}.',
    '{delta, plural, =1{a day} other{# days}} ago' => '{delta, plural, =1{en dag} other{# dager}} siden',
    '{delta, plural, =1{a minute} other{# minutes}} ago' => '{delta, plural, =1{ett minutt} other{# minutter}} siden',
    '{delta, plural, =1{a month} other{# months}} ago' => '{delta, plural, =1{en måned} other{# måneder}} siden',
    '{delta, plural, =1{a second} other{# seconds}} ago' => '{delta, plural, =1{ett sekund} other{# sekunder}} siden',
    '{delta, plural, =1{a year} other{# years}} ago' => '{delta, plural, =1{ett år} other{# år}} siden',
    '{delta, plural, =1{an hour} other{# hours}} ago' => '{delta, plural, =1{en time} other{# timer}} siden',
    '{nFormatted} B' => '{nFormatted} B',
    '{nFormatted} GB' => '{nFormatted} GB',
    '{nFormatted} GiB' => '{nFormatted} GiB',
    '{nFormatted} KB' => '{nFormatted} KB',
    '{nFormatted} KiB' => '{nFormatted} KiB',
    '{nFormatted} MB' => '{nFormatted} MB',
    '{nFormatted} MiB' => '{nFormatted} MiB',
    '{nFormatted} PB' => '{nFormatted} PB',
    '{nFormatted} PiB' => '{nFormatted} PiB',
    '{nFormatted} TB' => '{nFormatted} TB',
    '{nFormatted} TiB' => '{nFormatted} TiB',
    '{nFormatted} {n, plural, =1{byte} other{bytes}}' => '{nFormatted} {n, plural, =1{byte} other{byte}}',
    '{nFormatted} {n, plural, =1{gibibyte} other{gibibyte}}' => '{nFormatted} {n, plural, =1{gibibyte} other{gibibyte}}',
    '{nFormatted} {n, plural, =1{gigabyte} other{gigabyte}}' => '{nFormatted} {n, plural, =1{gigabyte} other{gigabyte}}',
    '{nFormatted} {n, plural, =1{kibibyte} other{kibibyte}}' => '{nFormatted} {n, plural, =1{kibibyte} other{kibibyte}}',
    '{nFormatted} {n, plural, =1{kilobyte} other{kilobyte}}' => '{nFormatted} {n, plural, =1{kilobyte} other{kilobyte}}',
    '{nFormatted} {n, plural, =1{mebibyte} other{mebibyte}}' => '{nFormatted} {n, plural, =1{mebibyte} other{mebibyte}}',
    '{nFormatted} {n, plural, =1{megabyte} other{megabyte}}' => '{nFormatted} {n, plural, =1{megabyte} other{megabyte}}',
    '{nFormatted} {n, plural, =1{pebibyte} other{pebibyte}}' => '{nFormatted} {n, plural, =1{pebibyte} other{pebibyte}}',
    '{nFormatted} {n, plural, =1{petabyte} other{petabyte}}' => '{nFormatted} {n, plural, =1{petabyte} other{petabyte}}',
    '{nFormatted} {n, plural, =1{tebibyte} other{tebibyte}}' => '{nFormatted} {n, plural, =1{tebibyte} other{tebibyte}}',
    '{nFormatted} {n, plural, =1{terabyte} other{terabyte}}' => '{nFormatted} {n, plural, =1{terabyte} other{terabyte}}',
];
