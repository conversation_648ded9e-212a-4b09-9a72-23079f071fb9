<?php
/**
 * Message translations.
 *
 * This file is automatically generated by 'yii message/extract' command.
 * It contains the localizable messages extracted from source code.
 * You may modify this file by translating the extracted messages.
 *
 * Each array element represents the translation (value) of a message (key).
 * If the value is empty, the message is considered as not translated.
 * Messages that no longer need translation will have their translations
 * enclosed between a pair of '@@' marks.
 *
 * Message string can be used with plural forms format. Check i18n section
 * of the guide for details.
 *
 * NOTE: this file must be saved in UTF-8 encoding.
 */
return [
    'Powered by {yii}' => 'Powered by {yii}',
    'Yii Framework' => 'Yii Framework',
    '{attribute} must be equal to "{compareValueOrAttribute}".' => '{attribute} täytyy olla yhtä suuri kuin "{compareValueOrAttribute}".',
    '{attribute} must be greater than "{compareValueOrAttribute}".' => '{attribute} täytyy olla suurempi kuin "{compareValueOrAttribute}".',
    '{attribute} must be greater than or equal to "{compareValueOrAttribute}".' => '{attribute} täytyy olla suurempi tai yhtä suuri kuin "{compareValueOrAttribute}".',
    '{attribute} must be less than "{compareValueOrAttribute}".' => '{attribute} täytyy olla pienempi kuin "{compareValueOrAttribute}".',
    '{attribute} must be less than or equal to "{compareValueOrAttribute}".' => '{attribute} täytyy olla pienempi tai yhtä suuri kuin "{compareValueOrAttribute}".',
    '{attribute} must not be equal to "{compareValueOrAttribute}".' => '{attribute} ei saa olla yhtä suuri kuin "{compareValueOrAttribute}".',
    '(not set)' => '(ei asetettu)',
    'An internal server error occurred.' => 'Sisäinen palvelinvirhe.',
    'Are you sure you want to delete this item?' => 'Haluatko varmasti poistaa tämän?',
    'Delete' => 'Poista',
    'Error' => 'Virhe',
    'File upload failed.' => 'Tiedoston lähetys epäonnistui.',
    'Home' => 'Koti',
    'Invalid data received for parameter "{param}".' => 'Parametri "{param}" vastaanotti virheellistä dataa.',
    'Login Required' => 'Kirjautuminen vaaditaan',
    'Missing required arguments: {params}' => 'Pakolliset argumentit puuttuu: {params}',
    'Missing required parameters: {params}' => 'Pakolliset parametrit puuttuu: {params}',
    'No' => 'Ei',
    'No results found.' => 'Ei tuloksia.',
    'Only files with these MIME types are allowed: {mimeTypes}.' => 'Sallittuja ovat vain tiedostot, joiden MIME-tyyppi on: {mimeTypes}.',
    'Only files with these extensions are allowed: {extensions}.' => 'Sallittuja ovat vain tiedostot, joiden tiedostopääte on: {extensions}.',
    'Page not found.' => 'Sivua ei löytynyt.',
    'Please fix the following errors:' => 'Korjaa seuraavat virheet:',
    'Please upload a file.' => 'Lähetä tiedosto.',
    'Showing <b>{begin, number}-{end, number}</b> of <b>{totalCount, number}</b> {totalCount, plural, one{item} other{items}}.' => 'Näytetään <b>{begin, number}-{end, number}</b> kaikkiaan <b>{totalCount, number}</b> {totalCount, plural, one{tuloksesta} other{tuloksesta}}.',
    'The file "{file}" is not an image.' => 'Tiedosto "{file}" ei ole kuva.',
    'The file "{file}" is too big. Its size cannot exceed {formattedLimit}.' => 'Tiedosto "{file}" on liian iso. Sen koko ei voi olla suurempi kuin {formattedLimit}.',
    'The file "{file}" is too small. Its size cannot be smaller than {formattedLimit}.' => 'Tiedosto "{file}" on liian pieni. Sen koko ei voi olla pienempi kuin {formattedLimit}.',
    'The format of {attribute} is invalid.' => 'Attribuutin {attribute} formaatti on virheellinen.',
    'The image "{file}" is too large. The height cannot be larger than {limit, number} {limit, plural, one{pixel} other{pixels}}.' => 'Kuva "{file}" on liian suuri. Korkeus ei voi olla suurempi kuin {limit, number} {limit, plural, one{pikseli} other{pikseliä}}.',
    'The image "{file}" is too large. The width cannot be larger than {limit, number} {limit, plural, one{pixel} other{pixels}}.' => 'Kuva "{file}" on liian suuri. Leveys ei voi olla suurempi kuin {limit, number} {limit, plural, one{pikseli} other{pikseliä}}.',
    'The image "{file}" is too small. The height cannot be smaller than {limit, number} {limit, plural, one{pixel} other{pixels}}.' => 'Kuva "{file}" on liian pieni. Korkeus ei voi olla pienempi kuin {limit, number} {limit, plural, one{pikseli} other{pikseliä}}.',
    'The image "{file}" is too small. The width cannot be smaller than {limit, number} {limit, plural, one{pixel} other{pixels}}.' => 'Kuva "{file}" on liian pieni. Leveys ei voi olla pienempi kuin {limit, number} {limit, plural, one{pikseli} other{pikseliä}}.',
    'The requested view "{name}" was not found.' => 'Pyydettyä näkymää "{name}" ei löytynyt.',
    'The verification code is incorrect.' => 'Vahvistuskoodi on virheellinen.',
    'Total <b>{count, number}</b> {count, plural, one{item} other{items}}.' => 'Yhteensä <b>{count, number}</b> {count, plural, one{tulos} other{tulosta}}.',
    'Unable to verify your data submission.' => 'Tietojen lähetystä ei voida varmistaa.',
    'Unknown option: --{name}' => 'Tuntematon valinta: --{name}',
    'Update' => 'Päivitä',
    'View' => 'Näytä',
    'Yes' => 'Kyllä',
    'You are not allowed to perform this action.' => 'Sinulla ei ole tarvittavia oikeuksia toiminnon suorittamiseen.',
    'You can upload at most {limit, number} {limit, plural, one{file} other{files}}.' => 'Voit lähettää enintään {limit, number} {limit, plural, one{tiedoston} other{tiedostoa}}.',
    'in {delta, plural, =1{a day} other{# days}}' => '{delta, plural, =1{päivässä} other{# päivässä}}',
    'in {delta, plural, =1{a minute} other{# minutes}}' => '{delta, plural, =1{minuutissa} other{# minuutissa}}',
    'in {delta, plural, =1{a month} other{# months}}' => '{delta, plural, =1{kuukaudessa} other{# kuukaudessa}}',
    'in {delta, plural, =1{a second} other{# seconds}}' => '{delta, plural, =1{sekunnissa} other{# sekunnissa}}',
    'in {delta, plural, =1{a year} other{# years}}' => '{delta, plural, =1{vuodessa} other{# vuodessa}}',
    'in {delta, plural, =1{an hour} other{# hours}}' => '{delta, plural, =1{tunnissa} other{# tunnissa}}',
    'just now' => 'juuri nyt',
    'the input value' => 'syötetty arvo',
    '{attribute} "{value}" has already been taken.' => '{attribute} "{value}" on jo käytössä.',
    '{attribute} cannot be blank.' => '{attribute} ei voi olla tyhjä.',
    '{attribute} contains wrong subnet mask.' => '{attribute} sisältää väärän aliverkkopeitteen.',
    '{attribute} is invalid.' => '{attribute} on virheellinen.',
    '{attribute} is not a valid URL.' => '{attribute} on virheellinen URL.',
    '{attribute} is not a valid email address.' => '{attribute} on virheellinen sähköpostiosoite.',
    '{attribute} is not in the allowed range.' => '{attribute} ei ole sallitulla alueella.',
    '{attribute} must be "{requiredValue}".' => '{attribute} täytyy olla "{requiredValue}".',
    '{attribute} must be a number.' => '{attribute} täytyy olla luku.',
    '{attribute} must be a string.' => '{attribute} täytyy olla merkkijono.',
    '{attribute} must be a valid IP address.' => '{attribute} täytyy olla kelvollinen IP-osoite.',
    '{attribute} must be an IP address with specified subnet.' => '{attribute} täytyy olla määritetyllä aliverkolla oleva IP-osoite.',
    '{attribute} must be an integer.' => '{attribute} täytyy olla kokonaisluku.',
    '{attribute} must be either "{true}" or "{false}".' => '{attribute} täytyy olla joko {true} tai {false}.',
    '{attribute} must be no greater than {max}.' => '{attribute} ei saa olla suurempi kuin "{max}".',
    '{attribute} must be no less than {min}.' => '{attribute} ei saa olla pienempi kuin "{min}".',
    '{attribute} must not be a subnet.' => '{attribute} ei saa olla aliverkko.',
    '{attribute} must not be an IPv4 address.' => '{attribute} ei saa olla IPv4-osoite.',
    '{attribute} must not be an IPv6 address.' => '{attribute} ei saa olla IPv6-osoite.',
    '{attribute} should contain at least {min, number} {min, plural, one{character} other{characters}}.' => '{attribute} tulisi sisältää vähintään {min, number} {min, plural, one{merkki} other{merkkiä}}.',
    '{attribute} should contain at most {max, number} {max, plural, one{character} other{characters}}.' => '{attribute} tulisi sisältää enintään {max, number} {max, plural, one{merkki} other{merkkiä}}.',
    '{attribute} should contain {length, number} {length, plural, one{character} other{characters}}.' => '{attribute} tulisi sisältää {length, number} {length, plural, one{merkki} other{merkkiä}}.',
    '{delta, plural, =1{1 day} other{# days}}' => '{delta, plural, =1{1 päivä} other{# päivää}}',
    '{delta, plural, =1{1 hour} other{# hours}}' => '{delta, plural, =1{1 tunti} other{# tuntia}}',
    '{delta, plural, =1{1 minute} other{# minutes}}' => '{delta, plural, =1{1 minuutti} other{# minuuttia}}',
    '{delta, plural, =1{1 month} other{# months}}' => '{delta, plural, =1{1 kuukausi} other{# kuukautta}}',
    '{delta, plural, =1{1 second} other{# seconds}}' => '{delta, plural, =1{1 sekunti} other{# sekuntia}}',
    '{delta, plural, =1{1 year} other{# years}}' => '{delta, plural, =1{1 vuosi} other{# vuotta}}',
    '{delta, plural, =1{a day} other{# days}} ago' => '{delta, plural, =1{päivä} other{# päivää}} sitten',
    '{delta, plural, =1{a minute} other{# minutes}} ago' => '{delta, plural, =1{minuutti} other{# minuuttia}} sitten',
    '{delta, plural, =1{a month} other{# months}} ago' => '{delta, plural, =1{kuukausi} other{# kuukautta}} sitten',
    '{delta, plural, =1{a second} other{# seconds}} ago' => '{delta, plural, =1{sekunti} other{# sekuntia}} sitten',
    '{delta, plural, =1{a year} other{# years}} ago' => '{delta, plural, =1{vuosi} other{# vuotta}} sitten',
    '{delta, plural, =1{an hour} other{# hours}} ago' => '{delta, plural, =1{tunti} other{# tuntia}} sitten',
    '{nFormatted} B' => '{nFormatted} t',
    '{nFormatted} GB' => '{nFormatted} Gt',
    '{nFormatted} GiB' => '{nFormatted} GiB',
    '{nFormatted} KB' => '{nFormatted} kt',
    '{nFormatted} KiB' => '{nFormatted} KiB',
    '{nFormatted} MB' => '{nFormatted} Mt',
    '{nFormatted} MiB' => '{nFormatted} MiB',
    '{nFormatted} PB' => '{nFormatted} Pt',
    '{nFormatted} PiB' => '{nFormatted} PiB',
    '{nFormatted} TB' => '{nFormatted} Tt',
    '{nFormatted} TiB' => '{nFormatted} TiB',
    '{nFormatted} {n, plural, =1{byte} other{bytes}}' => '{nFormatted} {n, plural, =1{tavu} other{tavua}}',
    '{nFormatted} {n, plural, =1{gibibyte} other{gibibytes}}' => '{nFormatted} {n, plural, =1{gibitavu} other{gibitavua}}',
    '{nFormatted} {n, plural, =1{gigabyte} other{gigabytes}}' => '{nFormatted} {n, plural, =1{gigatavu} other{gigatavua}}',
    '{nFormatted} {n, plural, =1{kibibyte} other{kibibytes}}' => '{nFormatted} {n, plural, =1{kibitavu} other{kibitavua}}',
    '{nFormatted} {n, plural, =1{kilobyte} other{kilobytes}}' => '{nFormatted} {n, plural, =1{kilotavu} other{kilotavua}}',
    '{nFormatted} {n, plural, =1{mebibyte} other{mebibytes}}' => '{nFormatted} {n, plural, =1{mebitavu} other{mebitavua}}',
    '{nFormatted} {n, plural, =1{megabyte} other{megabytes}}' => '{nFormatted} {n, plural, =1{megatavu} other{megatavua}}',
    '{nFormatted} {n, plural, =1{pebibyte} other{pebibytes}}' => '{nFormatted} {n, plural, =1{pebitavu} other{pebitavua}}',
    '{nFormatted} {n, plural, =1{petabyte} other{petabytes}}' => '{nFormatted} {n, plural, =1{petatavu} other{petatavua}}',
    '{nFormatted} {n, plural, =1{tebibyte} other{tebibytes}}' => '{nFormatted} {n, plural, =1{tebitavu} other{tebitavua}}',
    '{nFormatted} {n, plural, =1{terabyte} other{terabytes}}' => '{nFormatted} {n, plural, =1{teratavu} other{teratavua}}',
];
