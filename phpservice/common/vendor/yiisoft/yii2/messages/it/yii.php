<?php
/**
 * Message translations.
 *
 * This file is automatically generated by 'yii message/extract' command.
 * It contains the localizable messages extracted from source code.
 * You may modify this file by translating the extracted messages.
 *
 * Each array element represents the translation (value) of a message (key).
 * If the value is empty, the message is considered as not translated.
 * Messages that no longer need translation will have their translations
 * enclosed between a pair of '@@' marks.
 *
 * Message string can be used with plural forms format. Check i18n section
 * of the guide for details.
 *
 * NOTE: this file must be saved in UTF-8 encoding.
 */
return [
    'Unknown alias: -{name}' => 'Alias sconosciuto: -{name}',
    '{attribute} contains wrong subnet mask.' => '{attribute} contiene una subnet mask errata.',
    '{attribute} is not in the allowed range.' => '{attribute} non rientra nell\'intervallo permesso',
    '{attribute} must be a valid IP address.' => '{attribute} deve essere un indirizzo IP valido.',
    '{attribute} must be an IP address with specified subnet.' => '{attribute} deve essere un indirizzo IP valido con subnet specificata.',
    '{attribute} must be equal to "{compareValueOrAttribute}".' => '{attribute} deve essere uguale a "{compareValueOrAttribute}".',
    '{attribute} must be greater than "{compareValueOrAttribute}".' => '{attribute} deve essere maggiore di "{compareValueOrAttribute}".',
    '{attribute} must be greater than or equal to "{compareValueOrAttribute}".' => '{attribute} deve essere maggiore o uguale a "{compareValueOrAttribute}".',
    '{attribute} must be less than "{compareValueOrAttribute}".' => '{attribute} deve essere minore di "{compareValueOrAttribute}".',
    '{attribute} must be less than or equal to "{compareValueOrAttribute}".' => '{attribute} deve essere minore o uguale a "{compareValueOrAttribute}".',
    '{attribute} must not be a subnet.' => '{attribute} non deve essere una subnet.',
    '{attribute} must not be an IPv4 address.' => '{attribute} non deve essere un indirizzo IPv4.',
    '{attribute} must not be an IPv6 address.' => '{attribute} non deve essere un indirizzo IPv6.',
    '{attribute} must not be equal to "{compareValueOrAttribute}".' => '{attribute} non deve essere uguale a "{compareValueOrAttribute}".',
    '{delta, plural, =1{1 day} other{# days}}' => '{delta, plural, =1{1 giorno} other{# giorni}}',
    '{delta, plural, =1{1 hour} other{# hours}}' => '{delta, plural, =1{1 ora} other{# ore}}',
    '{delta, plural, =1{1 minute} other{# minutes}}' => '{delta, plural, =1{1 minuto} other{# minuti}}',
    '{delta, plural, =1{1 month} other{# months}}' => '{delta, plural, =1{1 mese} other{# mesi}}',
    '{delta, plural, =1{1 second} other{# seconds}}' => '{delta, plural, =1{1 secondo} other{# secondi}}',
    '{delta, plural, =1{1 year} other{# years}}' => '{delta, plural, =1{1 anno} other{# anni}}',
    '{attribute} must be greater than "{compareValue}".' => '@@{attribute} deve essere maggiore di "{compareValue}".@@',
    '{attribute} must be greater than or equal to "{compareValue}".' => '@@{attribute} deve essere maggiore o uguale a "{compareValue}".@@',
    '{attribute} must be less than "{compareValue}".' => '@@{attribute} deve essere minore di "{compareValue}".@@',
    '{attribute} must be less than or equal to "{compareValue}".' => '@@{attribute} deve essere minore o uguale a "{compareValue}".@@',
    '{attribute} must be repeated exactly.' => '@@{attribute} deve essere ripetuto esattamente.@@',
    '{attribute} must not be equal to "{compareValue}".' => '@@{attribute} non deve essere uguale a "{compareValue}".@@',
    '(not set)' => '(nessun valore)',
    'An internal server error occurred.' => 'Si è verificato un errore interno',
    'Are you sure you want to delete this item?' => 'Sei sicuro di voler eliminare questo elemento?',
    'Delete' => 'Elimina',
    'Error' => 'Errore',
    'File upload failed.' => 'Upload file fallito.',
    'Home' => 'Home',
    'Invalid data received for parameter "{param}".' => 'Dati ricevuti non corretti per il parametro "{param}".',
    'Login Required' => 'Login richiesto',
    'Missing required arguments: {params}' => 'Il seguente argomento è mancante: {params}',
    'Missing required parameters: {params}' => 'Il seguente parametro è mancante: {params}',
    'No' => 'No',
    'No results found.' => 'Nessun risultato trovato',
    'Only files with these MIME types are allowed: {mimeTypes}.' => 'Solo i file con questi tipi MIME sono consentiti: {mimeTypes}.',
    'Only files with these extensions are allowed: {extensions}.' => 'Solo i file con queste estensioni sono permessi: {extensions}.',
    'Page not found.' => 'Pagina non trovata.',
    'Please fix the following errors:' => 'Per favore correggi i seguenti errori:',
    'Please upload a file.' => 'Per favore carica un file.',
    'Showing <b>{begin, number}-{end, number}</b> of <b>{totalCount, number}</b> {totalCount, plural, one{item} other{items}}.' => 'Visualizzo <b>{begin, number}-{end, number}</b> di <b>{totalCount, number}</b> {totalCount, plural, one{elemento} other{elementi}}.',
    'The file "{file}" is not an image.' => 'Il file "{file}" non è una immagine.',
    'The file "{file}" is too big. Its size cannot exceed {formattedLimit}.' => 'Il file "{file}" è troppo grande. La dimensione non può superare i {formattedLimit}.',
    'The file "{file}" is too small. Its size cannot be smaller than {formattedLimit}.' => 'Il file "{file}" è troppo piccolo. La dimensione non può essere inferiore a {formattedLimit}.',
    'The format of {attribute} is invalid.' => 'Il formato di {attribute} non è valido.',
    'The image "{file}" is too large. The height cannot be larger than {limit, number} {limit, plural, one{pixel} other{pixels}}.' => 'L\'immagine "{file}" è troppo grande. La sua altezza non può essere maggiore di {limit, number} {limit, plural, one{pixel} other{pixel}}.',
    'The image "{file}" is too large. The width cannot be larger than {limit, number} {limit, plural, one{pixel} other{pixels}}.' => 'L immagine "{file}" è troppo grande. La sua larghezza non può essere maggiore di {limit, number} {limit, plural, one{pixel} other{pixel}}.',
    'The image "{file}" is too small. The height cannot be smaller than {limit, number} {limit, plural, one{pixel} other{pixels}}.' => 'L immagine "{file}" è troppo piccola. La sua altezza non può essere minore di {limit, number} {limit, plural, one{pixel} other{pixel}}.',
    'The image "{file}" is too small. The width cannot be smaller than {limit, number} {limit, plural, one{pixel} other{pixels}}.' => 'L immagine "{file}" è troppo piccola. La sua larghezza non può essere minore di {limit, number} {limit, plural, one{pixel} other{pixel}}.',
    'The requested view "{name}" was not found.' => 'La vista "{name}" richiesta non è stata trovata.',
    'The verification code is incorrect.' => 'Il codice di verifica non è corretto.',
    'Total <b>{count, number}</b> {count, plural, one{item} other{items}}.' => '{count, plural, one{Elementi} other{Elementi}} totali <b>{count, number}</b>.',
    'Unable to verify your data submission.' => 'Impossibile verificare i dati inviati.',
    'Unknown option: --{name}' => 'Opzione Sconosciuta: --{name}',
    'Update' => 'Aggiorna',
    'View' => 'Visualizza',
    'Yes' => 'Si',
    'You are not allowed to perform this action.' => 'Non sei autorizzato ad eseguire questa operazione.',
    'You can upload at most {limit, number} {limit, plural, one{file} other{files}}.' => 'Puoi caricare al massimo {limit, number} {limit, plural, one{file} other{file}}.',
    'in {delta, plural, =1{a day} other{# days}}' => 'in {delta, plural, =1{un giorno} other{# giorni}}',
    'in {delta, plural, =1{a minute} other{# minutes}}' => 'in {delta, plural, =1{un minuto} other{# minuti}}',
    'in {delta, plural, =1{a month} other{# months}}' => 'in {delta, plural, =1{un mese} other{# mesi}}',
    'in {delta, plural, =1{a second} other{# seconds}}' => 'in {delta, plural, =1{un secondo} other{# secondi}}',
    'in {delta, plural, =1{a year} other{# years}}' => 'in {delta, plural, =1{un anno} other{# anni}}',
    'in {delta, plural, =1{an hour} other{# hours}}' => 'in {delta, plural, =1{un\'ora} other{# ore}}',
    'just now' => 'proprio ora',
    'the input value' => 'il valore del campo',
    '{attribute} "{value}" has already been taken.' => '{attribute} "{value}" è già presente.',
    '{attribute} cannot be blank.' => '{attribute} non può essere vuoto.',
    '{attribute} is invalid.' => '{attribute} non è valido.',
    '{attribute} is not a valid URL.' => '{attribute} non è un URL valido.',
    '{attribute} is not a valid email address.' => '{attribute} non è un indirizzo email valido.',
    '{attribute} must be "{requiredValue}".' => '{attribute} deve essere "{requiredValue}".',
    '{attribute} must be a number.' => '{attribute} deve essere un numero.',
    '{attribute} must be a string.' => '{attribute} deve essere una stringa.',
    '{attribute} must be an integer.' => '{attribute} deve essere un numero intero.',
    '{attribute} must be either "{true}" or "{false}".' => '{attribute} deve essere "{true}" oppure "{false}".',
    '{attribute} must be no greater than {max}.' => '{attribute} non deve essere maggiore di {max}.',
    '{attribute} must be no less than {min}.' => '{attribute} non deve essere minore di {min}.',
    '{attribute} should contain at least {min, number} {min, plural, one{character} other{characters}}.' => '{attribute} dovrebbe contenere almeno {min, number} {min, plural, one{carattere} other{caratteri}}.',
    '{attribute} should contain at most {max, number} {max, plural, one{character} other{characters}}.' => '{attribute} dovrebbe contenere al massimo {max, number} {max, plural, one{carattere} other{caratteri}}.',
    '{attribute} should contain {length, number} {length, plural, one{character} other{characters}}.' => '{attribute} dovrebbe contenere {length, number} {length, plural, one{carattere} other{caratteri}}.',
    '{delta, plural, =1{a day} other{# days}} ago' => '{delta, plural, =1{un giorno} other{# giorni}} fa',
    '{delta, plural, =1{a minute} other{# minutes}} ago' => '{delta, plural, =1{un minuto} other{# minuti}} fa',
    '{delta, plural, =1{a month} other{# months}} ago' => '{delta, plural, =1{un mese} other{# mesi}} fa',
    '{delta, plural, =1{a second} other{# seconds}} ago' => '{delta, plural, =1{un secondo} other{# secondi}} fa',
    '{delta, plural, =1{a year} other{# years}} ago' => '{delta, plural, =1{un anno} other{# anni}} fa',
    '{delta, plural, =1{an hour} other{# hours}} ago' => '{delta, plural, =1{un\'ora} other{# ore}} fa',
    '{nFormatted} B' => '{nFormatted} B',
    '{nFormatted} GB' => '{nFormatted} GB',
    '{nFormatted} GiB' => '{nFormatted} GiB',
    '{nFormatted} KB' => '{nFormatted} KB',
    '{nFormatted} KiB' => '{nFormatted} KiB',
    '{nFormatted} MB' => '{nFormatted} MB',
    '{nFormatted} MiB' => '{nFormatted} MiB',
    '{nFormatted} PB' => '{nFormatted} PB',
    '{nFormatted} PiB' => '{nFormatted} PiB',
    '{nFormatted} TB' => '{nFormatted} TB',
    '{nFormatted} TiB' => '{nFormatted} TiB',
    '{nFormatted} {n, plural, =1{byte} other{bytes}}' => '{nFormatted} {n, plural, =1{byte} other{byte}}',
    '{nFormatted} {n, plural, =1{gibibyte} other{gibibytes}}' => '{nFormatted} {n, plural, =1{gibibyte} other{gibibyte}}',
    '{nFormatted} {n, plural, =1{gigabyte} other{gigabytes}}' => '{nFormatted} {n, plural, =1{gigabyte} other{gigabyte}}',
    '{nFormatted} {n, plural, =1{kibibyte} other{kibibytes}}' => '{nFormatted} {n, plural, =1{kibibyte} other{kibibyte}}',
    '{nFormatted} {n, plural, =1{kilobyte} other{kilobytes}}' => '{nFormatted} {n, plural, =1{kilobyte} other{kilobyte}}',
    '{nFormatted} {n, plural, =1{mebibyte} other{mebibytes}}' => '{nFormatted} {n, plural, =1{mebibyte} other{mebibyte}}',
    '{nFormatted} {n, plural, =1{megabyte} other{megabytes}}' => '{nFormatted} {n, plural, =1{megabyte} other{megabyte}}',
    '{nFormatted} {n, plural, =1{pebibyte} other{pebibytes}}' => '{nFormatted} {n, plural, =1{pebibyte} other{pebibyte}}',
    '{nFormatted} {n, plural, =1{petabyte} other{petabytes}}' => '{nFormatted} {n, plural, =1{petabyte} other{petabyte}}',
    '{nFormatted} {n, plural, =1{tebibyte} other{tebibytes}}' => '{nFormatted} {n, plural, =1{tebibyte} other{tebibyte}}',
    '{nFormatted} {n, plural, =1{terabyte} other{terabytes}}' => '{nFormatted} {n, plural, =1{terabyte} other{terabyte}}',
];
