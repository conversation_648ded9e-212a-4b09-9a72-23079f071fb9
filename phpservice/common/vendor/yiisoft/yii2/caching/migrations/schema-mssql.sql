/**
 * Database schema required by \yii\caching\DbCache.
 *
 * <AUTHOR> <<EMAIL>>
 * <AUTHOR> D Munir <<EMAIL>>
 * @link http://www.yiiframework.com/
 * @copyright 2008 Yii Software LLC
 * @license http://www.yiiframework.com/license/
 * @since 2.0.7
 */

drop table if exists [cache];

create table [cache]
(
    [id]  varchar(128) not null,
    [expire] integer,
    [data]   BLOB,
    primary key ([id])
);
