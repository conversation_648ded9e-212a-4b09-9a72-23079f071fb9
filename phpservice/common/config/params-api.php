<?php
// 基础接口
// sso 对外 apis 接口可以支持的方法 
$apiparams['ssobase_host'] = $ssobase_host = $commonServerIP; 
$apiparams['ssobase_port'] = $ssobase_port = $commonServerPort;
$apiparams['ssobase_path'] = $ssobase_path = '/yjbapi/core/sso/';

//通行证相关
$apiparams['passport_host'] = $passport_host = $commonServerIP;
$apiparams['passport_port'] = $passport_port = $commonServerPort;
$apiparams['passport_path'] = $passport_path = '/yjbapi/core/passport/';

//通用密码服务ups相关
$apiparams['ups_host'] = $ups_host = $commonServerIP;
$apiparams['ups_port'] = $ups_port = $commonServerPort;
$apiparams['ups_path'] = $ups_path = '/yjbapi/upsservice/';

//账户视图
$apiparams['user_account_host'] = $user_account_host = $commonServerIP;
$apiparams['user_account_port'] = $user_account_port = $commonServerPort;
$apiparams['user_account_path'] = $user_account_path = '/yjbapi/user/account/';

//公告类
$apiparams['announce_host'] = $announce_host = $commonServerIP;
$apiparams['announce_port'] = $announce_port = $commonServerPort;
$apiparams['announce_path'] = $announce_path = '/yjbapi/core/announcement/';

//时间服务接口
$apiparams['timeservice_host'] = $timeservice_host = $commonServerIP;
$apiparams['timeservice_port'] = $timeservice_port = $commonServerPort;
$apiparams['timeservice_path'] = $timeservice_path = '/yjbapi/core/timeservice/';

//mobile-api
$apiparams['mobile_api_host'] = $mobile_api_host = $commonServerIP;
$apiparams['mobile_api_port'] = $mobile_api_port = $commonServerPort;
$apiparams['mobile_api_path'] = $mobile_api_path = '/yjbmobile/api/core/';

//SAS
$apiparams['sas_host'] = $sas_host = $commonServerIP;
$apiparams['sas_port'] = $sas_port = $commonServerPort;
$apiparams['sas_path'] = $sas_path = '/yjbapi/sas/';

$apiparams['account_host'] = $account_host = $commonServerIP;
$apiparams['account_port'] = $account_port = $commonServerPort;
$apiparams['account_path'] = $account_path = '/yjbapi/account/';

$apiparams['common_host'] = $common_host = $commonServerIP;
$apiparams['common_port'] = $common_port = $commonServerPort;
$apiparams['common_path'] = $common_path = '/yjbapi/';

$apiparams['comm_post_apis'] = array( 'redeem', 'next_tradedate', 'userMessageSubscribeUpdate', 'trigger_amount_update','redeem_undo', 
 	'checkInstantToken','findDurableTokenInfo','userInfoQuery','getPassword','authenticate'		
);
$apiparams['comm_apis'] = array(

	'instant_tokenApply' => array( //验证一次性令牌
		'apiname'=>'instant_token/apply',
		'server' => $sas_host,
		'port' => $sas_port,
		'path' => $sas_path
	),
	'instant_tokenCheck' => array( //验证一次性令牌
		'apiname'=>'instant_token/check',
		'server' => $sas_host,
		'port' => $sas_port,
		'path' => $sas_path
	),
	'session_infoQuery' => array( //根据instant_token返回会话的相关信息
		'apiname'=>'session_info/query',
		'server' => $sas_host,
		'port' => $sas_port,
		'path' => $sas_path
	),
	'cipher_tokenPasswordQuery' => array( //根据instant_token返回会话的相关信息
		'apiname'=>'cipher_token/password/query',
		'server' => $sas_host,
		'port' => $sas_port,
		'path' => $sas_path
	),
	
	//array(
	//	'ssobase' => array(
			// sso 相关
			'registerOnlineUsersSession' => array( //注册全局回话
				'apiname'=>'registerOnlineUsersSession',
				'server' => $ssobase_host,
				'port' => $ssobase_port,
				'path' => $ssobase_path
			),
			'applyInstantToken' => array(  //申请一次性令牌
				'apiname'=>'applyInstantToken',
				'server' => $ssobase_host,
				'port' => $ssobase_port,
				'path' => $ssobase_path
			),
			'checkInstantToken' => array(  //验证一次性令牌
				'apiname'=>'checkInstantToken',
				'server' => $ssobase_host,
				'port' => $ssobase_port,
				'path' => $ssobase_path
			),
			'deleteExpiredDurableToken' => array(  //删除指定的持久令牌
				'apiname'=>'deleteExpiredDurableToken',
				'server' => $ssobase_host,
				'port' => $ssobase_port,
				'path' => $ssobase_path
			),
			'getCipherTokenByDurableToken' => array(  //通过durable_token获取密码令牌
				'apiname'=>'getCipherTokenByDurableToken',
				'server' => $ssobase_host,
				'port' => $ssobase_port,
				'path' => $ssobase_path
			),
			'extendDurableTokenEffectiveTime' => array(  //延长令牌有效时间
				'apiname'=>'extendDurableTokenEffectiveTime',
				'server' => $ssobase_host,
				'port' => $ssobase_port,
				'path' => $ssobase_path
			),
			'findSessionIdByDurableToken' => array(  //通过druable_token返回对应的seesion_id
				'apiname'=>'findSessionIdByDurableToken',
				'server' => $ssobase_host,
				'port' => $ssobase_port,
				'path' => $ssobase_path
			),
			'findExpiredTimeByDurableToken' => array(  //通过druable_token返回对应的过期时间
				'apiname'=>'findExpiredTimeByDurableToken',
				'server' => $ssobase_host,
				'port' => $ssobase_port,
				'path' => $ssobase_path
			),
			'findDurableTokenInfo' => array(  //根据durable_token返回会话的相关信息
				'apiname'=>'findDurableTokenInfo',
				'server' => $ssobase_host,
				'port' => $ssobase_port,
				'path' => $ssobase_path
			),
			'getDurableTokenInfoByInstantToken'=>array( //根据instant_token返回sso的会话相关信息
				'apiname'=>'getDurableTokenInfoByInstantToken',
				'server' => $ssobase_host,
				'port' => $ssobase_port,
				'path' => $ssobase_path
			),
	//	)
	//),
	
	//array(
	//	'passport' => array(
			// 通行证相关
			'createPassport' => array(  //创建通行证
				'apiname'=>'createPassport',
				'server' => $passport_host,
				'port' => $passport_port,
				'path' => $passport_path
			),
			'updatePassword' => array(  //更新通行证密码
				'apiname'=>'updatePassword',
				'server' => $passport_host,
				'port' => $passport_port,
				'path' => $passport_path
			),
			'updatePassword' => array(  //重置通行证密码
				'apiname'=>'updatePassword',
				'server' => $passport_host,
				'port' => $passport_port,
				'path' => $passport_path
			),
			'activePassport' => array(  //激活通行证
				'apiname'=>'activePassport',
				'server' => $passport_host,
				'port' => $passport_port,
				'path' => $passport_path
			),
			'frozePassport' => array(  //冻结通行证
				'apiname'=>'frozePassport',
				'server' => $passport_host,
				'port' => $passport_port,
				'path' => $passport_path
			),
			'expirePassport' => array(  //更新通行证到期日
				'apiname'=>'expirePassport',
				'server' => $passport_host,
				'port' => $passport_port,
				'path' => $passport_path
			),
			'addAccount' => array(  //通行证账号绑定创建操作
				'apiname'=>'addAccount',
				'server' => $passport_host,
				'port' => $passport_port,
				'path' => $passport_path
			),
			'updateAccount' => array(  //通行证账号绑定更新操作
				'apiname'=>'updateAccount',
				'server' => $passport_host,
				'port' => $passport_port,
				'path' => $passport_path
			),
			'getAccount' => array(  //通行证账号绑定查询操作,查询某类账号
				'apiname'=>'getAccount',
				'server' => $passport_host,
				'port' => $passport_port,
				'path' => $passport_path
			),
			'getAccounts' => array(  //通行证账号绑定查询操作,查询全部类型的账号
				'apiname'=>'getAccounts',
				'server' => $passport_host,
				'port' => $passport_port,
				'path' => $passport_path
			),
			'authenticate' => array(  //认证服务
				'apiname'=>'authenticate',
				'server' => $passport_host,
				'port' => $passport_port,
				'path' => $passport_path
			),
			'removeAccount' => array(  //通行证账号绑定移除操作
				'apiname'=>'removeAccount',
				'server' => $passport_host,
				'port' => $passport_port,
				'path' => $passport_path
			),
			'getPassport' => array(  //通行证账号信息查询
				'apiname'=>'removeAccount',
				'server' => $passport_host,
				'port' => $passport_port,
				'path' => $passport_path
			),
			'removeFundAccountAndOpenIdAccount' => array(  //通行证账号绑定移除操作--解除资金账号和微信号专用
				'apiname'=>'removeFundAccountAndOpenIdAccount',
				'server' => $passport_host,
				'port' => $passport_port,
				'path' => $passport_path
			),
			'userAcctBind' => array(  //用户账户绑定
				'apiname'=>'userAcctBind',
				'server' => $passport_host,
				'port' => $passport_port,
				'path' => $passport_path
			),
			'removeFundOpenIdForFundAccount' => array(  //移除微信号操作
				'apiname'=>'removeFundOpenIdForFundAccount',
				'server' => $passport_host,
				'port' => $passport_port,
				'path' => $passport_path
			),
			'acctBind' => array(  //账号绑定
				'apiname'=>'acctBind',
				'server' => $passport_host,
				'port' => $passport_port,
				'path' => $passport_path
			),
			'removeAcctBind' => array(  //账号解绑
				'apiname'=>'removeAcctBind',
				'server' => $passport_host,
				'port' => $passport_port,
				'path' => $passport_path
			),
			'acctBindQuery' => array(  //账号绑定查询
				'apiname'=>'acctBindQuery',
				'server' => $passport_host,
				'port' => $passport_port,
				'path' => $passport_path
			),
			'deletePassport' => array(  //删除通行证
				'apiname'=>'deletePassport',
				'server' => $passport_host,
				'port' => $passport_port,
				'path' => $passport_path
			),
			'userAcctBindQuery' => array(  //账户绑定查询
				'apiname'=>'userAcctBindQuery',
				'server' => $passport_host,
				'port' => $passport_port,
				'path' => $passport_path
			),
			'removeFundAccountAndOpenIdAccount' => array(  //账户绑定查询
				'apiname'=>'removeFundAccountAndOpenIdAccount',
				'server' => $passport_host,
				'port' => $passport_port,
				'path' => $passport_path
			),
            
	//	)
	//),
	
	//array(
	//	'ups' => array(
			// 通用密码服务ups相关
			'savePassword' => array( // UPS_0001 保存密码
				'apiname'=>'savePassword',
				'server' => $ups_host,
				'port' => $ups_port,
				'path' => $ups_path,
				'template' => array(
					"actionId" => "UPS_0001",
					"params" => array(
						"app_id" => "{app_id}",     // weixin
						"app_uuid" => "{app_uuid}",   // uuid
						"cipher_content" => "{cipher_content}",     //1Admin2
						"cipher_type" => "{cipher_type}",     //1
						"duration_hours" => "{duration_hours}", //3
					)
				)
			),  
			'getPassword' => array( // UPS_002 提取密码
				'apiname'=>'getPassword',
				'server' => $ups_host,
				'port' => $ups_port,
				'path' => $ups_path,
				'template' => array(
					"actionId" => "UPS_0002",
					"params" => array(
						"cipher_token" => '{cipher_token}'
					)
				)
			),
			'deletePassword' => array(  // UPS_0003  注销密码
				'apiname'=>'deletePassword',
				'server' => $ups_host,
				'port' => $ups_port,
				'path' => $ups_path,
				'template' => array(
					"actionId" => "UPS_0003",
					"params" => array(
					  "cipher_token" => '{cipher_token}'
					)
				)
			),
			'extendPassword' => array(  // UPS_0004  延长密码失效时间
				'apiname'=>'extendPassword',
				'server' => $ups_host,
				'port' => $ups_port,
				'path' => $ups_path,
				'template' => array(
					"actionId" => "UPS_0004",
					"params" => array(
						"cipher_token" => "{cipher_token}",
						"duration_hours" => "{duration_hours}"
					)
				)
			),
			'getPasswordExpireTime' => array(  // UPS_0005  获取密码失效时间
				'apiname'=>'getPasswordExpireTime',
				'server' => $ups_host,
				'port' => $ups_port,
				'path' => $ups_path,
				'template' => array(
					"actionId" => "UPS_0005",
					"params" => array(
						"cipher_token" => "{cipher_token}"
					)
				)
			),
	//	)
	//),
	
		// 账户视图
		'assetSummaryInfo' => array(  // UACT_001 证券账户资产信息统计查询
				'apiname'=>'assetSummaryInfo',
				'server' => $user_account_host,
				'port' => $user_account_port,
				'path' => $user_account_path
		),
		'assetStockHolderInfo' => array(  // UACT_002 证券账户当前持股信息
				'apiname'=>'assetStockHolderInfo',
				'server' => $user_account_host,
				'port' => $user_account_port,
				'path' => $user_account_path
		),
		'userInfoQuery' => array(  // UACT_003 用户信息查询
				'apiname'=>'userInfoQuery',
				'server' => $user_account_host,
				'port' => $user_account_port,
				'path' => $user_account_path
		),
		'userInfoUpdate' => array(  // UACT_004 用户信息修改
				'apiname'=>'userInfoUpdate',
				'server' => $user_account_host,
				'port' => $user_account_port,
				'path' => $user_account_path
		),
		'userBirthdayQuery' => array(  // UACT_005 生日查询服务
				'apiname'=>'userBirthdayQuery',
				'server' => $user_account_host,
				'port' => $user_account_port,
				'path' => $user_account_path
		),
		'queryUserAccountsInfo' => array(  // UACT_006  我的账户信息
				'apiname'=>'queryUserAccountsInfo',
				'server' => $user_account_host,
				'port' => $user_account_port,
				'path' => $user_account_path
		),
		'isyjbclient' => array(  // UACT_007  是否为佣金宝客户
				'apiname'=>'isyjbclient',
				'server' => $user_account_host,
				'port' => $user_account_port,
				'path' => $user_account_path
		),
	
	//请求公告的接口
	'query'=>array(
		'apiname'=>'query',
		'server' => $announce_host,
		'port' => $announce_port,
		'path' => $announce_path
	),
	//时间服务
	'checkTransactionTime' => array(  // 交易时间查询
		'apiname'=>'checkTransactionTime',
		'server' => $timeservice_host,
		'port' => $timeservice_port,
		'path' => $timeservice_path
	),
	//mobile_api
	'servertime' => array(  // 获取服务器时间
		'apiname'=>'servertime',
		'server' => $mobile_api_host,
		'port' => $mobile_api_port,
		'path' => $mobile_api_path,
	),

	//两融账号查询
	'marginFundAccountQuery' => array(
		'apiname'=>'margin/fundaccount/query',
		'server' => $account_host,
		'port' => $account_port,
		'path' => $account_path,
	),

	//条件检查查询
	'checkUp' => array(
		'apiname'=>'checkup/check',
		'server' => $account_host,
		'port' => $account_port,
		'path' => $account_path,
	),

	//客户资产账号查询-用于校验token换取的是否为信用资金账号
	'counter.web.fundaccount.query' => array(
		'apiname'=>'counter/web/fundaccount/query',
		'server' => $common_host,
		'port' => $common_port,
		'path' => $common_path,
	),

);

return $apiparams;

