<?php
$ENV = 'local';
//基础信息配置
$evnConfig = array(
	'local'=>array(
		'commonServerIP'=>'**************',  //API请求的IP
		'commonServerPort'=>'8080',   //API请求的Port
		'crhCommonServerPort'=>'83',  //财人汇API请求的Port
		'AES_KEY'=>'g6j0z0q1w0e9bapp',    //对称加密秘钥建议在公共配置中设置  add by yancy
	),
	'dev'=>array(
		'commonServerIP'=>'**************',  //API请求的IP
		'commonServerPort'=>'8080',   //API请求的Port
		'crhCommonServerPort'=>'83',  //财人汇API请求的Port
		'AES_KEY'=>'g6j0z0q1w0e9bapp'
	),
	'test'=>array(
		'commonServerIP'=>'**************',  //API请求的IP
		'commonServerPort'=>'8080',   //API请求的Port
		'crhCommonServerPort'=>'83',  //财人汇API请求的Port
		'AES_KEY'=>'g6j0z0q1w0e9bapp'
	),
	'emulate'=>array(
		'commonServerIP'=>'**************',  //API请求的IP
		'commonServerPort'=>'8080',   //API请求的Port
		'crhCommonServerPort'=>'83',  //财人汇API请求的Port
		'AES_KEY'=>'g6j0z0q1w0e9bapp'
	),
	'product'=>array(
		'commonServerIP'=>'**************',  //API请求的IP
		'commonServerPort'=>'8080',   //API请求的Port
		'crhCommonServerPort'=>'83',  //财人汇API请求的Port
		'AES_KEY'=>'g6j0z0q1w0e9bapp'
	)
);

$commonServerIP = $evnConfig[$ENV]['commonServerIP'];
$commonServerPort = $evnConfig[$ENV]['commonServerPort'];
$crhCommonServerPort = $evnConfig[$ENV]['crhCommonServerPort'];
$AES_KEY = $evnConfig[$ENV]['AES_KEY'];

$commonInfo = [
	'commonServerIP'=>$commonServerIP,
	'commonServerPort'=>$commonServerPort,
	'crhCommonServerPort'=>$crhCommonServerPort,
	'AES_KEY'=>$AES_KEY,
	// 'qqStockAPi'=>'http://************:10076/kesb_req',
	'qqStockAPi'=>'http://**************:28080/kesb_req', // 金微蓝新的仿真地址
	'qqStockUseSign'=>true,
	'qqStockSignKey'=>'KlzukIhhlKbLDqsvVB',
	'goSiteCheck'=>true,
];

return $commonInfo;
