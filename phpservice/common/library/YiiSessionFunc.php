<?php
namespace common\library;

use Yii;
use yii\web\Session;
use yii\web\Cookie;
use common\library\LogFunc;


class YiiSessionFunc 
{
	static function startSession($expire = 0){
		if ($expire == 0) {
			$expire = ini_get('session.gc_maxlifetime');
		} else {
			ini_set('session.gc_maxlifetime', $expire);
		}
		ini_set('session.cookie_lifetime', Yii::$app->params['timeout']);
		ini_set('session.cookie_path', Yii::$app->params['session_path']);
		ini_set('session.name', Yii::$app->params['session_name']);
		
		$session = Yii::$app->session;
		
		$cookieparams = $session->getCookieParams();
		$session->setCookieParams($cookieparams);
		LogFunc::_log("debug",'行'.__LINE__.'cookieparams参数----@'.json_encode($cookieparams,true),'session');
		LogFunc::_log("debug",'行'.__LINE__.'cookieparams参数----@'.json_encode($cookieparams,true),'page');
		
		$session->open();
		
		$url = $_SERVER['SERVER_NAME'].$_SERVER['REQUEST_URI'];
		LogFunc::_log("debug",'行'.__LINE__.'我们的家乡COOKIE信息----@'.json_encode($_COOKIE,true).'@--url--'.$url,'session');
		LogFunc::_log("debug",'行'.__LINE__.'我们的家乡HTTP_COOKIE----@'.$_SERVER['HTTP_COOKIE'],'session');
		LogFunc::_log("debug",'行'.__LINE__.'我们的家乡COOKIE信息----@'.json_encode($_COOKIE,true).'@--url--'.$url,'page');
		LogFunc::_log("debug",'行'.__LINE__.'我们的家乡HTTP_COOKIE----@'.$_SERVER['HTTP_COOKIE'],'page');
	}
	
	//清空和销毁session  (能够清空但是不能清空服务器的session文件)
	static function clearSession()
	{
		$session = Yii::$app->session;
		$session->destroy();
	}
	
	/**
	 *  写Session  update by yancy
	 *  2016-11-07
	 */
    static public function writeSession($data){
    	$session = Yii::$app->session;
    	if(is_array($data) && count($data)>0) {
    		foreach ($data as $k=>$v) {
    			$k = Yii::$app->params['cookie_prefix'].$k;	// add prefix to key
    			/* $_SESSION[$k] = $v;
				//严格控制超时时间
				$_SESSION[$k.'_expiretime'] = time() + Yii::$app->params['timeout']; */
				$session[$k] = $v;
				$session[$k.'_expiretime'] = time() + Yii::$app->params['timeout'];
    		}
    	}
    }
    
    /**
     *  读取Session update by yancy
	 *   2016-11-07
	 */
    static public function readSession($key)
    {
    	$session = Yii::$app->session;
    	$key = Yii::$app->params['cookie_prefix'].$key;
    	if($session[$key] != ''){
    		if(isset($session[$key.'_expiretime'])){
    			if($session[$key.'_expiretime'] < time()){
    				unset($session[$key]);
    				unset($session[$key.'_expiretime']);
    			}else{
    				$session[$key.'_expiretime'] = time() + Yii::$app->params['timeout'];// 刷新时间戳
    				if(is_array($session[$key])){
    					return $session[$key];
    				}else{
    					return (string)$session[$key];
    				}
    			}
    		}else{
    			unset($session[$key]);
    		}
    	}
    	return '';
    }
  
    
    //普通方式写 cookie
    static public function writeCookie($data){
    	if(is_array($data) && count($data)>0) {
    		foreach ($data as $k=>$v) {
    			$k = Yii::$app->params['cookie_prefix'].$k;	// add prefix to key
    			if(is_array($v))
    			{
    				$v = json_encode($v);
    			}
    			$_COOKIE[$k] = $v; // no need to refresh page to make cookie work
    			setcookie($k, $v);
    		}
    	}
    }
    
    //普通方式读cookie
    static public function readCookie($key){
    	$key = Yii::$app->params['cookie_prefix'].$key;
    	if($_COOKIE[$key] != '') {
    		if(json_decode($_COOKIE[$key], true) != NULL) {
    			return json_decode($_COOKIE[$key], true);
    		}
    		else {
    			return $_COOKIE[$key];
    		}
    	}
    	return '';
    }
    
    // 写 Yiicookie
    static public function writeYiiCookie($data)
    {
    	$cookies = Yii::$app->response->cookies;
    	if(is_array($data) && count($data)>0) {
    		foreach ($data as $k=>$v) {
    			$k = Yii::$app->params['cookie_prefix'].$k;	// add prefix to key
    			if(is_array($v))
    			{
    				$v = json_encode($v);
    			}
    			$cookies->add(new Cookie([
    				'name'=>$k,
    				'value' => $v,
    				'expire'=>time()+Yii::$app->params['timeout']
    			]));
    		}
    	}
    }
    
    //读Yiicookie
    static public function readYiiCookie($key){
    	$cookies = Yii::$app->request->cookies;
    	
    	$key = Yii::$app->params['cookie_prefix'].$key;
    	if($cookies->getValue($key, '') != '') {
    		if(json_decode($cookies->getValue($key,''), true) != NULL) {
    			return json_decode($cookies->getValue($key,''), true);
    		}
    		else {
    			return $cookies->getValue($key,'');
    		}
    	}
    	return '';
    }
}

