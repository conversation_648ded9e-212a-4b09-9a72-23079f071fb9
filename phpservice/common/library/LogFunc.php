<?php
namespace common\library;

use Yii;
/**
 * ContactForm is the model behind the contact form.
 */
class LogFunc 
{
	static public function _log($type, $content, $log='client')
	{
		$is_openfrontlog = Yii::$app->params['is_openfrontlog'];
		if($is_openfrontlog){
			$date = date('Y-m-d');
			$content = self::remove_password($content);
			//$content = substr($content,0,1000);
			$file = fopen(Yii::$app->params['log_path'].$log.$date.'.log',"a");
			//var_dump($file,Yii::$app->params['log_path'].$log.$date.'.log');exit;
			if(Yii::$app->params['env'] =='dev' || Yii::$app->params['env'] =='local'){
				$session_id = '';
			}else{
				$session_id = session_id();
			}
			fwrite($file, date('Y-m-d H:i:s')." -- ".$session_id.'--'.$type." -- ".$content." \r\n");
			fclose($file);
		}
	}
	
	static public function statlog($type,$content,$code=200,$log='apis')
	{
		$date = date('Y-m-d');
		$content = self::remove_password($content);
		$file = fopen(Yii::$app->params['log_path'].$log.$date.'.log',"a");
		/* $logArray = array(
			'date'=>Date('Y-m-d H:i:s'),
			'session_id'=>session_id(),
			'style'=>$type,
			'code'=>$code,
			'result'=>$content,
		);
		fwrite($file,json_encode($logArray,true)."\r\n");
		 */
		$logInfo = 
			Date('Y-m-d H:i:s').';'.
			session_id().';'.
			$type.';'.
			$code.';'.
			$content.';'
		;
		fwrite($file,$logInfo."\r\n");
		fclose($file);
	}
	

	static public function remove_password($content)
	{
		$tmp = is_array($content) ? $content : json_decode($content, true);
		if(is_array($tmp))
		{
			foreach($tmp as $k => $v)
			{
				if(is_array($v))
				{
					$tmp[$k] = json_decode(self::remove_password($v), true);
				}
				else if(in_array($k, Yii::$app->params['ignore_keys']))
				{
					$tmp[$k] = '****';
				}
			}
			$content = json_encode($tmp);
		}
		else
		{
			if(strpos($content,'post_curl_value')>=0 && strpos($content,'post_curl_value') !=false){
				$_t = explode(' -- ', $content);//post method
			}else{
				$_t = explode('?', $content);
			}

			if(count($_t)>0)
			{
				$tmp = explode('&', $_t[1]);
				for($a=0; $a<count($tmp); $a++)
				{
					foreach(Yii::$app->params['ignore_keys'] as $ignore_key)
					{
						if(false !== strpos($tmp[$a], $ignore_key))
						{
							$tmp[$a] = $ignore_key."=****";
						}
					}
				}
				$content = $_t[0].'?'.implode('&', $tmp);
			}
		}
		return $content;
	}
}
