<?php
namespace common\library;

use Yii;
use common\library\SessionFunc;
use common\library\YiiSessionFunc;
use common\library\SsoFunc;
use common\library\AESFunc;

class CommonFunc 
{
	//判断是否是微信
    static public function checkWeChat() 
    {
        $user_agent = strtoupper($_SERVER["HTTP_USER_AGENT"]);
        if (strpos($user_agent, 'MICROMESSENGER') !== false) {
            return true; //微信
        } 
        return false;
    }
    
    //前后端account_type转化
    static  public function getAccountType($account_type)
    {
    	return array(0=>1,1=>10,15=>13)[$account_type];
    }
    
    //判断用户是否登录超时（仅仅判断用户的session数据是否超时和是否修改过密码）
    static public function checkLoginFromSession($account_type = 0)
    {
    	//账号关系转化(0=>1,1=>10)
    	$account_type = self::getAccountType($account_type);
    	
    	$isLogin = false;
    	$SsoFunc = new SsoFunc();
    	if($account_type == 1){  //普通资金账号且$sso_session存在
    		$sso_session = unserialize(base64_decode(SessionFunc::readSession('sso_message')));
    		if($sso_session){
				LogFunc::_log("debug", "普通资金账号checkLogin，值是--".json_encode($sso_session,true), $log='page');
				$isLogin = true;
    		}
    	} else if ($account_type == 10){  //信用资金账号且$sso_credit_session存在
    		$sso_credit_session = unserialize(base64_decode(SessionFunc::readSession('sso_credit_message')));
    		if($sso_credit_session){
    			LogFunc::_log("debug", "信用资金账号checkLogin，值是--".json_encode($sso_credit_session,true), $log='page');
    			$isLogin = true;
    		}
    	}else{
    		//session超时了
    		LogFunc::_log("debug", "code->-882,instant_token为空时，session数据不存在或被清空了", $log='page');
    		$isLogin = false;
    	}
    	return $isLogin;
    }
    
    //判断是否登录及用户的相关信息(1、从instant_token或session中获取数据，2、认证密码是否正确，3、支持普通和信用账号的相关信息
    // 4、解析&存储op_station)
    static public function checkLoginInfo($instant_token = '',$account_type,$op_station = '')
    {
    	//账号关系转化(0=>1,1=>10)
    	$account_type = self::getAccountType($account_type);
    	$SsoFunc = new SsoFunc();
    	
    	LogFunc::_log("debug",'行'.__LINE__.'checkLoginInfo得到的参数是instant_token--'.$instant_token.'--account_type转化值--'.
    			$account_type.'--$op_station值--'.$op_station,'page');
    	if($instant_token != ''){  //session信息不存在且直接instant_token请求
    		//update by yancy,处理opstation信息
    		$key = Yii::$app->params['AES_KEY']; //读取配置参数
    		$op_station = AESFunc::decrypt(self::webToBase64($op_station), $key);
    		LogFunc::_log("debug","转化后的op_station是：".$op_station, $log='page');
    		
    		$res = $SsoFunc->getUserInfoByInstantToken($instant_token,$op_station);//根据instant_token获取所有信息
    		if ($res['code'] == '0') {
    			if($account_type == 1){  //普通资金账号
    				$sso_session = $res['result'];
    				$sso_session['op_station'] = $op_station;
    	
    				SessionFunc::writeSession(array(
    					'sso_message'=>base64_encode(serialize($sso_session))
    				));
    				//print_r("<pre>");print_r(SessionFunc::readSession('sso_message'));exit;
    				LogFunc::_log("debug", "【普通资金账号用户信息来自API】，值是--".json_encode($sso_session,true), $log='page');
    				//var_dump($sso_session);exit;
    				return array('isLogin'=>true,'code'=>0,'result'=>$sso_session);
    			} elseif ($account_type == 10){  //信用资金账号
    				$sso_credit_session = $res['result'];
    				$sso_credit_session['op_station'] = $op_station;
    				SessionFunc::writeSession(array(
    					'sso_credit_message'=>base64_encode(serialize($sso_credit_session))
    				));
    				//var_dump($sso_credit_session);exit;
    				LogFunc::_log("debug", "【信用资金账号用户信息来自API】，值是--".json_encode($sso_credit_session,true), $log='page');
    				return array('isLogin'=>true,'code'=>0,'result'=>$sso_credit_session);
    			}
    		} elseif ($res['code'] == -996){ //instant_token无效，获取durable失败
    			return array('isLogin'=>false,'code'=>-996,'result'=>$res['message']);
    		} elseif ($res['code'] == -997){ //durable_token无效，获取sso信息失败
    			return array('isLogin'=>false,'code'=>-997,'result'=>$res['message']);
    		} elseif ($res['code'] == -998){  //获取用户信息失败
    			return array('isLogin'=>false,'code'=>-998,'result'=>$res['message']);
    		} else if ($res['code'] == -999){  //获取用户密码失败
    			return array('isLogin'=>false,'code'=>-999,'result'=>$res['message']);
    		} else if($res['code'] == -1000){  //认证密码失败，可能是用户更改了密码
    			return array('isLogin'=>false,'code'=>-1000,'result'=>$res['message']);
    		}
    		//997,998,999,1000出现说明已经登录，前端需要注销登录，如果不注销则会在sso桥接的地方报错。996说明instant_token无效。也是未登录状态
    	} else {
    		//获取session新的对应的用户sso信息
    		if($account_type == 1){    //信用账号
    			$sso_session = unserialize(base64_decode(SessionFunc::readSession('sso_message')));
    		} elseif ($account_type == 10){ //普通账号
    			$sso_credit_session = unserialize(base64_decode(SessionFunc::readSession('sso_credit_message')));
    		}
    		
    		if($account_type == 1 && $sso_session){ //普通资金账号且$sso_session存在
    			LogFunc::_log("debug", "【普通资金账号用户信息来自session】，值是--".json_encode($sso_session,true), $log='page');
    			return array('isLogin'=>true,'code'=>0,'result'=>$sso_session);
    		} else if ($account_type == 10 && $sso_credit_session){ //信用资金账号且$sso_credit_session存在
    			LogFunc::_log("debug", "【信用资金账号用户信息来自session】，值是--".json_encode($sso_credit_session,true), $log='page');
    			return array('isLogin'=>true,'code'=>0,'result'=>$sso_credit_session);
    		}else{
    			return array('isLogin'=>false,'code'=>-882,'result'=>'instant_token为空时，session数据不存在或被清空了');
    		}
    	}
    }


	/**
	 * 判断用户是否登录超时（仅仅判断用户的session数据是否超时）
	 * add by JinYC
	 */
	static public function checkLoginFromSessionV2($account_type = 0)
	{
		//账号关系转化(0=>1,1=>10)
		$account_type = self::getAccountType($account_type);

		$isLogin = false;
		if($account_type == 1){  //普通资金账号且$sso_session存在
			$sso_session = unserialize(base64_decode(SessionFunc::readSession('sso_message')));
			LogFunc::_log("debug","转化后的op_station是：".$sso_session, $log='session_content');
			if($sso_session){
				$isLogin = true;
			}
		} else if ($account_type == 10){  //信用资金账号且$sso_credit_session存在
			$sso_credit_session = unserialize(base64_decode(SessionFunc::readSession('sso_credit_message')));
			if($sso_credit_session){
				$isLogin = true;
			}
		}else{
			//session超时了
			//return array('isLogin'=>false,'code'=>-882,'result'=>'instant_token为空时，session数据不存在或被清空了');
			$isLogin = false;
		}
		return $isLogin;
	}
	/**
	 * 判断是否登录及用户的相关信息(1、从instant_token或session中获取数据，2、支持普通和信用账号的相关信息  3、解析&存储op_station)
	 *  add by JinYC
	 *  注: auth_lv参数请配置在项目(非框架): Yii::$app->params['auth_lv'] 配置中
	 */
	static public function checkLoginInfov2($instant_token = '',$account_type,$op_station = '',$auth_lv)
	{
		//账号关系转化(0=>1,1=>10)
		$account_type = self::getAccountType($account_type);
		$SsoFunc = new SsoFunc();
		LogFunc::_log("debug",'行'.__LINE__.'checkLoginInfo得到的参数是instant_token--'.$instant_token.'--account_type转化值--'.
			$account_type.'--$op_station值--'.$op_station,'page');
		if($instant_token != ''){  //session信息不存在且直接instant_token请求
			$res = $SsoFunc->getUserInfoByInstantTokenV2($instant_token,$op_station,$auth_lv);//根据instant_token获取所有信息
			LogFunc::_log("debug",'行'.__LINE__.'--getUserInfoByInstantTokenV2返回:'.json_encode($res,true),'passport');
			if ($res['code'] == '0') {
				//update by yancy,当确认instant_token 有效时,需要处理opstation信息
				$key = Yii::$app->params['AES_KEY']; //读取配置参数
				$op_station = AESFunc::decrypt(self::webToBase64($op_station), $key);
				//子业务模块未必使用post,get 中出现空格会存在问题,这里对op_station做处理去除空格
				$op_station = preg_replace('# #','',$op_station);

				if($account_type == 1){  //普通资金账号
					$sso_session = $res['result'];
					$sso_session['op_station'] = $op_station;

					SessionFunc::writeSession(array(
						'sso_message'=>base64_encode(serialize($sso_session)),
						'account_type'=>'0'
					));
					LogFunc::_log("debug", "【普通资金账号用户信息来自API】，值是--".json_encode($sso_session,true), $log='page');
					return array('isLogin'=>true,'code'=>0,'result'=>$sso_session);
				} elseif ($account_type == 10){  //信用资金账号
					$sso_credit_session = $res['result'];
					$sso_credit_session['op_station'] = $op_station;
					SessionFunc::writeSession(array(
						'sso_credit_message'=>base64_encode(serialize($sso_credit_session)),
						'account_type'=>'1'
					));
					LogFunc::_log("debug", "【信用资金账号用户信息来自API】，值是--".json_encode($sso_credit_session,true), $log='page');
					return array('isLogin'=>true,'code'=>0,'result'=>$sso_credit_session);
				} elseif  ($account_type == 13){  //期权资金账号
					$sso_option_session = $res['result'];
					$sso_option_session['op_station'] = $op_station;
					SessionFunc::writeSession(array(
						'sso_option_message'=>base64_encode(serialize($sso_option_session)),
						'account_type'=>'13'//
					));
					LogFunc::_log("debug", "【期权资金账号用户信息来自API】，值是--".json_encode($sso_option_session,true), $log='page');
					return array('isLogin'=>true,'code'=>0,'result'=>$sso_option_session);
				}
			} elseif ($res['code'] == -996){ //instant_token无效，获取durable失败
				return array('isLogin'=>false,'code'=>-996,'result'=>$res['message']);
			} elseif ($res['code'] == -997){ //durable_token无效，获取sso信息失败
				return array('isLogin'=>false,'code'=>-997,'result'=>$res['message']);
			} elseif ($res['code'] == -990){ //非主资金账号
				return array('isLogin'=>false,'code'=>-990,'result'=>$res['message']);
			} elseif ($res['code'] == -991){ //不支持的委托方式
				return array('isLogin'=>false,'code'=>-991,'result'=>$res['message']);
			} elseif ($res['code'] == -998){  //获取用户信息失败
				return array('isLogin'=>false,'code'=>-998,'result'=>$res['message']);
			} else if ($res['code'] == -999){  //获取用户密码失败
				return array('isLogin'=>false,'code'=>-999,'result'=>$res['message']);
			} else if($res['code'] == -1000){  //认证密码失败，可能是用户更改了密码
				return array('isLogin'=>false,'code'=>-1000,'result'=>$res['message']);
			}
			//997,998,999,1000出现说明已经登录，前端需要注销登录，如果不注销则会在sso桥接的地方报错。996说明instant_token无效。也是未登录状态
		} else {
			//获取session新的对应的用户sso信息
			if($account_type == 1){    //信用账号
				$sso_session = unserialize(base64_decode(SessionFunc::readSession('sso_message')));
			} elseif ($account_type == 10){ //普通账号
				$sso_credit_session = unserialize(base64_decode(SessionFunc::readSession('sso_credit_message')));
			} elseif ($account_type == 13){ //期权账号
				$sso_option_session = unserialize(base64_decode(SessionFunc::readSession('sso_option_message')));
			}

			if($account_type == 1 && $sso_session){ //普通资金账号且$sso_session存在
				return array('isLogin'=>true,'code'=>0,'result'=>$sso_session);
			} else if ($account_type == 10 && $sso_credit_session){ //信用资金账号且$sso_credit_session存在
				return array('isLogin'=>true,'code'=>0,'result'=>$sso_credit_session);
			} else if ($account_type == 13 && $sso_option_session){ //期权资金账号且$sso_option_session
				return array('isLogin'=>true,'code'=>0,'result'=>$sso_option_session);
			}else{
				LogFunc::_log("debug",'行'.__LINE__.'---checkLoginInfov2-instant_token为空时-882-'.json_encode($sso_session,true),'page');
				return array('isLogin'=>false,'code'=>-882,'result'=>'instant_token为空时，session数据不存在或被清空了');
			}
		}
	}

	//判断参数是否为null，false或空
	static public function isNullOrEmpty($param)
	{
		if($param === '' || $param === null || $param === false){
			return true;
		}else{
			return false;
		}
	}

	/**
	 * add by yancy
	 * 将web-url处理后的base64格式转换为-base64
	 */
	static public function webToBase64($url){
		$regexp=[['-','+'],['_','/'],['.','=']];
		for($i=0;$i<count($regexp);$i++){
			$url=str_replace($regexp[$i][0],$regexp[$i][1],$url);
		}
		return $url;
	}
}
