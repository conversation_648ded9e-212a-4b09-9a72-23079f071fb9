<?php
namespace common\library;

use Yii;
use common\library\LogFunc;
use yii\helpers\VarDumper;

/**
 * ContactForm is the model behind the contact form.
 */

//备注: 这里的函数需要修改, controller的方式则不存在act参数等. add by yancy
class ApiFunc 
{
	static public function api_curl($params,$logInfo)
	{
		$act = $params['act'];
		unset($params['act']);

		if($act=='qq.kesb_req'){
			return self::post_curl_value( Yii::$app->params['qqStockAPi'], json_encode($params['params']),$logInfo);
		}

		if(array_key_exists('commonFunc',$params)){
			$apis = Yii::$app->params['comm_apis'];
			$postApis = Yii::$app->params['comm_post_apis'];
			unset($params['commonFunc']);
			//var_dump(Yii::$app->params['comm_apis']);exit;
		}else{
			//var_dump($params);exit;
			$apis = Yii::$app->params['apis'];
			$postApis = Yii::$app->params['post_apis'];
			//LogFunc::_log("debug",'请求的act是--'.json_encode($act),'page');
		}
		
		
		foreach($apis as $k=>$v)
		{
			if($k == $act)
			{
				$apiname = $v['apiname'] ? $v['apiname'] : $k;
				$url = 'http://'.$v['server'].':'.$v['port'].$v['path'].$apiname;
				if(is_array($apis[$act]['template'])){	// 特殊UPS方式
					$par = $apis[$act]['template'];
					$par['params'] = $params;
					return self::post_curl_value($url, json_encode($par),$logInfo);
				} else if(in_array($k, $postApis)) {
					$par['params'] = $params;
					$params_str = '';
					foreach($params as $pk => $pv){
						$params_str .= '&'.$pk.'='.$pv;
					}
					$data_str = substr($params_str, 1);
					return self::post_curl_value2($url, $data_str,$logInfo);
				} else {  // 普通get方式
					$params_str = '';
					foreach($params as $pk => $pv){
						$params_str .= '&'.$pk.'='.$pv;
					}
					$url.= '?'.substr($params_str, 1);
					return self::get_curl_value($url,$logInfo,$act);
				}
			}
		}
	}
	
	static public function get_curl_value($url,$logInfo='',$act)
	{
		try{
			LogFunc::_log("debug", "In get_curl_value,查询项:".$logInfo.'--请求地址:'.LogFunc::remove_password($url), $log='page');
			LogFunc::statlog('request', "In get_curl_value:".' -- '.LogFunc::remove_password($url),200, $log='apis');
			$ch = curl_init();
			if($_[0] == 'https:')
			{
				curl_setopt ($ch, CURLOPT_SSL_VERIFYHOST, 0);
				curl_setopt ($ch, CURLOPT_SSL_VERIFYPEER, 0);
			}
			curl_setopt($ch, CURLOPT_URL, $url);
			curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
			curl_setopt($ch, CURLOPT_HEADER, 0);
			curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 28);
			$res=curl_exec($ch);
			/* if($act == 'ipo_index'){
				VarDumper::dump($res);exit;
			} */
			$errno = curl_errno($ch);
			$curl_error = curl_error($ch); 
			curl_close($ch);
		}catch(Exception $e){
		    $errno = "";
			$res = array();
			LogFunc::_log("error", "Out get_curl_value,查询结果:".$logInfo.':'.LogFunc::remove_password($url).' -- '.$e->getMessage(), $log='error');
			LogFunc::_log("error", "Out get_curl_value,查询结果:".$logInfo.':'.LogFunc::remove_password($url).' -- '.$e->getMessage(), $log='page');
			LogFunc::statlog("error", "Out get_curl_value:--".$logInfo.':'.LogFunc::remove_password($url).' -- '.$e->getMessage(), -999,$log='apis');
		}
		
	
		if($errno != 0)
		{
			$res = json_encode(array('code'=>-999, 'message'=>'timeout'));
			LogFunc::_log("error", "Out get_curl_value,查询结果:".$logInfo." error_no: ".LogFunc::remove_password($url).' -- '.$errno." error: ".$curl_error, $log='error');
			LogFunc::_log("error", "Out get_curl_value,查询结果:".$logInfo." error_no: ".LogFunc::remove_password($url).' -- '.$errno." error: ".$curl_error, $log='page');
			LogFunc::statlog("error", "Out get_curl_value:--".$logInfo.':'.LogFunc::remove_password($url), $errno,$log='apis');
		}
		else
		{
			LogFunc::_log("debug", "Out get_curl_value, 查询结果:".$logInfo.' -- 结果：'.LogFunc::remove_password($res), $log='page');
			LogFunc::statlog("success", "Out get_curl_value:--".$logInfo.':'.LogFunc::remove_password($res), 200,$log='apis');
		}
		return $res;
	}
	
	
	static public function post_curl_value($url, $data_string,$logInfo='')
	{
 		try
		{
			LogFunc::_log('debug', "In post_curl_value: 查询项:".$logInfo.'--请求地址:'.$url.' -- '.LogFunc::remove_password($data_string), $log='page');
			LogFunc::statlog('request', "In post_curl_value--url:".$url.' --params: '.LogFunc::remove_password($data_string),200, $log='apis');
			$ch = curl_init();
			curl_setopt($ch, CURLOPT_POST, 1);
			curl_setopt($ch, CURLOPT_TIMEOUT, 25);
			curl_setopt($ch, CURLOPT_URL, $url);
			curl_setopt($ch, CURLOPT_POSTFIELDS, $data_string);
			curl_setopt($ch, CURLOPT_HTTPHEADER, array(
					'Content-Type: application/json; charset=utf-8',
					'Content-Length: ' . strlen($data_string))
			);
			ob_start();
			curl_exec($ch);
			$return_content = ob_get_contents();
			ob_end_clean();
			$return_code = curl_errno($ch);
		} catch(Exception $e) {
			$return_code = "";
			$return_content = "";
			LogFunc::_log("error", "Out post_curl_value: 查询结果:".$logInfo.'--'.LogFunc::remove_password($url).' -- '.$e->getMessage(), $log='error');
			LogFunc::_log("error", "Out post_curl_value: 查询结果:".$logInfo.'--'.LogFunc::remove_password($url).' -- '.$e->getMessage(), $log='page');
			LogFunc::statlog("error", "Out post_curl_value:--".$logInfo.':'.$e->getMessage(), -999,$log='apis');
		}
	
		if($return_code > 0)
		{
			LogFunc::_log("error", "Out post_curl_value: 查询结果:".$logInfo."-- error_no: ".$return_code." error: ".LogFunc::remove_password($return_content), $log='error');
			LogFunc::_log("error", "Out post_curl_value: 查询结果:".$logInfo."-- error_no: ".$return_code." error: ".LogFunc::remove_password($return_content), $log='page');
			LogFunc::statlog("error", "Out post_curl_value:--".$logInfo.':'.LogFunc::remove_password($return_content), $return_code,$log='apis');
			return json_encode(array('code'=>-999, "message" => "通讯超时"));
		} else {
			LogFunc::_log("debug", "Out post_curl_value: 查询结果:".$logInfo."--结果：".LogFunc::remove_password($return_content), $log='page');
			LogFunc::statlog("success", "Out post_curl_value:--".$logInfo.':--'.LogFunc::remove_password($return_content), 200,$log='apis');
			return $return_content;
		}
	}
	
	static public function post_curl_value2($url, $data_string,$logInfo='') 
	{
		try
		{
			$data_string_for_log = strpos($data_string, "?") === 0 ? $data_string : "?".$data_string;
			
			if(strpos($url,'/uploadbase64') !== false){
				//LogFunc::_log('debug', "In post_curl_value2: 查询项:".$logInfo.'--请求地址:'.$url.' -- '.LogFunc::remove_password($data_string_for_log), $log='uploadbase64');
				LogFunc::statlog('request', "In post_curl_value2--url:".$url.' --param: '.LogFunc::remove_password($data_string_for_log),200, $log='apis');
			}else{
				LogFunc::_log('debug', "In post_curl_value2: 查询项:".$logInfo.'--请求地址:'.$url.' -- '.LogFunc::remove_password($data_string_for_log), $log='page');
				LogFunc::statlog('request', "In post_curl_value2--url:".$url.' --param: '.LogFunc::remove_password($data_string_for_log),200, $log='apis');
			}
			$ch = curl_init();
			curl_setopt($ch, CURLOPT_POST, 1);
			curl_setopt($ch, CURLOPT_TIMEOUT, 25);
			curl_setopt($ch, CURLOPT_URL, $url);
			curl_setopt($ch, CURLOPT_POSTFIELDS, $data_string);
			curl_setopt($ch, CURLOPT_POST, 1 );
			curl_setopt($ch, CURLOPT_HTTPHEADER, array(
					'Accept-Language: zh-cn',
					'Connection: Keep-Alive',
					'Cache-Control: no-cache'
			));
			ob_start();
			curl_exec($ch);
			$return_content = ob_get_contents();
			ob_end_clean();
			$return_code = curl_errno($ch);
		} catch(Exception $e) {
			$curl_errno = "";
			$res = array();
			LogFunc::_log("error", "Out post_curl_value2: 查询结果:".$logInfo."--".LogFunc::remove_password($url).' -- '.$e->getMessage(), $log='error');
			LogFunc::_log("error", "Out post_curl_value2: 查询结果:".$logInfo."--".LogFunc::remove_password($url).' -- '.$e->getMessage(), $log='page');
			LogFunc::statlog("error", "Out post_curl_value2:--".$logInfo."--".LogFunc::remove_password($url).' -- '.$e->getMessage(),-999, $log='apis');
		}
	
		if($return_code > 0){
			LogFunc::_log("error", "Out post_curl_value2: 查询结果:".$logInfo."-- error_no: ".$return_code." error: ".LogFunc::remove_password($return_content), $log='error');
			LogFunc::_log("error", "Out post_curl_value2: 查询结果:".$logInfo."-- error_no: ".$return_code." error: ".LogFunc::remove_password($return_content), $log='page');
			LogFunc::statlog("error", "Out post_curl_value2:--".$logInfo."--".LogFunc::remove_password($return_content),$return_code, $log='apis');
			return json_encode(array('code'=>-999, "message" => "通讯超时"));
		} else {
			LogFunc::_log("debug", "Out post_curl_value2: 查询结果:".$logInfo."--结果：".LogFunc::remove_password($return_content), $log='page');
			LogFunc::statlog("success", "Out post_curl_value2:--".$logInfo."--".LogFunc::remove_password($return_content),200, $log='apis');
			return $return_content;
		}
	}

	//解析请求参数
	static public function parseRequestParam(){
		if(Yii::$app->request->isPost){
            $params=Yii::$app->request->post();
        }else{
            $params=Yii::$app->request->get();
		}
		
		if(in_array($params['UniqueKey'], Yii::$app->params['encrypt_apis'])){
			$private_key=
'**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************';
			$pi_key =  openssl_pkey_get_private($private_key);
			foreach($params as $key => $value){
				if($key =='UniqueKey'){
					//不需要解密的字段
					//unset($params['UniqueKey']);
				}else{
					openssl_private_decrypt(base64_decode($value),$params[$key],$pi_key);
				}
			}	
		}
		


		return  $params;
	}
}
