<?php
namespace common\library;

use Yii;
use common\library\LogFunc;

class SessionFunc 
{
	/**
	 *  Session初始化  add by yancy
 	 */
	static function startSession($expire = 0){
		if ($expire == 0) {
			$expire = ini_get('session.gc_maxlifetime');
		} else {
			ini_set('session.gc_maxlifetime', $expire);
		}
		//ini_set('session.cookie_lifetime', Yii::$app->params['timeout']);
		//ini_set('session.cookie_path', Yii::$app->params['session_path']);
		ini_set('session.name', Yii::$app->params['session_name']);
	
		$url = $_SERVER['SERVER_NAME'].$_SERVER['REQUEST_URI'];
		if(Yii::$app->params['is_sessioncookielog']){
			LogFunc::_log("debug",'行'.__LINE__.'请求头COOKIE----@'.json_encode($_COOKIE,true).'@--请求url--'.$url,'page');
		}
	
		if (empty($_COOKIE[session_name()])) {
			$sessioncookieparams = session_get_cookie_params();
			$sessioncookieparams['lifetime'] = $expire;
			$sessioncookieparams['secure'] = true;
			LogFunc::_log("debug",'行'.__LINE__.'$sessioncookieparams'.json_encode($sessioncookieparams),'session');
				
			session_set_cookie_params($sessioncookieparams);
			session_start();
			LogFunc::_log("debug",'行'.__LINE__.'【【【没有session_id，第一次生成'.session_id().'---'.$expire,'session');
			LogFunc::_log("debug",'行'.__LINE__.'【【【第一次HTTP_COOKIE----@'.$_SERVER['HTTP_COOKIE'],'session');
		} else {
			$sessioncookieparams = session_get_cookie_params();
			//session_set_cookie_params($sessioncookieparams);//勿删：这里设置的session_cookie会在HTTP_COOKIE出现重复
			session_start();
				
			$path = $sessioncookieparams['path'];
			$domain = $sessioncookieparams['domain'];
			$sessioncookieparams['secure'] = true;
			$httponly = $sessioncookieparams['httponly'];
			LogFunc::_log("debug",'行'.__LINE__.'$sessioncookieparams'.json_encode($sessioncookieparams),'session');
			setcookie(session_name(), session_id(), time() + $expire,$path,$domain,$secure,$httponly);
				
			//setcookie(session_name(), session_id(), time() + $expire);  //勿删：这里设置的session_cookie会在HTTP_COOKIE出现重复
			LogFunc::_log("debug",'行'.__LINE__.'【【【已经生成的session_id是'.session_id(),'session');
			LogFunc::_log("debug",'行'.__LINE__.'【【【已经生成HTTP_COOKIE----@'.$_SERVER['HTTP_COOKIE'],'session');
		}
	}
	
	//清空session
	static public function  clearSession()
	{
		$sessioncookieparams = session_get_cookie_params();
		$path = $sessioncookieparams['path'];
		$domain = $sessioncookieparams['domain'];
		$secure = $sessioncookieparams['secure'];
		$httponly = $sessioncookieparams['httponly'];
		setcookie(session_name(),'',time()-3600,$path,$domain,$secure,$httponly);  //将session_cookie的时间设置为空
		
		$_SESSION = array();
		session_unset();
		session_destroy();
	}
	
	
	/**
	 *  写Session  update by yancy
	 *  2016-11-07
	 */
    static public function writeSession($data)
    {
    	if(is_array($data) && count($data)>0) {
    		foreach ($data as $k=>$v) {
    			$k = Yii::$app->params['cookie_prefix'].$k;	// add prefix to key
    			$_SESSION[$k] = $v;
				//严格控制超时时间
				$_SESSION[$k.'_expiretime'] = time() + Yii::$app->params['timeout'];
    		}
    	}
    }
    
    /**
     *  读取Session update by yancy
	 *   2016-11-07
	 */
    static public function readSession($key)
    {
    	$key = Yii::$app->params['cookie_prefix'].$key;
    	if($_SESSION[$key] != '') {
			if(isset($_SESSION[$key.'_expiretime'])) {
				if($_SESSION[$key.'_expiretime'] < time()) {
					 /* 当判断当前session过期后,是清除当前session,还是清除全部?
					 * 个人认为公共方法尽量以原子粒度处理.*/
					//$_SESSION=array();
					//session_destroy();
					unset($_SESSION[$key]);
					unset($_SESSION[$key.'_expiretime']);
				} else {
					$_SESSION[$key.'_expiretime'] = time() + Yii::$app->params['timeout'];// 刷新时间戳
					if(is_array($_SESSION[$key])){
						return $_SESSION[$key];
					}else{
						return (string)$_SESSION[$key];
					}
				}
			}else{
				//$_SESSION=array();
				//session_destroy();
				unset($_SESSION[$key]);
			}
    	}
    	return '';
    }
    
    // 写 cookie
    static public function writeCookie($data){
    	if(is_array($data) && count($data)>0) {
    		foreach ($data as $k=>$v) {
    			$k = Yii::$app->params['cookie_prefix'].$k;	// add prefix to key
    			if(is_array($v))
    			{
    				$v = json_encode($v);
    			}
    			$_COOKIE[$k] = $v; // no need to refresh page to make cookie work
    			setcookie($k, $v);
    		}
    	}
    }
    
    //读cookie
    static public function readCookie($key){
    	$key = Yii::$app->params['cookie_prefix'].$key;
    	if($_COOKIE[$key] != '') {
    		if(json_decode($_COOKIE[$key], true) != NULL) {
    			return json_decode($_COOKIE[$key], true);
    		}
    		else {
    			return $_COOKIE[$key];
    		}
    	}
    	return '';
    }
}


/* 测试用暂时勿删 --2016-12-02 yyg
 		session_start(); 	
		session_unset();
		$_SESSION = array();
		setcookie('PHPSESSID','',time()-3600,'/');
		session_destroy();
		LogFunc::_log("debug",'行'.__LINE__.'我们的未来expire----@'.$expire,'page');
		LogFunc::_log("debug",'行'.__LINE__.'我们的家乡PHPSESSID----@'.$_COOKIE['PHPSESSID'].'@--url--'.$url,'page');
		LogFunc::_log("debug",'行'.__LINE__.'我们的乐园SESSION----@'.json_encode($_SESSION,true).'@--url--'.$url,'page');
		exit; 
 
 **************************************** 
if(empty($PHPSESSID)){
	//$_COOKIE['PHPSESSID'] = '';  //清空所有的$_COOKIE['PHPSESSID']
	session_set_cookie_params($expire,'/gjzqwxtest3g/xgsg');
	session_start();
	LogFunc::_log("debug",'行'.__LINE__.'【【【没有session_id，第一次生成'.session_id(),'page');
	LogFunc::_log("debug",'行'.__LINE__.'【【【没有session_id，第一次$_COOKIE[PHPSESSID]是'.$_COOKIE['PHPSESSID'],'page');
} else {
	session_start();
	//设置PHPSESSID
	isset($PHPSESSID) ? session_id($PHPSESSID) : $PHPSESSID = session_id();
	setcookie('PHPSESSID', $PHPSESSID, time() + $expire,'/gjzqwxtest3g/xgsg');
		
	LogFunc::_log("debug",'行'.__LINE__.'【【【已经生成的session_id是'.session_id(),'page');
	LogFunc::_log("debug",'行'.__LINE__.'【【【已经生成的$_COOKIE[PHPSESSID]是'.$_COOKIE['PHPSESSID'],'page');
} 
 **************************************** 
	static function startSession($expire = 0){
		if ($expire == 0) {
			$expire = ini_get('session.gc_maxlifetime');
		} else {
			ini_set('session.gc_maxlifetime', $expire);
		}
		//ini_set('session.cookie_lifetime', Yii::$app->params['timeout']);
		//ini_set('session.cookie_path', Yii::$app->params['session_path']);
		ini_set('session.name', Yii::$app->params['session_name']);
		
		$url = $_SERVER['SERVER_NAME'].$_SERVER['REQUEST_URI'];
		LogFunc::_log("debug",'行'.__LINE__.'大海啊大海COOKIE----@'.json_encode($_COOKIE,true).'@--请求url--'.$url,'page');
		
		if (empty($_COOKIE[session_name()])) {
			$sessioncookieparams = session_get_cookie_params();
			$sessioncookieparams['lifetime'] = $expire;
			$sessioncookieparams['secure'] = true;
			LogFunc::_log("debug",'行'.__LINE__.'$sessioncookieparams'.json_encode($sessioncookieparams),'session');
			
			session_set_cookie_params($sessioncookieparams);
			session_start();
			LogFunc::_log("debug",'行'.__LINE__.'【【【没有session_id，第一次生成'.session_id().'---'.$expire,'session');
			LogFunc::_log("debug",'行'.__LINE__.'【【【第一次HTTP_COOKIE----@'.$_SERVER['HTTP_COOKIE'],'session');
		} else {
			$sessioncookieparams = session_get_cookie_params();
			//session_set_cookie_params($sessioncookieparams);//勿删：这里设置的session_cookie会在HTTP_COOKIE出现重复
			session_start();
			
			$path = $sessioncookieparams['path'];
			$domain = $sessioncookieparams['domain'];
			$sessioncookieparams['secure'] = true;
			$httponly = $sessioncookieparams['httponly'];
			LogFunc::_log("debug",'行'.__LINE__.'$sessioncookieparams'.json_encode($sessioncookieparams),'session');
			setcookie(session_name(), session_id(), time() + $expire,$path,$domain,$secure,$httponly);
			
			//setcookie(session_name(), session_id(), time() + $expire);  //勿删：这里设置的session_cookie会在HTTP_COOKIE出现重复
			LogFunc::_log("debug",'行'.__LINE__.'【【【已经生成的session_id是'.session_id(),'session');
			LogFunc::_log("debug",'行'.__LINE__.'【【【已经生成HTTP_COOKIE----@'.$_SERVER['HTTP_COOKIE'],'session');
		}
	}
	***************************************
	static function startSession($expire = 0){
		if ($expire == 0) {
			$expire = ini_get('session.gc_maxlifetime');
		} else {
			ini_set('session.gc_maxlifetime', $expire);
		}
		//ini_set('session.cookie_lifetime', Yii::$app->params['timeout']);
		//ini_set('session.cookie_path', Yii::$app->params['session_path']);
		ini_set('session.name', Yii::$app->params['session_name']);
		
		$url = $_SERVER['SERVER_NAME'].$_SERVER['REQUEST_URI'];
		LogFunc::_log("debug",'行'.__LINE__.'大海啊大海COOKIE----@'.json_encode($_COOKIE,true).'@--请求url--'.$url,'page');
		
		if (empty($_COOKIE[session_name()])) {
			session_set_cookie_params($expire);
			session_start();
			LogFunc::_log("debug",'行'.__LINE__.'【【【没有session_id，第一次生成'.session_id().'---'.$expire,'session');
			LogFunc::_log("debug",'行'.__LINE__.'【【【第一次HTTP_COOKIE----@'.$_SERVER['HTTP_COOKIE'],'session');
		} else {
			$sessioncookieparams = session_get_cookie_params();
			//session_set_cookie_params($sessioncookieparams);//勿删：这里设置的session_cookie会在HTTP_COOKIE出现重复
			session_start();
			setcookie(session_name(), session_id(), time() + $expire);  //勿删：这里设置的session_cookie会在HTTP_COOKIE出现重复
			LogFunc::_log("debug",'行'.__LINE__.'【【【已经生成的session_id是'.session_id(),'session');
			LogFunc::_log("debug",'行'.__LINE__.'【【【已经生成HTTP_COOKIE----@'.$_SERVER['HTTP_COOKIE'],'session');
		}
	}
*/