flowchart TD
    A["GoGroupView 页面加载"] --> B["componentWillMount"]
    B --> C{"同花顺渠道 is4ths"}
    C -- 是 --> D{"token有效"}
    D -- 无效 --> E["跳转同花顺登录<br>yjbweblogin/login/web/build/index.html"]
    D -- 有效 --> F["继续处理"]
    C -- 否 --> F
    F --> G{"特殊渠道*************"}
    G -- 是 --> H["设置渠道Cookie和SessionStorage"]
    G -- 否 --> I["跳过设置"]
    H --> J{"instant_token最终验证"}
    I --> J
    J -- 无效且特殊渠道 --> K["跳转登录页面"]
    J -- 有效 --> L["componentDidMount"]
    L --> M{"同花顺业务"}
    M -- 是 --> N["thscode映射获取groupName<br>settings.thscodeMap"]
    M -- 否 --> O["直接使用groupName"]
    N --> P{"登录需求判断"}
    O --> P
    P -- 免登录业务<br>findAccount/selectWay等 --> Q["直接执行Events.click"]
    P -- 凌志推送personalForCsdcExit<br>且无instant_token --> R["打开bridge登录页面"]
    P -- 需要登录验证 --> S["调用_appInit"]
    S --> T{"QQ渠道 is4QQ"}
    T -- 是 --> U{"session_id存在?"}
    U -- 存在 --> V["fetchToken4qq验证"]
    U -- 不存在 --> W["登录失效提示"]
    V -- 成功 --> X["调用appInit"]
    V -- 失败 --> W
    T -- 否 --> X
    X --> Y{"QQ渠道token处理"}
    Y -- 是QQ渠道 --> Z["从sessionStorage获取token"]
    Y -- 否 --> AA["直接使用URL参数token"]
    Z --> BB{"token状态检查"}
    AA --> BB
    BB -- 无效或空 --> CC["设置为notoken"]
    BB -- 与旧token相同 --> DD["清空token"]
    BB -- 新有效token --> EE["保存到Cookie"]
    CC --> FF{"鸿蒙系统?"}
    DD --> FF
    EE --> FF
    FF -- 是 --> GG["强制app_id为yjbweb"]
    FF -- 否 --> HH["保持原app_id"]
    GG --> II{"终端类型判断"}
    HH --> II
    II -- yjbjyd --> JJ["佣金宝2.0<br>设置全局函数GoBackOnLoad，用于返回"]
    II -- yjbwx --> KK["微信端<br>隐藏菜单栏"]
    II -- "yjb3.0" --> LL["3.0客户端"]
    II -- yjbweb --> MM["Web端"]
    II -- 其他 --> NN["其他终端"]
    JJ --> OO{"isWeekLogin?"}
    KK --> OO
    LL --> OO
    MM --> OO
    NN --> OO
    OO -- 是 --> PP["直接回调"]
    OO -- 否 --> QQ["发起checklogin请求"]
    QQ --> RR{"请求结果"}
    RR -- 成功 --> SS["返回登录信息"]
    RR -- 失败 --> TT["返回false"]
    PP --> UU{"配置检查"}
    SS --> UU
    TT --> UU
    UU -- 不存在且有ForWeb后缀 --> VV["动态创建配置<br>关闭isOpenNewPage"]
    UU -- 存在 --> WW["使用现有配置"]
    VV --> XX{"鸿蒙系统业务检查"}
    WW --> XX
    XX -- 鸿蒙且被禁用业务 --> YY["显示不支持提示<br>3秒后关闭"]
    XX -- 正常系统或允许业务 --> ZZ["执行Events.click"]
    Q --> AAA["Events处理完成"]
    R --> AAA
    ZZ --> AAA
    AAA --> BBB["流程完成"]
    E --> CCC["异常结束"]
    K --> CCC
    W --> CCC
    YY --> CCC

    style A fill:#e1f5fe
    style P fill:#fff3e0
    style Y fill:#fff3e0
    style BB fill:#fff3e0
    style FF fill:#fff3e0
    style II fill:#fff3e0
    style OO fill:#fff3e0
    style RR fill:#fff3e0
    style UU fill:#fff3e0
    style XX fill:#fff3e0
    style AAA fill:#e8f5e8
    style BBB fill:#e8f5e8
    style CCC fill:#ffebee


