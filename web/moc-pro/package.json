{"name": "moc", "version": "1.0.3", "description": "yo<PERSON><PERSON>bao yjb login", "main": "index.js", "scripts": {"test": "jasmine", "fis3": "fis3 release  -d ../build/moc/web", "watch-common-dev": "webpack --config ./webpack.common.config.js --watch", "build-common-dev": "set NODE_ENV=product && webpack --config ./webpack.common.config.js", "build-common-app": "set NODE_ENV=product && webpack  -p --config ./webpack.common.config.js", "watch-dev": "set NODE_ENV=product && webpack --config ./webpack.common.config.js&&webpack -w --config ./webpack.dev.config.js", "watch-local": "set NODE_ENV=product && webpack --config ./webpack.common.config.js&&webpack -w --config ./webpack.dev.config.bak.js", "local": "node local && npm run watch-local", "build-dev": "webpack --config ./webpack.dev.config.js", "build-app": "set NODE_ENV=product && webpack -p --config ./webpack.dev.config.js", "build-mytest": "set NODE_ENV=product && webpack --config ./webpack.common.config.js && webpack --config ./webpack.dev.config.js --watch", "build-myprod": "set NODE_ENV=product && webpack  -p --config ./webpack.common.config.js && set NODE_ENV=product && webpack -p --config ./webpack.dev.config.js"}, "repository": {"type": "git", "url": "none"}, "keywords": ["moc"], "browserslist": ["> 1%", "last 2 versions"], "author": "sinolink", "license": "ISC", "devDependencies": {"autoprefixer": "^9.6.5", "babel-plugin-import": "^1.13.3", "babel-plugin-transform-class-properties": "^6.24.1", "enzyme": "^2.5.1", "inquirer": "^8.2.0", "jasmine-enzyme": "^2.0.0", "jasmine-es6": "^0.1.10", "jasmine-reporters": "^2.2.0", "jsdom": "^9.8.0", "node-sass": "4.12.0", "postcss": "^7.0.18", "postcss-loader": "^4.3.0", "react-addons-test-utils": "^15.3.2"}, "dependencies": {"@babel/helper-module-imports": "^7.16.0", "@sinolink/collect": "^0.2.8", "antd-mobile": "^2.3.4", "axios": "^0.21.1", "babel-core": "^6.26.0", "babel-loader": "^7.1.4", "babel-plugin-transform-runtime": "^6.15.0", "babel-preset-env": "^1.7.0", "babel-preset-es2015": "^6.16.0", "babel-preset-react": "^6.16.0", "babel-preset-stage-0": "^6.16.0", "better-scroll": "^2.4.2", "css-loader": "0.25.0", "html-webpack-plugin": "^4.5.0", "jsencrypt": "^3.2.0", "keypad-easy": "^1.0.5", "lodash.debounce": "^4.0.8", "lodash.throttle": "^4.1.1", "moment": "^2.29.4", "normalizr": "^3.2.1", "react": "^15.3.2", "react-dom": "^15.3.2", "react-pdf": "^1.6.1", "sass-loader": "^7.1.0", "style-loader": "^2.0.0", "url-loader": "0.5.7", "vconsole": "^3.0.0", "webpack": "^4.0.0", "webpack-cli": "^3.3.5"}}