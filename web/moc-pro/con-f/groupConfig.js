/**
 * Created by JinYC on 2017/9/12.
 */
//TODO 组定义 Group

/**
 * 通过任务管理模块统一控制处理Group流程配置
 * module 规定接口 :  init(初始化模块)  next(结束模块)
 * eq: init(type,subtype) 进入公告模块, 内部判断如果展示公告,则不执行 next 模块
 * 若到定义二维表尾部执行end 则结束 Group (关闭当前webview)
 */
var groupConfig = {
    // 风险测评
    risk_sd: {
        id: '',
        name: 'risk',
        modules: [
            {
                name: 'noticeModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    UniqueKey: 'ANNC_1004',
                    type: '1002',
                    subtype: '10020018'
                }
            },
            {
                name: 'checkSdVersionModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    version: '8.01.001' // 该业务支持的客户端版本
                }
            },
            {
                name: 'goSdModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    bizType: '010013',
                }
            }
        ]
    },
    // //风险测评结果
    riskResult_sd: {
        id: '',
        name: 'risk',
        modules: [
            {
                name: 'noticeModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    UniqueKey: 'ANNC_1004',
                    type: '1002',
                    subtype: '10020018'
                }
            },
            {
                name: 'checkSdVersionModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    version: '8.01.001' // 该业务支持的客户端版本
                }
            },
            {
                name: 'goSdModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    bizType: '010013',
                }
            }
        ]
    },
    // 专业投资者
    professionalInvestor_sd: {
        id: '',
        name: 'professionalInvestor',
        modules: [
            {
                name: 'noticeModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    UniqueKey: 'ANNC_1004',
                    type: '1002',
                    subtype: '10020044'
                }
            },
            {
                name: 'checkSdVersionModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    version: '8.01.001', // 该业务支持的客户端版本
                }
            },
            {
                name: 'goSdModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    bizType:'010042',
                }
            }
        ]
    },
    // 基础设施基金
    crhInfrastructureFund_sd: {
        id: '',
        name: 'crhInfrastructureFund',
        modules: [
            {
                name: 'noticeModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    UniqueKey: 'ANNC_1004',
                    type: '1002',
                    subtype: '********'
                }
            },
            {
                name: 'checkSdVersionModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    version: '8.01.001' // 该业务支持的客户端版本
                }
            },
            {
                name: 'goSdModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    bizType:'010173',
                    specialLink: 'businessIntroduce' // 特殊路由
                    
                }
            }
        ]
    },
    // 可转债
    holderRights_sd: {
        id: '',
        name: 'holderRights',
        modules: [
            {
                name: 'noticeModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    UniqueKey: 'ANNC_1004',
                    type: '1002',
                    subtype: '********'
                }
            },
            {
                name: 'checkSdVersionModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    version: '8.01.001' // 该业务支持的客户端版本
                }
            },
            {
                name: 'goSdModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    bizType:'010172',
                    specialLink: 'businessIntroduce' // 特殊路由
                }
            }
        ]
    },

    // 开放式基金账户
    openfund_sd: {
        "id": "",
        "name": "openfund",
        "modules": [
            {
                name: 'noticeModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    UniqueKey: 'ANNC_1004',
                    type: '1002',
                    subtype: '********'
                },
                result: {}
            },
            {
                name: 'checkSdVersionModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    version: '8.01.001' // 该业务支持的客户端版本
                }
            },
            {
                name: 'goSdModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    bizType:'010089',
                }
            }
        ]
    },
    // 期权买入额度调整
    crhStockOptions_sd: {
        id: '',
        name: 'crhStockOptions',
        modules: [
            {
                name: 'noticeModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    UniqueKey: 'ANNC_1004',
                    type: '1002',
                    subtype: '10020053'
                }
            },
            {
                name: 'checkSdVersionModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    version: '8.01.001' // 该业务支持的客户端版本
                }
            },
            {
                name: 'goSdModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    bizType:'010131',
                    specialLink: 'businessIntroduce' // 特殊路由
                }
            }
        ]
    },
    // 场内外账户对应关系维护
    crhTaRelationInfo_sd: {
        id: '',
        name: 'crhTaRelationInfo',
        modules: [
            {
                name: 'noticeModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    UniqueKey: 'ANNC_1004',
                    type: '1002',
                    subtype: '********'//todo
                }
            },
            {
                name: 'checkSdVersionModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    version: '8.01.001' // 该业务支持的客户端版本
                }
            },
            {
                name: 'goSdModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    bizType:'010177',
                    specialLink: 'businessIntroduce' // 特殊路由
                }
            }
        ]
    },
    // 电子对账单
    crhCheckAccount_sd: {
        id: '',
        name: 'crhCheckAccount',
        modules: [
            {
                name: 'noticeModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    UniqueKey: 'ANNC_1004',
                    type: '1002',
                    subtype: '********'//todo
                }
            },
            {
                name: 'checkSdVersionModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    version: '8.01.001' // 该业务支持的客户端版本
                }
            },
            {
                name: 'goSdModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    // bizType:'010246',
                    bizType:'010246',
                    specialLink: 'proofApply' // 特殊路由，之前specialLink为空
                }
            }
        ]
    },
    //身份更新
    idupdate_sd: {
        id: '',
        name: 'idupdate',
        modules: [
            {
                name: 'noticeModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    UniqueKey: 'ANNC_1004',
                    type: '1002',
                    subtype: '********'
                }
            },
            {
                name: 'checkSdVersionModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    version: '8.01.004' // 该业务支持的客户端版本
                }
            },
            {
                name: 'goSdModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    bizType:'010006',
                }
            }
        ]
    },
    // 开立港股通账户
    ggtOpenAccount_sd: {
        id: '',
        name: 'ggtOpenAccount',
        modules: [
            {
                name: 'noticeModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    UniqueKey: 'ANNC_1004',
                    type: '1002',
                    subtype: '********'
                }
            },
            {
                name: 'checkSdVersionModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    version: '8.01.004' // 该业务支持的客户端版本
                }
            },
            {
                name: 'goSdModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    // bizType:'014002',
                    // specialLink: 'businessIntroduce' // 特殊路由
                    bizType:'014002',
                    specialLink: 'businessIntroduce' // 特殊路由
                }
            }
        ]
    },
    // 开户确认单
    crhOpenConfirm_sd: {
        id: '',
        name: 'crhOpenConfirm',
        modules: [
            {
                name: 'noticeModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    UniqueKey: 'ANNC_1004',
                    type: '1002',
                    subtype: '10020056'
                }
            },
            {
                name: 'checkSdVersionModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    version: '8.01.001' // 该业务支持的客户端版本
                }
            },
            {
                name: 'goSdModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    bizType:'010003',
                }
            }
        ]
    },
    personal_sd: {
        id: '',
        name: 'personal',
        modules: [
            {
                name: 'noticeModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    UniqueKey: 'ANNC_1004',
                    type: '1002',
                    subtype: '10020001'
                },
                result: {}
            },
            {
                name: 'preHandlerForWebModule',//机构户校验
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    needPersonal: true,
                    needCredit: false,
                    pageTitle:'修改个人资料',
                },
                result: {}
            },
            {
                name: 'checkSdVersionModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    version: '8.01.001' // 该业务支持的客户端版本
                }
            },
            {
                name: 'goSdModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    specialLink: 'clientInfoModify' // 特殊路由
                }
            }
        ]
    },
    personalForCsdcExit_sd: {
        id: '',
        name: 'personal',
        modules: [
            {
                name: 'noticeModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    UniqueKey: 'ANNC_1004',
                    type: '1002',
                    subtype: '10020001'
                },
                result: {}
            },
            {
                name: 'preHandlerForWebModule',//机构户校验
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    needPersonal: true,
                    needCredit: false,
                    pageTitle:'修改个人资料',
                },
                result: {}
            },
            {
                name: 'checkSdVersionModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    version: '8.01.001' // 该业务支持的客户端版本
                }
            },
            {
                name: 'goSdModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    specialLink: 'clientInfoModify' // 特殊路由
                }
            }
        ]
    },
    personalItem_sd: {
        id: '',
        name: 'personal',
        modules: [
            {
                name: 'noticeModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    UniqueKey: 'ANNC_1004',
                    type: '1002',
                    subtype: '10020001'
                },
                result: {}
            },
            {
                name: 'preHandlerForWebModule',//机构户校验
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    needPersonal: true,
                    needCredit: false,
                    pageTitle:'修改个人资料',
                },
                result: {}
            },
            {
                name: 'checkSdVersionModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    version: '8.01.001' // 该业务支持的客户端版本
                }
            },
            {
                name: 'goSdModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    specialLink: 'clientInfoModify' // 特殊路由
                }
            }
        ]
    },
    // 特转A
    specialRights_sd: {
        id: '',
        name: 'specialRights',
        modules: [
            {
                name: 'noticeModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    UniqueKey: 'ANNC_1004',
                    type: '1002',
                    subtype: '10020062'
                }
            },
            {
                name: 'checkSdVersionModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    version: '8.01.001' // 该业务支持的客户端版本
                }
            },
            {
                name: 'goSdModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    bizType:'010213',
                    specialLink: 'businessIntroduce' // 特殊路由
                }
            }
        ]
    },
    // 资管/信托合格投资者认证
    investorList_sd: {
        id: '',
        name: 'investorList',
        modules: [
            {
                name: 'noticeModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    UniqueKey: 'ANNC_1004',
                    type: '1002',
                    subtype: '********'
                }
            },
            {
                name: 'checkSdVersionModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    version: '8.01.001', // 该业务支持的客户端版本
                }
            },
            {
                name: 'goSdModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    specialLink: 'investorList' // 特殊路由
                }
            }
        ]
    },
    // 重置密码-首页
    goClearPwd_sd: {
        id: '',
        name: 'goClearPwd',
        needLogin: '0',
        modules: [
            {
                name: 'noticeModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    UniqueKey: 'ANNC_1004',
                    type: '1002',
                    subtype: '10020021'
                }
            },
            {
                name: 'checkSdVersionModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    version: '8.01.004' // 该业务支持的客户端版本
                }
            },
            {
                name: 'goSdModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    specialLink: 'common/idverify' // 特殊路由
                }
            }
        ]
    },
    // 开通公司债/企业债权限
    debentureQuery_sd: {
        id: '',
        name: 'investorList',
        modules: [
            {
                name: 'noticeModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    UniqueKey: 'ANNC_1004',
                    type: '1002',
                    subtype: '********'
                }
            },
            {
                name: 'checkSdVersionModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    version: '8.01.001' // 该业务支持的客户端版本
                }
            },
            {
                name: 'goSdModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    bizType:'010203',
                    specialLink: 'businessIntroduce' // 特殊路由
                }
            }
        ]
    },
    // 开通信用公司债/企业债权限
    creditDebentureQuery_sd: {
        id: '',
        name: 'investorList',
        modules: [
            {
                name: 'noticeModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    UniqueKey: 'ANNC_1004',
                    type: '1002',
                    subtype: '********'
                }
            },
            {
                name: 'checkSdVersionModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    version: '8.01.001' // 该业务支持的客户端版本
                }
            },
            {
                name: 'goSdModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    bizType:'010203',
                    specialLink: 'businessIntroduce' // 特殊路由
                }
            }
        ]
    },
    //A股账户补开户--展示
    stockA_sd: {
        "id": "",
        "name": "stockA",
        "modules": [
            {
                "name": "noticeModule",
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                "param": {
                    UniqueKey: 'ANNC_1004',
                    type: '1002',
                    subtype: '********'
                },
                "result": {}
            },
            {
                name: 'checkSdVersionModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    version: '8.02.002' // 该业务支持的客户端版本
                }
            },
            {
                name: 'goSdModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    specialLink: 'addShareHoldAccount' // 特殊路由
                }
            }
        ]
    },
    //股票期权账户-查询
    optionAccount_sd: {
        id: '',
        name: 'optionAccount',
        modules: [
            {
                name: 'noticeModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    UniqueKey: 'ANNC_1004',
                    type: '1002',
                    subtype: '********'//股票期权账户
                }
            },
            {
                name: 'checkSdVersionModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    version: '8.02.002', // 该业务支持的客户端版本
                }
            },
            {
                name: 'goSdModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    bizType:'010129'
                }
            }
        ]
    },
    // 销户
    accountCancellation_sd: {
        id: '',
        name: 'accountCancellation',
        modules: [
            {
                name: 'noticeModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    UniqueKey: 'ANNC_1004',
                    type: '1002',
                    subtype: '********'// 销户
                }
            },
            {
                name: 'checkSdVersionModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    version: '8.02.002', // 该业务支持的客户端版本
                }
            },
            {
                name: 'goSdModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    bizType:'010731',/* 010731 销户预约 */
                    specialLink: 'businessIntroduce' // 特殊路由
                }
            }
        ]
    },
    // 销户
    accountCancellations_sd: {
        id: '',
        name: 'accountCancellations',
        modules: [
            {
                name: 'noticeModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    UniqueKey: 'ANNC_1004',
                    type: '1002',
                    subtype: '********'// 销户
                }
            },
            {
                name: 'checkSdVersionModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    version: '8.02.002', // 该业务支持的客户端版本
                }
            },
            {
                name: 'goSdModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    // bizType:'010731',
                    bizType:'014001',/* 010731 销户预约 */
                    specialLink: 'businessIntroduce' // 特殊路由
                }
            }
        ]
    },

    reserveListModule_sd: {
        id: '',
        name: 'reserveListModule',
        modules: [
            {
                name: 'noticeModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    UniqueKey: 'ANNC_1004',
                    type: '1002',
                    subtype: '10020031'
                }
            },
            {
                name: 'checkSdVersionModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    version: '8.02.002', // 该业务支持的客户端版本
                }
            },
            {
                name: 'goSdModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    bizType: comm.TOOLS.GetQueryString("businessType") ? '' : '010262',
                    specialLink: comm.TOOLS.GetQueryString("businessType") ? 'bcPreBizList/010277' : 'businessIntroduce' // 特殊路由
                }
            }
        ]
    },
    //产品购买双录
    doubleRecordList_sd: {
        id: '',
        name: 'doubleRecordList',
        modules: [
            {
                name: 'noticeModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    UniqueKey: 'ANNC_1004',
                    type: '1002',
                    subtype: '********'
                }
            },
            {
                name: 'checkSdVersionModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    version: '8.02.002', // 该业务支持的客户端版本
                }
            },
            {
                name: 'goSdModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    specialLink: 'doubleRecord' // 特殊路由
                }
            }
        ]
    },
    //指定双录流程
    doubleRecord_sd: {
        id: '',
        name: 'doubleRecord_sd',
        modules: [
            {
                name: 'noticeModule',
                setting: {
                    isOpenNewPage: false,
                    isCache: false
                },
                param: {
                    UniqueKey: 'ANNC_1004',
                    type: '1002',
                    subtype: '********'
                }
            },
            {
                name: 'preHandlerForWebModule',//机构户校验
                setting: {
                    isOpenNewPage: false,
                    isCache: false
                },
                param: {
                    needPersonal: true,
                    needCredit: false,
                    pageTitle:'产品购买双录',
                },
                result: {}
            },
            {
                name: 'checkSdVersionModule',
                setting: {
                    isOpenNewPage: false,
                    isCache: false
                },
                param: {
                    version: '8.02.002', // 该业务支持的客户端版本
                }
            },
            {
                name: 'goSdModule',
                setting: {
                    isOpenNewPage: false,
                    isCache: false
                },
                param: {
                    specialLink: 'doubleRecord/' + comm.TOOLS.GetQueryString("double_record_id")
                }
            }
        ]
    },
    //指定交易状态查询
    specified_sd: {
        "id": "",
        "name": "specified",
        "modules": [
            {
                "name": "noticeModule",
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                "param": {
                    "UniqueKey": "ANNC_1004",
                    "type": "1002",
                    "subtype": "********"
                }
            },
            {
                name: 'checkSdVersionModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    version: '8.01.001' // 该业务支持的客户端版本
                }
            },
            {
                name: 'goSdModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    bizType: '010053'
                }
            }
        ]
    },
    // 债券专业投资者认定
    // bondProfessionalInvestor: {
    //     "id": "",
    //     "name": "bondProfessionalInvestor",
    //     "modules": [ 
    //         {
    //             "name": "noticeModule",
    //             setting: {
    //                 isOpenNewPage: true,
    //                 isCache: false
    //             },
    //             "param": {
    //                 "UniqueKey": "ANNC_1004",
    //                 "type": "1002",
    //                 "subtype": "10020072"
    //             }
    //         },
    //         {
    //             name: 'checkSdVersionModule',
    //             setting: {
    //                 isOpenNewPage: true,
    //                 isCache: false
    //             },
    //             param: {
    //                 version: '8.01.001' // 该业务支持的客户端版本
    //             }
    //         },
    //         {
    //             name: 'goSdModule',
    //             setting: {
    //                 isOpenNewPage: true,
    //                 isCache: false
    //             },
    //             param: {
    //                 bizType: '010066',
    //                 specialLink: 'businessIntroduce'
    //             }
    //         }
    //     ]
    // },
    // 债券专业投资者认定
    bondProfessionalInvestor: {
        "id": "",
        "name": "bondProfessionalInvestor",
        "modules": [ 
            {
                "name": "noticeModule",
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                "param": {
                    "UniqueKey": "ANNC_1004",
                    "type": "1002",
                    "subtype": "10020072"
                }
            },
            {
                name: 'checkSdVersionModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    version: '8.01.001' // 该业务支持的客户端版本
                }
            },
            {
                name: 'goSdModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    bizType: '010066',
                    specialLink: 'introduce'
                }
            }
        ]
    },
    // 开通股票期权全真模拟交易账户
    qptionSimulated: {
        "id": "",
        "name": "qptionSimulated",
        "modules": [ 
            {
                "name": "noticeModule",
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                "param": {
                    "UniqueKey": "ANNC_1004",
                    "type": "1002",
                    "subtype": "10020074"
                }
            },
            {
                name: 'checkSdVersionModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    version: '8.01.001' // 该业务支持的客户端版本
                }
            },
            {
                name: 'goSdModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    bizType: '010135'
                }
            }
        ]
    },
    // 关联关系确认
    associationConfirm_sd: {
        id: '',
        name: 'associationConfirm',
        modules: [
            {
                name: 'noticeModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    UniqueKey: 'ANNC_1004',
                    type: '1002',
                    subtype: '10020059'
                }
            },
            {
                name: 'checkSdVersionModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    version: '8.02.002', // 该业务支持的客户端版本
                }
            },
            {
                name: 'goSdModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    bizType:'010197',
                    specialLink: 'businessIntroduce' // 特殊路由
                }
            }
        ]
    },
    reserveListModule_sd: {
        id: '',
        name: 'reserveListModule',
        modules: [
            {
                name: 'noticeModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    UniqueKey: 'ANNC_1004',
                    type: '1002',
                    subtype: '10020031'
                }
            },
            {
                name: 'checkSdVersionModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    version: '8.02.002' // 该业务支持的客户端版本
                }
            },
            {
                name: 'goSdModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    bizType: comm.TOOLS.GetQueryString("businessType") ? '' : '010262',
                    specialLink: comm.TOOLS.GetQueryString("businessType") ? 'bcPreBizList/010277' : 'businessIntroduce' // 特殊路由
                }
            }
        ]
    },
    //产品购买双录
    doubleRecordList_sd: {
        id: '',
        name: 'doubleRecordList',
        modules: [
            {
                name: 'noticeModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    UniqueKey: 'ANNC_1004',
                    type: '1002',
                    subtype: '********'
                }
            },
            {
                name: 'checkSdVersionModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    version: '8.02.002' // 该业务支持的客户端版本
                }
            },
            {
                name: 'goSdModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    specialLink: 'doubleRecord'
                }
            }
        ]
    },
    //指定双录流程
    doubleRecord_sd: {
        id: '',
        name: 'doubleRecord_sd',
        modules: [
            {
                name: 'noticeModule',
                setting: {
                    isOpenNewPage: false,
                    isCache: false
                },
                param: {
                    UniqueKey: 'ANNC_1004',
                    type: '1002',
                    subtype: '********'
                }
            },
            {
                name: 'preHandlerForWebModule',//机构户校验
                setting: {
                    isOpenNewPage: false,
                    isCache: false
                },
                param: {
                    needPersonal: true,
                    needCredit: false,
                    pageTitle:'产品购买双录',
                },
                result: {}
            },
            {
                name: 'checkSdVersionModule',
                setting: {
                    isOpenNewPage: false,
                    isCache: false
                },
                param: {
                    version: '8.02.002' // 该业务支持的客户端版本
                }
            },
            {
                name: 'goSdModule',
                setting: {
                    isOpenNewPage: false,
                    isCache: false
                },
                param: {
                    specialLink: 'doubleRecord/' + comm.TOOLS.GetQueryString("double_record_id")
                }
            }
        ]
    },
    //指定交易状态查询
    specified_sd: {
        "id": "",
        "name": "specified",
        "modules": [
            {
                "name": "noticeModule",
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                "param": {
                    "UniqueKey": "ANNC_1004",
                    "type": "1002",
                    "subtype": "********"
                }
            },
            {
                name: 'checkSdVersionModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    version: '8.01.001' // 该业务支持的客户端版本
                }
            },
            {
                name: 'goSdModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    bizType: '010053'
                }
            }
        ]
    },
    // 关联关系确认
    associationConfirm_sd: {
        id: '',
        name: 'associationConfirm',
        modules: [
            {
                name: 'noticeModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    UniqueKey: 'ANNC_1004',
                    type: '1002',
                    subtype: '10020059'
                }
            },
            {
                name: 'checkSdVersionModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    version: '8.01.001' // 该业务支持的客户端版本
                }
            },
            {
                name: 'goSdModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    bizType:'010197',
                    specialLink: 'introduce' // 特殊路由
                }
            }
        ]
    },
    // 债券专业投资者认定
    // bondProfessionalInvestor: {
    //     "id": "",
    //     "name": "bondProfessionalInvestor",
    //     "modules": [ 
    //         {
    //             "name": "noticeModule",
    //             setting: {
    //                 isOpenNewPage: true,
    //                 isCache: false
    //             },
    //             "param": {
    //                 "UniqueKey": "ANNC_1004",
    //                 "type": "1002",
    //                 "subtype": "10020072"
    //             }
    //         },
    //         {
    //             name: 'checkSdVersionModule',
    //             setting: {
    //                 isOpenNewPage: true,
    //                 isCache: false
    //             },
    //             param: {
    //                 version: '8.01.001' // 该业务支持的客户端版本
    //             }
    //         },
    //         {
    //             name: 'goSdModule',
    //             setting: {
    //                 isOpenNewPage: true,
    //                 isCache: false
    //             },
    //             param: {
    //                 bizType: '010066',
    //                 specialLink: 'introduce'
    //             }
    //         }
    //     ]
    // },


    // 补开信用股东户
    creditA_sd: {
        "id": "",
        "name": "creditA",
        "modules": [
            {
                "name": "noticeModule",
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                "param": {
                    "UniqueKey": "ANNC_1004",
                    "type": "1002",
                    "subtype": "********"
                }
            },
            {
                name: 'checkSdVersionModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    version: '8.02.002' // 该业务支持的客户端版本
                }
            },
            {
                name: 'goSdModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    bizType:'010099',
                }
            }]
    },
    // 科创板权限开通
    stibQuery_sd: {
        id: '',
        name: 'stibQuery',
        modules: [
            {
                name: 'noticeModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    UniqueKey: 'ANNC_1004',
                    type: '1002',
                    subtype: '********'
                }
            },
            {
                name: 'checkSdVersionModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    version: '8.02.002' // 该业务支持的客户端版本
                }
            },
            {
                name: 'goSdModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    bizType:'010086',
                    specialLink: 'businessIntroduce' // 特殊路由
                }
            }
        ]
    },
    //港股通查询
    ggtQuery_sd: {
        id: '',
        name: 'ggtQuery',
        param: {},
        modules: [
            {
                name: 'noticeModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    UniqueKey: 'ANNC_1004',
                    type: '1002',
                    subtype: '********'
                },
                result: {}
            },
            {
                name: 'checkSdVersionModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    version: '8.02.002' // 该业务支持的客户端版本
                }
            },
            {
                name: 'goSdModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    bizType:'010080',
                    specialLink: 'businessIntroduce' // 特殊路由
                }
            }
        ]
    }, 
    //开通报价回购权限
    quoteRepurchase_sd: {
        id: '',
        name: 'quoteRepurchase',
        modules: [
            {
                name: 'noticeModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    UniqueKey: 'ANNC_1004',
                    type: '1002',
                    subtype: '10020064'
                }
            },
            {
                name: 'checkSdVersionModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    version: '8.02.002' // 该业务支持的客户端版本
                }
            },
            {
                name: 'goSdModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    bizType:'010254',
                    specialLink: 'businessIntroduce' // 特殊路由
                }
            }
        ]
    }, 
    //风险警示板查询
    riskwarn_sd: {
        "id": "",
        "name": "riskwarn",
        "modules": [
            {
                "name": "noticeModule",
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                "param": {
                    "UniqueKey": "ANNC_1004",
                    "type": "1002",
                    "subtype": "********"
                }
            },
            {
                name: 'checkSdVersionModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    version: '8.02.002' // 该业务支持的客户端版本
                }
            },
            {
                name: 'goSdModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    bizType:'010076',
                    specialLink: 'businessIntroduce' // 特殊路由
                }
            }
        ]
    },
    // 债券逆回购查询
    reverseReposQuery_sd: {
        id: '',
        name: 'reverseReposQuery',
        modules: [
            {
                name: 'noticeModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    UniqueKey: 'ANNC_1004',
                    type: '1002',
                    subtype: '********'//todo
                }
            },
            {
                name: 'checkSdVersionModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    version: '8.02.002' // 该业务支持的客户端版本
                }
            },
            {
                name: 'goSdModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    bizType:'010255',
                    specialLink: 'businessIntroduce' // 特殊路由
                }
            }
        ]
    },
    //场内基金开户
    "closedEndFund_sd": {
        "id": "",
        "name": "closedEndFund",
        "modules": [
            {
                "name": "noticeModule",
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                "param": {
                    UniqueKey: 'ANNC_1004',
                    type: '1002',
                    subtype: '********'//todo
                },
                "result": {}
            },
            {
                name: 'checkSdVersionModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    version: '8.02.002' // 该业务支持的客户端版本
                }
            },
            {
                name: 'goSdModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    // bizType:'',
                    specialLink: 'inExchangFoundAccount' // 特殊路由
                }
            }
        ]
    },
    //信用科创板查询
    creditStibQuery_sd: {
        id: '',
        name: 'creditStibQuery',
        modules: [
            {
                name: 'noticeModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    UniqueKey: 'ANNC_1004',
                    type: '1002',
                    subtype: '********'
                }
            },
            {
                name: 'checkSdVersionModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    version: '8.02.002' // 该业务支持的客户端版本
                }
            },
            {
                name: 'goSdModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    bizType:'010086',
                    specialLink: 'businessIntroduce' // 特殊路由
                }
            }
        ]
    },
    //三方存管
    "thirdpart_sd": {
        "id": "",
        "name": "thirdpart",
        "pageTitle": "三方存管",
        "modules": [
            {
                name: 'noticeModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    UniqueKey: 'ANNC_1004',
                    type: '1002',
                    subtype: '********'
                },
                result: {}
            },
            {
                name: 'checkSdVersionModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    version: '8.01.004' // 该业务支持的客户端版本
                }
            },
            {
                name: 'goSdModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    // bizType:'010030',
                    specialLink: 'thirdpartAccountList' // 特殊路由
                }
            }
        ]
    },

    //问卷调查列表
    "wjdcList_sd": {
        id: '',
        name: 'wjdcList',
        "modules": [
            {
                "name": "noticeModule",
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                "param": {
                    UniqueKey: 'ANNC_1004',
                    type: '1002',
                    subtype: '********'
                },
                "result": {}
            },
            {
                name: 'checkSdVersionModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    version: '8.01.001' // 该业务支持的客户端版本
                }
            },
            {
                name: 'goSdModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    specialLink: 'wjdc' // 特殊路由
                }
            }
        ]
    },
    
    // 外部站点跳转
    goSite: {
        id: '',
        name: 'goSite',
        modules: [
            {
                name: 'goSiteModule',
                setting: {
                    isOpenNewPage: false,
                    isCache: false
                },
                param: {},
                result: {}
            }
        ]
    },
    
    //信用额度--展示
    "creditLimit_sd": {
        "id": "",
        "name": "creditLimit",
        "modules": [
            {
                "name": "noticeModule",
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                "param": {
                    UniqueKey: 'ANNC_1004',
                    type: '1002',
                    subtype: '10020009'
                },
                "result": {}
            },
            {
                name: 'checkSdVersionModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    version: '8.01.001' // 该业务支持的客户端版本
                }
            },
            {
                name: 'goSdModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    // bizType:'010098',
                    specialLink: 'showCreditQuota' // 特殊路由
                }
            }
        ]
    },
    
    // 退市整理查询
    deListQuery_sd: {
        id: '',
        name: 'deListQuery',
        modules: [
            {
                name: 'noticeModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    UniqueKey: 'ANNC_1004',
                    type: '1002',
                    subtype: '********'
                }
            },
            {
                name: 'checkSdVersionModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    version: '8.02.002' // 该业务支持的客户端版本
                }
            },
            {
                name: 'goSdModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    bizType:'010078',
                    specialLink: 'businessIntroduce' // 特殊路由
                }
            }
        ]
    },
    // CDR 存托凭证权限
    cdrQuery_sd: {
        id: '',
        name: 'cdrQuery',
        modules: [
            {
                name: 'noticeModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    UniqueKey: 'ANNC_1004',
                    type: '1002',
                    subtype: '********'//todo
                }
            },
            {
                name: 'checkSdVersionModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    version: '8.01.001' // 该业务支持的客户端版本
                }
            },
            {
                name: 'goSdModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    bizType:'010084',
                    specialLink: 'businessIntroduce' // 特殊路由
                }
            }
        ]
    },

    // 中签资金预冻结
    fundPrefrozen_sd: {
        id: '',
        name: 'fundPrefrozen',
        modules: [
            {
                name: 'noticeModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    UniqueKey: 'ANNC_1004',
                    type: '1002',
                    subtype: '10020065'
                }
            },
            {
                name: 'checkSdVersionModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    version: '8.02.002' // 该业务支持的客户端版本
                }
            },
            {
                name: 'goSdModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    bizType:'010256',
                    specialLink: 'businessIntroduce' // 特殊路由
                }
            }
        ]
    }, 
    // 创新企业股票交易权限  查询
    innovationStockQuery_sd: {
        id: '',
        name: 'innovationStockQuery',
        modules: [
            {
                name: 'noticeModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    UniqueKey: 'ANNC_1004',
                    type: '1002',
                    subtype: '********'//
                }
            },
            {
                name: 'checkSdVersionModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    version: '8.01.001' // 该业务支持的客户端版本
                }
            },
            {
                name: 'goSdModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    bizType:'010283',
                    specialLink: 'businessIntroduce' // 特殊路由
                }
            }
        ]
    },

    //电子协议查询
    "contractQuery_sd": {
        id: '',
        name: 'contractQuery',
        "modules": [
            {
                name: 'noticeModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    UniqueKey: 'ANNC_1004',
                    type: '1002',
                    subtype: '10020028'
                }
            },
            {
                name: 'checkSdVersionModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    version: '8.01.001' // 该业务支持的客户端版本
                }
            },
            {
                name: 'goSdModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    specialLink: 'contractList' // 特殊路由
                }
            }
        ]
    },

    // 开通创业板
    startUpBoardV2Query_sd: {
        id: '',
        name: 'startUpBoardV2Query',
        modules: [
            {
                name: 'noticeModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    UniqueKey: 'ANNC_1004',
                    type: '1002',
                    subtype: '********' 
                }
            },
            {
                name: 'checkSdVersionModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    version: '8.02.002' // 该业务支持的客户端版本
                }
            },
            {
                name: 'goSdModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    bizType:'010061',
                    subBizType:'1',
                    specialLink: 'businessIntroduce' // 特殊路由
                }
            }
        ]
    },
    
    //创业板转签查询
    startUpBoardQuery_sd: {
        id: '',
        name: 'startUpBoardQuery',
        modules: [
            {
                name: 'noticeModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    UniqueKey: 'ANNC_1004',
                    type: '1002',
                    subtype: '********'
                }
            },
            {
                name: 'checkSdVersionModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    version: '8.02.002' // 该业务支持的客户端版本
                }
            },
            {
                name: 'goSdModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    bizType:'010061',
                    subBizType:'2',
                    specialLink: 'businessIntroduce' // 特殊路由
                }
            }
        ]
    },

    
    // 补签创业板
    startUpBoardV2SignQuery_sd: {
        id: '',
        name: 'startUpBoardV2SignQuery',
        modules: [
            {
                name: 'noticeModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    UniqueKey: 'ANNC_1004',
                    type: '1002',
                    subtype: '********' 
                }
            },
            {
                name: 'checkSdVersionModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    version: '8.02.002' // 该业务支持的客户端版本
                }
            },
            {
                name: 'goSdModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    bizType:'010061',
                    subBizType:'3',
                    specialLink: 'businessIntroduce' // 特殊路由
                }
            }
        ]
    },

    //信用创业板
    creditStartUpBoardQuery_sd: {
        id: '',
        name: 'creditStartUpBoardQuery',
        modules: [
            {
                name: 'noticeModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    UniqueKey: 'ANNC_1004',
                    type: '1002',
                    subtype: '********' 
                }
            },
            {
                name: 'checkSdVersionModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    version: '8.02.002' // 该业务支持的客户端版本
                }
            },
            {
                name: 'goSdModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    bizType:'010245',
                    specialLink: 'businessIntroduce' // 特殊路由
                }
            }
        ]
    },

    // 业务办理进度查询----账户权限试图
    searchProgress_sd: {
        id: '',
        name: 'searchProgress',
        modules: [
            {
                name: 'noticeModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    UniqueKey: 'ANNC_1004',
                    type: '1002',
                    subtype: '********'
                }
            },
            {
                name: 'checkSdVersionModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    version: '8.01.001' // 该业务支持的客户端版本
                }
            },
            {
                name: 'goSdModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    // bizType:'account',
                    // specialLink: 'home'
                    specialLink: 'accountPermissionAttempt'
                }
            }
        ]
    },

    //两融预约开户
    marginTradeReserve_sd: {
        id: '',
        name: 'marginTradeReserve',
        modules: [
            {
                name: 'noticeModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    UniqueKey: 'ANNC_1004',
                    type: '1002',
                    subtype: '********' //两融的预约开户的公告
                }
            },
            {
                name: 'checkSdVersionModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    version: '8.02.002', // 该业务支持的客户端版本
                }
            },
            {
                name: 'goSdModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    specialLink: 'lrIntroduce' // 特殊路由
                }
            }
        ]
    }, 
    
    // 修改资金密码
    resetFundPwd_sd: {
        id: '',
        name: 'resetpwd',
        modules: [
            {
                name: 'noticeModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    UniqueKey: 'ANNC_1004',
                    type: '1002',
                    subtype: '10020019'
                },
                result: {}
            },
            {
                name: 'checkSdVersionModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    version: '8.01.001' // 该业务支持的客户端版本
                }
            },
            {
                name: 'goSdModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    bizType:'010251',
                    // specialLink: '' // 特殊路由
                }
            }
        ]
    },

    // 修改交易密码
    resetTradePwd_sd: {
        id: '',
        name: 'resetpwd',
        modules: [
            {
                name: 'noticeModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    UniqueKey: 'ANNC_1004',
                    type: '1002',
                    subtype: '10020019'
                },
                result: {}
            },
            {
                name: 'checkSdVersionModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    version: '8.02.002' // 该业务支持的客户端版本
                }
            },
            {
                name: 'goSdModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    bizType:'010250',
                    // specialLink: '' // 特殊路由
                }
            }
        ]
    },
    
    //找回资金账号-首页
    goFindAccount_sd: {
        id: '',
        name: 'goFindAccount',
        needLogin:'0',
        modules: [
            {
                name: 'checkSdVersionModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    version: '8.01.001' // 该业务支持的客户端版本
                }
            },
            {
                name: 'goSdModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    // bizType:'',
                    specialLink: 'common/fundAccRetrieve' // 特殊路由
                }
            }
        ]
    },
    
    //开通两融合约展期自动申请权限
    marginRenewal_sd: {
        id: '',
        name: 'marginRenewal',
        modules: [
            {
                name: 'noticeModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    UniqueKey: 'ANNC_1004',
                    type: '1002',
                    subtype: '10020069'
                }
            },
            {
                name: 'checkSdVersionModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    version: '8.01.001' // 该业务支持的客户端版本
                }
            },
            {
                name: 'goSdModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    bizType:'010290',
                    specialLink: 'businessIntroduce' // 特殊路由
                }
            }
        ]
    },  

    // 专项头寸权限
    specialSecuritiesQuery_sd: {
        id: '',
        name: 'specialSecuritiesQuery',
        modules: [
            {
                name: 'noticeModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false,
                },
                param: {
                    UniqueKey: 'ANNC_1004',
                    type: '1002',
                    subtype: '********',
                },
            },
            {
                name: 'checkSdVersionModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    version: '8.01.001' // 该业务支持的客户端版本
                }
            },
            {
                name: 'goSdModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    bizType:'010261',
                    specialLink: 'businessIntroduce' // 特殊路由
                }
            }
        ],
    },
    
    //北交所
    "stockBeiQuery_sd": {
        "id": "",
        "name": "stockBeiQuery",
        "modules": [
            {
                "name": "noticeModule",
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                "param": {
                    UniqueKey: 'ANNC_1004',
                    type: '1002',
                    subtype: '********'
                },
            },
            {
                name: 'checkSdVersionModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    version: '8.02.002' // 该业务支持的客户端版本
                }
            },
            {
                name: 'goSdModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    bizType:'010180',
                    specialLink: 'businessIntroduce' // 特殊路由
                }
            }
        ]
    },
    
    //信用北交所
    creditStockBeiQuery_sd: {
        id: '',
        name: 'creditStockBeiQuery',
        modules: [
            {
                name: 'noticeModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    UniqueKey: 'ANNC_1004',
                    type: '1002',
                    subtype: '********'
                }
            },
            {
                name: 'checkSdVersionModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    version: '8.02.002' // 该业务支持的客户端版本
                }
            },
            {
                name: 'goSdModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    bizType:'010221',
                    specialLink: 'businessIntroduce' // 特殊路由
                }
            }
        ]
    },
    
    //开通行权融资业务权限
    exerciseFinancing_sd: {
        id: '',
        name: 'exerciseFinancing',
        modules: [
            {
                name: 'noticeModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    UniqueKey: 'ANNC_1004',
                    type: '1002',
                    subtype: '10020066'
                }
            },
            {
                name: 'checkSdVersionModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    version: '8.01.001' // 该业务支持的客户端版本
                }
            },
            {
                name: 'goSdModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    bizType:'010257',
                    specialLink: 'businessIntroduce' // 特殊路由
                }
            }
        ]
    }, 
    
    //新三板
    "stockRotation_sd": {
        "id": "",
        "name": "stockRotation",
        "modules": [
            {
                "name": "noticeModule",
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                "param": {
                    UniqueKey: 'ANNC_1004',
                    type: '1002',
                    subtype: '********'
                },
            },
            {
                name: 'checkSdVersionModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    version: '8.02.002' // 该业务支持的客户端版本
                }
            },
            {
                name: 'goSdModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    bizType:'010117',
                    specialLink: 'businessIntroduce' // 特殊路由
                }
            }
        ]
    },
    
    //退市板块权限开通
    delistRights_sd: {
        id: '',
        name: 'delistRights',
        modules: [
            {
                name: 'noticeModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    UniqueKey: 'ANNC_1004',
                    type: '1002',
                    subtype: '10020063'
                }
            },
            {
                name: 'checkSdVersionModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    version: '8.01.001' // 该业务支持的客户端版本
                }
            },
            {
                name: 'goSdModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    bizType:'010190',
                    specialLink: 'businessIntroduce' // 特殊路由
                }
            }
        ]
    },   
    
    //股权激励授权签署
    equityIncentiveSign_sd: {
        id: '',
        name: 'equityIncentiveSign',
        modules: [
            {
                name: 'noticeModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    UniqueKey: 'ANNC_1004',
                    type: '1002',
                    subtype: '10020070'
                }
            },
            {
                name: 'checkSdVersionModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    version: '8.01.001' // 该业务支持的客户端版本
                }
            },
            {
                name: 'goSdModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    bizType:'010198',
                    specialLink: 'businessIntroduce' // 特殊路由
                }
            }
        ]
    }, 

    // 开通PTrade/QMT权限
    pTradeQMT: {
        id: '',
        name: 'pTradeQMT',
        modules: [
            {
                name: 'noticeModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    UniqueKey: 'ANNC_1004',
                    type: '1002',
                    subtype: '10020076'
                }
            },
            {
                name: 'checkSdVersionModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    version: '8.01.001', // 该业务支持的客户端版本
                    // filterHarmony: true, // 过滤鸿蒙客户端
                }
            },
            {
                name: 'goSdModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    bizType:'010734',
                    specialLink: 'businessIntroduce' // 特殊路由
                }
            }
        ]
    }, 

    // 开通养老金账户
    openPensionAccount: {
        id: '',
        name: 'openPensionAccount',
        modules: [
            {
                name: 'noticeModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    UniqueKey: 'ANNC_1004',
                    type: '1002',
                    subtype: '********'
                }
            },
            {
                name: 'checkSdVersionModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    version: '8.01.001' // 该业务支持的客户端版本
                }
            },
            {
                name: 'goSdModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    bizType:'010293',
                    specialLink: 'businessIntroduce' // 特殊路由
                }
            }
        ]
    },

    
    //信用三方存管
    "creditthird_sd": {
        "id": "",
        "name": "creditthird",
        "pageTitle": "信用三方存管",
        "modules": [
            {
                name: 'noticeModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    UniqueKey: 'ANNC_1004',
                    type: '1002',
                    subtype: '********'
                },
                result: {}
            },
            {
                name: 'checkSdVersionModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    version: '8.02.002' // 该业务支持的客户端版本
                }
            },
            {
                name: 'goSdModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    specialLink: 'creditThirdpartAccountList' // 特殊路由
                }
            }
        ]
    },

    // 开通可转债退市整理权限
    delistHolderRights_sd: {
        id: '',
        name: 'delistHolderRights',
        "modules": [
            {
                name: 'noticeModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    UniqueKey: 'ANNC_1004',
                    type: '1002',
                    subtype: '********'
                },
                result: {}
            },
            {
                name: 'checkSdVersionModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    version: '8.01.001' // 该业务支持的客户端版本
                }
            },
            {
                name: 'goSdModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    bizType:'010249',
                    specialLink: 'businessIntroduce'
                }
            }
        ]
    },
    // 开通科科创版成长层权限
    openkcbczc: {
        id: '',
        name: 'openkcbczc',
        modules: [
            {
                name: 'noticeModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    UniqueKey: 'ANNC_1004',
                    type: '1002',
                    subtype: '10020080'
                }
            },
            {
                name: 'checkSdVersionModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    version: '8.01.001' // 该业务支持的客户端版本
                }
            },
            {
                name: 'goSdModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    bizType:'014004',
                    specialLink: 'businessIntroduce' // 特殊路由
                }
            }
        ]
    }, 
    // 开通信用科创版成长层权限
    openxykcbczc: {
        id: '',
        name: 'openxykcbczc',
        modules: [
            {
                name: 'noticeModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    UniqueKey: 'ANNC_1004',
                    type: '1002',
                    subtype: '10020080'
                }
            },
            {
                name: 'checkSdVersionModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    version: '8.01.001' // 该业务支持的客户端版本
                }
            },
            {
                name: 'goSdModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    bizType:'014004',
                    specialLink: 'businessIntroduce' // 特殊路由
                }
            }
        ]
    }, 

    /** --------------     思迪入口分割线     ------------------ */

    //港股通查询
    businessReturnVisit: {
        id: '',
        name: 'businessReturnVisit',
        param: {},
        modules: [
            {
                name: 'businessReturnVisitModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {},
                result: {}
            }
        ]
    },
    //港股通查询
    ggtQuery: {
        id: '',
        name: 'ggtQuery',
        param: {},
        modules: [
            {
                name: 'noticeModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    UniqueKey: 'ANNC_1004',
                    type: '1002',
                    subtype: '********'
                },
                result: {}
            },
            {
                name: 'ggtQueryModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    UniqueKey: 'CWHQS',
                    client_id: '($usercode)',
                    fund_account: '($fund_account)',
                    password: '($password)'
                },
                result: {}
            }
        ]
    },
    /**
     *  action(group) 依赖的数据都会被初始化,并存入 Session对应的action域中,全局依赖的公共数据会存在Session中
     *  理论上 若 B 依赖 A数据  流程编排 A->B 是没问题的,但是要尽量避免这种情况,若 A依赖B,则流程上很难调度,没必要拆分为2个module
     */
    //开通港股通
    ggtOpen: {
        id: '',
        name: 'ggtOpen',
        modules: [
            {
                name: 'conditionCheckModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    UniqueKey: "ACU_001",
                    //"business_type": "30006",  动态group配置
                    "client_id": "($usercode)",
                    "password": "($password)",
                    "op_station": "($op_station)",
                    "pageTitle": "港股通"

                },
                result: {}
            },
            {
                //适当性
                name: 'prefSuitablyModule',
                setting: {
                    isOpenNewPage: false,
                    isCache: false
                },
                param: {
                    UniqueKey: 'CWCPQ',
                    client_id: '($usercode)',
                    fund_account: '($fund_account)',
                    branch_no: '($khbranch)',
                    op_station: "($op_station)",
                    op_entrust_way: '7',
                    password: '($password)',
                    password_type: '2',
                }
            },
            {
                name: 'isGgtQuesListModule',
                setting: {
                    isOpenNewPage: false,
                    isCache: false
                },
                param: {
                    UniqueKey: 'CWEHP',
                    client_id: '($usercode)',
                    password: '($password)'
                    //paper_type:'n or s' //(n:沪港通 s:深港通)
                }
            },
            {
                name: 'quesListModule',
                setting: {
                    isOpenNewPage: false,
                    isCache: false
                },
                param: {
                    clientId: '($usercode)',
                    fundAccount: '($fund_account)',
                    password: '($password)',
                    number: '试卷题号',
                    type: '试卷类型'
                }
            },
            {
                name: 'signPactModule',
                setting: {
                    isOpenNewPage: false,
                    isCache: false
                },
                param: {}
            },
            {
                name: 'ggtRetModule',
                setting: {
                    isOpenNewPage: false,
                    isCache: false
                },
                param: {
                    type: 1, // 1 开通  0 注销
                    client_id: '($usercode)',
                    fund_account: '($fund_account)',
                    password: '($password)'
                    //UniqueKey: "CWSHSO or CWSZSO",
                    //stock_account:''
                }
            }
        ]
    },

    //注销港股通
    ggtCancellation: {
        id: '',
        name: 'ggtCancellation',
        modules: [
            {
                name: 'conditionCheckModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    UniqueKey: "ACU_001",
                    //"business_type": "30006",  动态group配置
                    "client_id": "($usercode)",
                    "password": "($password)",
                    "op_station": "($op_station)",
                    "pageTitle": "港股通"

                },
                result: {}
            },
            {
                "name": "cancelConfirmModule",//取消确认弹框
                setting: {
                    isOpenNewPage: false,
                    isCache: false
                },
                "param": {
                    //rightName:'',//权限名称
                },
                "result": {}
            },
            {
                name: 'ggtRetModule',
                setting: {
                    isOpenNewPage: false,
                    isCache: false
                },
                param: {
                    type: 0, // 1 开通  0 注销
                    client_id: '($usercode)',
                    fund_account: '($fund_account)',
                    password: '($password)'
                    //UniqueKey: "CWSHSC or CWSZSC",
                    //stock_account:''
                }
            }
        ]
    },

    // //创业板转签查询
    // startUpBoardQuery: {
    //     id: '',
    //     name: 'startUpBoardQuery',
    //     modules: [
    //         {
    //             name: 'noticeModule',
    //             setting: {
    //                 isOpenNewPage: true,
    //                 isCache: false
    //             },
    //             param: {
    //                 UniqueKey: 'ANNC_1004',
    //                 type: '1002',
    //                 subtype: '********'
    //             }
    //         },
    //         {
    //             name: 'startUpBoardQueryModule',
    //             setting: {
    //                 isOpenNewPage: true,
    //                 isCache: false
    //             },
    //             param: {
    //                 UniqueKey: 'CWCNI',
    //                 client_id: '($usercode)',
    //                 fund_account: '($fund_account)',
    //                 password: '($password)'
    //             },
    //             result: {}
    //         }
    //     ]
    // },
    // //创业板转签更新
    // startUpBoardUpdate: {
    //     id: '',
    //     name: 'startUpBoardUpdate',
    //     modules: [
    //         {
    //             name: 'conditionCheckModule',
    //             setting: {
    //                 isOpenNewPage: true,
    //                 isCache: false
    //             },
    //             param: {
    //                 UniqueKey: "ACU_001",
    //                 "business_type": "30005",
    //                 "client_id": "($usercode)",
    //                 "password": "($password)",
    //                 "op_station": "($op_station)",
    //                 "pageTitle": "创业板"
    //             },
    //             result: {}
    //         },
    //         {
    //             //适当性
    //             name: 'suitablyModule',
    //             setting: {
    //                 isOpenNewPage: false,
    //                 isCache: false
    //             },
    //             param: {}
    //         },
    //         {   //风险揭示书
    //             name: 'signPactModule',
    //             setting: {
    //                 isOpenNewPage: false,
    //                 isCache: false
    //             },
    //             param: {
    //                 type:'startupboard',
    //                 marketType:'sz'
    //             }
    //         },
    //         {
    //             name: 'startUpBoardUpdateModule',
    //             setting: {
    //                 isOpenNewPage: false,
    //                 isCache: false
    //             },
    //             param: {
    //                 UniqueKey: 'CWCNT',
    //                 client_id: '($usercode)',
    //                 fund_account: '($fund_account)',
    //                 password: '($password)'
    //                 //stock_account:'',
    //                 //gen_train_flag:''
    //             }
    //         }
    //     ]
    // },
    /**
     * 退市整理查询
     */
    deListQuery: {
        id: '',
        name: 'deListQuery',
        modules: [
            {
                name: 'noticeModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    UniqueKey: 'ANNC_1004',
                    type: '1002',
                    subtype: '********'
                }
            },
            {
                name: 'deListQueryModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    UniqueKey: 'CWDQS',
                    clientId: '($usercode)',
                    fund_account: '($fund_account)',
                    password: '($password)',
                },
                result: {}
            }
        ]
    },
    //退市整理开通
    deListOpen: {
        id: '',
        name: 'deListOpen',
        modules: [
            {
                name: 'conditionCheckModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    UniqueKey: "ACU_001",
                    "business_type": "30008",//退市整理开通
                    "client_id": "($usercode)",
                    "password": "($password)",
                    "op_station": "($op_station)",
                    "pageTitle": "退市整理板"

                },
                result: {}
            },
            {
                //适当性
                name: 'prefSuitablyModule',
                setting: {
                    isOpenNewPage: false,
                    isCache: false
                },
                param: {
                    UniqueKey: 'CWCPQ',
                    client_id: '($usercode)',
                    fund_account: '($fund_account)',
                    branch_no: '($khbranch)',
                    op_station: "($op_station)",
                    op_entrust_way: '7',
                    password: '($password)',
                    password_type: '2',
                }
            },
            {
                name: 'signPactModule',
                setting: {
                    isOpenNewPage: false,
                    isCache: false
                },
                "param": {}
            },
            {
                name: 'deListRetModule',
                setting: {
                    isOpenNewPage: false,
                    isCache: false
                },
                param: {
                    type: 1, // 1 开通  0 注销
                    UniqueKey: "CWDOR",
                    client_id: '($usercode)',
                    fund_account: '($fund_account)',
                    password: "($password)",
                    op_station: "($op_station)"
                }
            }
        ]
    },
    //退市整理注销
    deListCancellation: {
        id: '',
        name: 'ggtCancellation',
        modules: [
            {
                name: "conditionCheckModule",
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    UniqueKey: "ACU_001",
                    "business_type": "30020",//退市整理注销
                    "client_id": "($usercode)",
                    "password": "($password)",
                    "op_station": "($op_station)",
                    "pageTitle": "退市整理板"

                },
                result: {}
            },
            {
                "name": "cancelConfirmModule",//取消确认弹框
                setting: {
                    isOpenNewPage: false,
                    isCache: false
                },
                "param": {
                    //rightName:'',//权限名称
                },
                "result": {}
            },
            {
                name: 'deListRetModule',
                setting: {
                    isOpenNewPage: false,
                    isCache: false
                },
                param: {
                    type: 0, // 0 开通  1 注销
                    UniqueKey: "CWDCR",
                    client_id: '($usercode)',
                    fund_account: '($fund_account)',
                    password: "($password)",
                    op_station: "($op_station)"
                }
            }
        ]
    },


    //风险测评
    risk: {
        id: '',
        name: 'risk',
        modules: [
            {
                name: 'riskModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {}
            }
        ]
    },
    //风险测评结果
    riskResult: {
        id: '',
        name: 'risk',
        modules: [
            {
                name: 'riskResultModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {}
            }

        ]
    },

    //风险警示板查询
    "riskwarn": {
        "id": "",
        "name": "riskwarn",
        "modules": [
            {
                "name": "noticeModule",
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                "param": {
                    "UniqueKey": "ANNC_1004",
                    "type": "1002",
                    "subtype": "********"
                }
            },
            {
                "name": "riskwarnModule",
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                "param": {
                    UniqueKey: "CWRQ_001",
                    client_id: '($usercode)',
                    fund_account: '($fund_account)',
                    password: '($password)'
                }
            }]
    },
    //风险警示板开通
    "riskwarnOpen": {
        "id": "",
        "name": "riskwarnOpen",
        "modules": [
            {
                "name": "conditionCheckModule",
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                "param": {
                    UniqueKey: "ACU_001",
                    "business_type": "30007",
                    "client_id": "($usercode)",
                    "password": "($password)",
                    "op_station": "($op_station)",
                    "pageTitle": "风险警示"
                },
                "result": {}
            },
            {
                "name": "prefSuitablyModule",
                setting: {
                    isOpenNewPage: false,
                    isCache: false
                },
                "param": {
                    // type: 'riskwarn',
                    // marketType: 'sh'
                    UniqueKey: 'CWCPQ',
                    client_id: '($usercode)',
                    fund_account: '($fund_account)',
                    branch_no: '($khbranch)',
                    op_station: "($op_station)",
                    op_entrust_way: '7',
                    password: '($password)',
                    password_type: '2',
                }
            },
            {
                "name": "signPactModule",
                setting: {
                    isOpenNewPage: false,
                    isCache: false
                },
                "param": {
                    // type: 'riskwarn',
                    // marketType: 'sh'
                }
            },
            {   //开通风险警示板结果
                "name": "riskwarnResultModule",
                setting: {
                    isOpenNewPage: false,
                    isCache: true
                },
                "param": {
                    UniqueKey: "CWRQ_002",
                    client_id: '($usercode)',
                    fund_account: '($fund_account)',
                    // exchange_type: '1',
                    // stock_account: '',
                    op_entrust_way: '7',
                    op_station: "($op_station)",
                    password: "($password)"
                }
            }]
    },
    //风险警示板注销
    "riskwarnCancel": {
        "id": "",
        "name": "riskwarnCancel",
        "modules": [
            {
                "name": "conditionCheckModule",
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                "param": {
                    UniqueKey: "ACU_001",
                    "business_type": "30019",
                    "client_id": "($usercode)",
                    "password": "($password)",
                    "op_station": "($op_station)",
                    "pageTitle": "风险警示"
                },
                "result": {}
            },
            {
                "name": "cancelConfirmModule",//取消确认弹框
                setting: {
                    isOpenNewPage: false,
                    isCache: false
                },
                "param": {
                    //rightName:'',//权限名称
                },
                "result": {}
            },
            {//注销风险警示板结果
                "name": "riskwarnResultModule",
                setting: {
                    isOpenNewPage: false,
                    isCache: true
                },
                "param": {
                    UniqueKey: "CWRQ_003",
                    client_id: '($usercode)',
                    fund_account: '($fund_account)',
                    // exchange_type: '1',
                    // stock_account: '',
                    op_entrust_way: '7',
                    op_station: "($op_station)",
                    password:'($password)',
                }
            }]
    },
    //指定交易状态查询
    "specified": {
        "id": "",
        "name": "specified",
        "modules": [
            {
                "name": "noticeModule",
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                "param": {
                    "UniqueKey": "ANNC_1004",
                    "type": "1002",
                    "subtype": "********"
                }
            },
            {
                "name": "specifiedModule",
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                "param": {
                    "UniqueKey": "SNS_001",
                    client_id: '($usercode)',
                    fund_account: '($fund_account)',
                    password: '($password)',
                    op_entrust_way: '7',
                    op_station: "($op_station)"
                }
            }]
    },
    //指定交易办理
    "specifiedOpen": {
        "id": "",
        "name": "specifiedOpen",
        "modules": [
            {
                "name": "conditionCheckModule",
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                "param": {
                    UniqueKey: "ACU_001",
                    "business_type": "30004",
                    "client_id": "($usercode)",
                    "password": "($password)",
                    "op_station": "($op_station)",
                    "pageTitle": "指定交易"
                },
                "result": {}
            },
            {
                "name": "specifiedResultModule",
                setting: {
                    isOpenNewPage: true,
                    isCache: true
                },
                "param": {
                    "UniqueKey": "CWS_001",
                    client_id: '($usercode)',
                    fund_account: '($fund_account)',
                    password: '($password)',
                    op_station: "($op_station)"
                }
            }]
    },
    //信用A股
    "creditA": {
        "id": "",
        "name": "creditA",
        "modules": [
            {
                "name": "noticeModule",
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                "param": {
                    "UniqueKey": "ANNC_1004",
                    "type": "1002",
                    "subtype": "********"
                }
            },
            {
                "name": "creditAModule",
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                "param": {
                    UniqueKey: "ACSCSI",
                    client_id: '($usercode)',
                    fund_account: '($fund_account)',
                    password: '($password)',
                    op_station: "($op_station)"
                }
            }]
    },
    "creditAForWeb": {
        "id": "",
        "name": "creditA",
        "modules": [
            {
                name: 'preHandlerForWebModule',//机构户校验
                setting: {
                    isOpenNewPage: false,
                    isCache: false,
                    pageTitle:'信用A股',
                },
                param: {
                    needPersonal: true,
                    needCredit: true,
                    creditNextStep:'openCredit',
                },
                result: {}
            },
            {
                "name": "noticeModule",
                setting: {
                    isOpenNewPage: false,
                    isCache: false
                },
                "param": {
                    "UniqueKey": "ANNC_1004",
                    "type": "1002",
                    "subtype": "********"
                }
            },
            {
                "name": "creditAModule",
                setting: {
                    isOpenNewPage: false,
                    isCache: false
                },
                "param": {
                    UniqueKey: "ACSCSI",
                    client_id: '($usercode)',
                    fund_account: '($fund_account)',
                    password: '($password)',
                    op_station: "($op_station)"
                }
            }]
    },

    //补开信用A股
    "creditAOpen": {
        "id": "",
        "name": "creditAOpen",
        "modules": [
            {
                "name": "conditionCheckModule",
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                "param": {
                    UniqueKey: "ACU_001",
                    "business_type": "30009",
                    "client_id": "($usercode)",
                    "password": "($password)",
                    "op_station": "($op_station)",
                    "pageTitle": "信用A股"
                },
                "result": {}
            },
            {
                "name": "creditAResultModule",
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                "param": {
                    UniqueKey: "ACSCO",
                    client_id: '($usercode)',
                    fund_account: '($fund_account)',
                    password: '($password)',
                    op_station: "($op_station)",
                    open_type: ""
                }
            }]
    },

    //A股账户补开户--展示
    "stockA": {
        "id": "",
        "name": "stockA",
        "modules": [
            {
                "name": "noticeModule",
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                "param": {
                    UniqueKey: 'ANNC_1004',
                    type: '1002',
                    subtype: '********'
                },
                "result": {}
            },
            {
                "name": "stockAModule",
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                "param": {},
                "result": {}
            }]
    },
    //A股账户补开户--开通
    "stockAOpen": {
        "id": "",
        "name": "stockAOpen",
        "modules": [
            {
                "name": "conditionCheckModule",
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                "param": {
                    UniqueKey: "ACU_001",
                    "business_type": "30002",
                    "client_id": "($usercode)",
                    "password": "($password)",
                    "op_station": "($op_station)",
                    "pageTitle": "A股账户"
                },
                "result": {}
            },
            {
                "name": "stockAOpenModule",
                setting: {
                    isOpenNewPage: false,
                    isCache: false
                },
                "param": {},
                "result": {}
            },
            {
                name: 'stockARetModule',
                setting: {
                    isOpenNewPage: false,
                    isCache: false
                },
                param: {},
                result: {}
            }
        ]
    },
    //A股账户补开户--申请结果
    stockAApplyRet: {
        id: '',
        name: 'stockAApplyRetModule',
        modules: [
            {
                name: 'stockAApplyRetModule',
                setting: {
                    isOpenNewPage: false,
                    isCache: false
                },
                param: {
                },
                result: {}
            }
        ]
    },
    //信用额度--展示
    "creditLimit": {
        "id": "",
        "name": "creditLimit",
        "modules": [
            {
                "name": "noticeModule",
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                "param": {
                    UniqueKey: 'ANNC_1004',
                    type: '1002',
                    subtype: '10020009'
                },
                "result": {}
            },
            {
                "name": "creditLimitModulue",
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                "param": {},
                "result": {}
            },
        ]
    },
    //信用调额--(调额|征信|不能调额）展示
    "creditLimitAdjust": {
        "id": "",
        "name": "creditLimitAdjust",
        "modules": [
            {
                name: 'conditionCheckModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    UniqueKey: "ACU_001",
                    "business_type": "30010",//信用额度
                    "client_id": "($usercode)",
                    "password": "($password)",
                    "op_station": "($op_station)",
                    "pageTitle": "信用额度"
                }
            },
            {
                "name": "creditLimitAdjustModule",
                "setting": {
                    isOpenNewPage: false,
                    isCache: false
                },
                "param": {},
                "result": {}
            },
        ]
    },
    //信用调额--调额并展示结果
    "creditLimitRet": {
        "id": "",
        "name": "creditLimitRet",
        "modules": [
            {
                "name": "creditLimitRetModule",
                "setting": {
                    isOpenNewPage: false,
                    isCache: false
                },
                "param": {},
                "result": {}
            },
        ]
    },
    //信用调额--深度征信并展示结果
    //移除深度征信流程
    // "creditLimitInvest": {
    //     "id": "",
    //     "name": "creditLimitInvest",
    //     "modules": [
    //         {
    //             "name": "creditLimitInvestModule",
    //             "setting": {
    //                 isOpenNewPage: false,
    //                 isCache: false
    //             },
    //             "param": {},
    //             "result": {}
    //         },
    //     ]
    // },
    //问卷调查列表
    "wjdcList": {
        id: '',
        name: 'wjdcList',
        "modules": [
            {
                name: 'wjdcListModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {},
                result: {}
            },
        ]
    },
    //问卷调查
    "wjdc": {
        id: '',
        name: 'wjdc',
        "modules": [
            {
                "name": "noticeModule",
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                "param": {
                    UniqueKey: 'ANNC_1004',
                    type: '1002',
                    subtype: '********'
                },
                "result": {}
            },
            {
                name: 'conditionCheckModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    UniqueKey: "ACU_001",
                    "business_type": "30016",//问卷调查
                    "client_id": "($usercode)",
                    "password": "($password)",
                    "op_station": "($op_station)",
                    "pageTitle": "问卷调查"
                }
            },
            {
                "name": "wjdcModule",
                "setting": {
                    isOpenNewPage: false,
                    isCache: false
                },
                "param": {},
                "result": {}
            },
        ]
    },
    //建议与投诉
    "wjdcFeedBack": {
        id: '',
        name: 'wjdcFeedBack',
        "modules": [
            {
                "name": "noticeModule",
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                "param": {
                    UniqueKey: 'ANNC_1004',
                    type: '1002',
                    subtype: '********'
                },
                "result": {}
            },
            {
                name: 'conditionCheckModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    UniqueKey: "ACU_001",
                    "business_type": "30016",//问卷调查
                    "client_id": "($usercode)",
                    "password": "($password)",
                    "op_station": "($op_station)",
                    "pageTitle": "问卷调查"
                }
            },
            {
                "name": "wjdcFeedBackModule",
                "setting": {
                    isOpenNewPage: false,
                    isCache: false
                },
                "param": {},
                "result": {}
            },
        ]
    },
    //身份更新
    idupdate: {
        id: '',
        name: 'idupdate',
        modules: [
            {
                name: 'noticeModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    UniqueKey: 'ANNC_1004',
                    type: '1002',
                    subtype: '********'
                }
            },
            {
                name: 'idupdateModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    clientId: '($usercode)',
                    fundAccount: '($fund_account)',
                    password: '($password)'
                }
            }
        ]
    },
    //身份证更新的条件检测
    idupdateItem: {
        id: '',
        name: 'idupdateItem',
        modules: [
            {
                name: 'conditionCheckModule',
                setting: {
                    isOpenNewPage: false,
                    isCache: false
                },
                param: {
                    UniqueKey: "ACU_001",
                    "business_type": "30015",//身份证更新
                    "client_id": "($usercode)",
                    "password": "($password)",
                    "op_station": "($op_station)",
                    "pageTitle": "身份证更新"
                },
                result: {}
            },
            {
                name: 'goSiteModule',
                setting: {
                    isOpenNewPage: false,
                    isCache: false
                },
                param: {},
                result: {}
            }
        ]
    },
    //找回资金账号
    findAccount: {
        id: '',
        name: 'findAccount',
        modules: [
            {
                name: 'noticeModule',
                setting: {
                    isOpenNewPage: false,
                    isCache: false
                },
                param: {
                    UniqueKey: 'ANNC_1004',
                    type: '1002',
                    subtype: '********',
                    nocheckLogin: true
                }
            },
            {
                name: 'goSiteModule',
                setting: {
                    isOpenNewPage: false,
                    isCache: false
                },
                param: {},
                result: {}
            }
        ]
    },
    //找回资金账号、清密引导页
    selectWay: {
        id: '',
        name: 'selectWay',
        modules: [
            {
                name: 'goSiteModule',
                setting: {
                    isOpenNewPage: false,
                    isCache: false
                },
                param: {},
                result: {}
            }
        ]
    },
    //开通融资融券提示页面
    openCredit: {
        id: '',
        name: 'openCredit',
        modules: [
            {
                name: 'openCreditModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {},
                result: {}
            }
        ]
    },
    //开通融资融券提示前置页面
    openCreditHint: {
        id: '',
        name: 'openCreditHint',
        modules: [
            {
                name: 'openCreditHintModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {},
                result: {}
            },
            {
                name: 'openCreditModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {},
                result: {}
            }
        ]
    },
    //深交所密码----start
    sztradepwdList: {
        id: '',
        name: 'sztradepwdList',
        "modules": [
            {
                name: 'noticeModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    UniqueKey: 'ANNC_1004',
                    type: '1002',
                    subtype: '10020020',
                    nocheckLogin: true
                }
            },
            {
                name: 'conditionCheckModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    UniqueKey: "ACU_001",
                    "business_type": "30024",//深交所密码
                    "client_id": "($usercode)",
                    "password": "($password)",
                    "op_station": "($op_station)",
                    "pageTitle": "深交所密码服务"
                },
                result: {}
            },
            {
                name: 'sztradepwdListModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {},
                result: {}
            },
        ]
    },
    szpwdApply: {  //申请密码
        id: '',
        name: 'szpwdApply',
        "modules": [
            {
                name: 'szpwdApplyModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {},
                result: {}
            },
        ]
    },
    szpwdActive: {  //激活密码
        id: '',
        name: 'szpwdActive',
        "modules": [
            {
                name: 'szpwdActiveModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {},
                result: {}
            },
        ]
    },
    szpwdReport: {  //挂失密码
        id: '',
        name: 'szpwdReport',
        "modules": [
            {
                name: 'szpwdReportModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {},
                result: {}
            },
        ]
    },
    //深交所密码--end

    //个人资料
    personal: {
        id: '',
        name: 'personal',
        modules: [
            {
                name: 'noticeModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    UniqueKey: 'ANNC_1004',
                    type: '1002',
                    subtype: '10020001'
                },
                result: {}
            },
            {
                name: 'preHandlerForWebModule',//机构户校验
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    needPersonal: true,
                    needCredit: false,
                    pageTitle:'修改个人资料',
                },
                result: {}
            },
            {
                name: 'personalModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {},
                result: {}
            }
        ]
    },

    //个人资料进入具体业务模块
    personalItem: {
        id: '',
        name: 'personalItem',
        modules: [
            {
                name: 'conditionCheckModule',
                setting: {
                    isOpenNewPage: false,
                    isCache: false
                },
                param: {
                    UniqueKey: "ACU_001",
                    "business_type": "30011",//个人资料
                    "client_id": "($usercode)",
                    "password": "($password)",
                    "op_station": "($op_station)",
                    "pageTitle": "个人资料"
                },
                result: {}
            },
            {
                name: 'goSiteModule',
                setting: {
                    isOpenNewPage: false,
                    isCache: false
                },
                param: {},
                result: {}
            }
        ]
    },

    //个人资料 用户个人资料与中登不一致
    personalForCsdcExit: {
        id: '',
        name: 'personal',
        modules: [
            {
                name: 'noticeModule',
                setting: {
                    isOpenNewPage: false,
                    isCache: false
                },
                param: {
                    UniqueKey: 'ANNC_1004',
                    type: '1002',
                    subtype: '10020001'
                },
                result: {}
            },
            {
                name: 'conditionCheckModule',
                setting: {
                    isOpenNewPage: false,
                    isCache: false
                },
                param: {
                    UniqueKey: "ACU_001",
                    "business_type": "30011",//个人资料
                    "client_id": "($usercode)",
                    "password": "($password)",
                    "op_station": "($op_station)",
                    "pageTitle": "个人资料"
                },
                result: {}
            },
            {
                name: 'personalForCsdcExitModule',
                setting: {
                    isOpenNewPage: false,
                    isCache: false
                },
                param: {},
                result: {}
            }
        ]
    },

    //三方存管
    "thirdpart": {
        "id": "",
        "name": "thirdpart",
        "pageTitle": "三方存管",
        "modules": [
            {
                name: 'noticeModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    UniqueKey: 'ANNC_1004',
                    type: '1002',
                    subtype: '********'
                },
                result: {}
            },
            {
                "name": "thirdpartModule",
                "setting": {
                    isOpenNewPage: true,
                    isCache: true
                },
                "param": {
                    UniqueKey: 'BDQ_001',
                    client_id: '($client_id)',
                    fund_account: '($fund_account)',
                    password: '($password)',
                    branch_no: '($khbranch)',
                },
                "result": {}
            },
        ]
    },
    //信用三方存管
    "creditthird": {
        "id": "",
        "name": "creditthird",
        "pageTitle": "信用三方存管",
        "modules": [
            {
                name: 'noticeModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    UniqueKey: 'ANNC_1004',
                    type: '1002',
                    subtype: '********'
                },
                result: {}
            },
            {
                "name": "creditthirdModule",
                "setting": {
                    isOpenNewPage: true,
                    isCache: true
                },
                "param": {
                    UniqueKey: 'BDQ_001',
                    client_id: '($client_id)',
                    fund_account: '($fund_account)',
                    password: '($password)',
                    branch_no: '($khbranch)',
                    asset_prop: '7'//信用账户
                },
                "result": {}
            },
        ]
    },

    //三方存管变更
    "thirdmodify": {
        "id": "",
        "name": "thirdmodify",
        "pageTitle": "三方存管",
        "modules": [
            {
                name: 'conditionCheckModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    UniqueKey: "ACU_001",
                    "business_type": "30026", //三方存管变更的business_type
                    "client_id": "($usercode)",
                    "password": "($password)",
                    "op_station": "($op_station)",
                    "pageTitle": "三方存管"
                },
                result: {}
            },
            {
                name: 'thirdCheckModule',
                setting: {
                    isOpenNewPage: false,
                    isCache: false
                },
                param: {},
                result: {}
            },
            {
                name: 'thirdOperModule',
                setting: {
                    isOpenNewPage: false,
                    isCache: false
                },
                param: {},
                result: {}
            },
        ]
    },
    "thirdmodify_bind": {     //三方存管变更--变更后重新绑定
        "id": "",
        "name": "thirdmodify",
        "pageTitle": "三方存管",
        "modules": [
            {
                name: 'thirdBindModule',
                setting: {
                    isOpenNewPage: false,
                    isCache: false
                },
                param: {},
                result: {}
            },
        ]
    },
    //三方存管变更---新开
    "thirdNewCard": {
        "id": "",
        "name": "thirdNewCard",
        "pageTitle": "三方存管",
        "modules": [
            {
                name: 'conditionCheckModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    UniqueKey: "ACU_001",
                    "business_type": "30021",
                    "client_id": "($usercode)",
                    "password": "($password)",
                    "op_station": "($op_station)",
                    "pageTitle": "三方存管"
                },
                result: {}
            },
            {
                name: 'chooseBankModule',
                setting: {
                    isOpenNewPage: false,
                    isCache: false
                },
                param: {},
                result: {}
            },
        ]
    },
    //三方存管变更---绑卡
    "thirdBindCard": {
        "id": "",
        "name": "thirdBindCard",
        "pageTitle": "三方存管",
        "modules": [
            {
                name: 'conditionCheckModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    UniqueKey: "ACU_001",
                    "business_type": "30021", //todo三方存管变更的business_type
                    "client_id": "($usercode)",
                    "password": "($password)",
                    "op_station": "($op_station)",
                    "pageTitle": "三方存管"
                },
                result: {}
            },
            {
                name: 'finishCardModule',
                setting: {
                    isOpenNewPage: false,
                    isCache: false
                },
                param: {},
                result: {}
            },
        ]
    },


    //三方存管变更
    "credit_thirdmodify": {
        "id": "",
        "name": "thirdmodify",
        "pageTitle": "信用三方存管",
        "modules": [
            {
                name: 'conditionCheckModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    UniqueKey: "ACU_001",
                    "business_type": "30029", //三方存管变更的business_type
                    "client_id": "($usercode)",
                    "password": "($password)",
                    "op_station": "($op_station)",
                    "pageTitle": "信用三方存管"
                },
                result: {}
            },
            {
                name: 'thirdCheckModule',
                setting: {
                    isOpenNewPage: false,
                    isCache: false
                },
                param: {},
                result: {}
            },
            {
                name: 'thirdOperModule',
                setting: {
                    isOpenNewPage: false,
                    isCache: false
                },
                param: {},
                result: {}
            },
        ]
    },
    "credit_thirdmodify_bind": {     //三方存管变更--变更后重新绑定
        "id": "",
        "name": "thirdmodify",
        "pageTitle": "信用三方存管",
        "modules": [
            {
                name: 'thirdBindModule',
                setting: {
                    isOpenNewPage: false,
                    isCache: false
                },
                param: {},
                result: {}
            },
        ]
    },
    //三方存管变更---新开
    "credit_thirdNewCard": {
        "id": "",
        "name": "thirdNewCard",
        "pageTitle": "信用三方存管",
        "modules": [
            {
                name: 'conditionCheckModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    UniqueKey: "ACU_001",
                    "business_type": "30027",
                    "client_id": "($usercode)",
                    "password": "($password)",
                    "op_station": "($op_station)",
                    "pageTitle": "信用三方存管",
                },
                result: {}
            },
            {
                name: 'chooseBankModule',
                setting: {
                    isOpenNewPage: false,
                    isCache: false
                },
                param: {},
                result: {}
            },
        ]
    },
    //三方存管变更---绑卡
    "credit_thirdBindCard": {
        "id": "",
        "name": "thirdBindCard",
        "pageTitle": "信用三方存管",
        "modules": [
            {
                name: 'conditionCheckModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    UniqueKey: "ACU_001",
                    "business_type": "30027", //todo三方存管变更的business_type
                    "client_id": "($usercode)",
                    "password": "($password)",
                    "op_station": "($op_station)",
                    "pageTitle": "信用三方存管",
                },
                result: {}
            },
            {
                name: 'finishCardModule',
                setting: {
                    isOpenNewPage: false,
                    isCache: false
                },
                param: {},
                result: {}
            },
        ]
    },
    //三方存管
    "thirdpartForWeb": {
        "id": "",
        "name": "thirdpart",
        "pageTitle": "三方存管",
        "modules": [
            {
                name: 'noticeModule',
                setting: {
                    isOpenNewPage: false,
                    isCache: false
                },
                param: {
                    UniqueKey: 'ANNC_1004',
                    type: '1002',
                    subtype: '********'
                },
                result: {}
            },
            {
                "name": "thirdpartModule",
                "setting": {
                    isOpenNewPage: false,
                    isCache: true
                },
                "param": {
                    UniqueKey: 'BDQ_001',
                    client_id: '($client_id)',
                    fund_account: '($fund_account)',
                    password: '($password)',
                    branch_no: '($khbranch)',
                },
                "result": {}
            },
        ]
    },
    //信用三方存管
    "creditthirdForWeb": {
        "id": "",
        "name": "creditthird",
        "pageTitle": "信用三方存管",
        "modules": [
            {
                name: 'noticeModule',
                setting: {
                    isOpenNewPage: false,
                    isCache: false
                },
                param: {
                    UniqueKey: 'ANNC_1004',
                    type: '1002',
                    subtype: '********'
                },
                result: {}
            },
            {
                "name": "creditthirdModule",
                "setting": {
                    isOpenNewPage: false,
                    isCache: true
                },
                "param": {
                    UniqueKey: 'BDQ_001',
                    client_id: '($client_id)',
                    fund_account: '($fund_account)',
                    password: '($password)',
                    branch_no: '($khbranch)',
                    asset_prop: '7'//信用账户
                },
                "result": {}
            },
        ]
    },
    //开放式基金账户
    "openfund": {
        "id": "",
        "name": "openfund",
        "modules": [
            {
                name: 'noticeModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    UniqueKey: 'ANNC_1004',
                    type: '1002',
                    subtype: '********'
                },
                result: {}
            },
            {
                "name": "openfundModule",
                "setting": {
                    isOpenNewPage: true,
                    isCache: false
                },
                "param": {
                    UniqueKey: 'CWOI',
                    client_id: '($client_id)',
                    fund_account: '($fund_account)',
                    password: '($password)',
                    branch_no: '($khbranch)',
                    "op_station": "($op_station)"
                },
                "result": {}
            },
        ]
    },
    //在线客服
    "onlineService": {
        id: '',
        name: 'onlineService',
        modules: [
            {
                name: 'noticeModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    UniqueKey: 'ANNC_1004',
                    type: '1002',
                    subtype: '********'
                },
                result: {}
            },
            {
                name: 'onlineServiceModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {},
                result: {}
            }
        ]
    },
    //查找营业部
    "findDepartment": {
        id: '',
        name: 'findDepartment',
        modules: [
            {
                name: 'findDepartmentModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {},
                result: {}
            }
        ]
    },

    //即将上线
    "comingSoon": {
        id: '',
        name: 'comingSoon',
        modules: [
            {
                name: 'comingSoonModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    content: "",
                    btnContent: "",
                    btnValue: ''

                },
                result: {}
            }
        ]
    },
    //提示
    "simpleTip": {
        id: '',
        name: 'simpleTip',
        param: {
            pageTitle: '提示'
        },
        modules: [
            {
                name: 'simpleTipModule',
                setting: {
                    isOpenNewPage: false,//由上个页面弹出
                    isCache: false
                },
                param: {
                    content: "",
                    btnName: "返回"
                },
                result: {}
            }
        ]
    },
    //修改密码
    resetpwd: {
        id: '',
        name: 'resetpwd',
        modules: [
            {
                name: 'noticeModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    UniqueKey: 'ANNC_1004',
                    type: '1002',
                    subtype: '10020019'
                },
                result: {}
            },
            {
                name: 'conditionCheckModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    UniqueKey: "ACU_001",
                    "business_type": "30012",//修改密码
                    "client_id": "($usercode)",
                    "password": "($password)",
                    "op_station": "($op_station)",
                    "pageTitle": "修改密码"
                },
                result: {}
            },
            {
                name: 'resetpwdModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {},
                result: {}
            }
        ]
    },
    resetTradePwd: {
        id: '',
        name: 'resetpwd',
        modules: [
            {
                name: 'noticeModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    UniqueKey: 'ANNC_1004',
                    type: '1002',
                    subtype: '10020019'
                },
                result: {}
            },
            {
                name: 'conditionCheckModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    UniqueKey: "ACU_001",
                    "business_type": "30012",//修改密码
                    "client_id": "($usercode)",
                    "password": "($password)",
                    "op_station": "($op_station)",
                    "pageTitle": "修改密码"
                },
                result: {}
            },
            {
                name: 'resetTradePwdModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {},
                result: {}
            }
        ]
    },
    resetFundPwd: {
        id: '',
        name: 'resetpwd',
        modules: [
            {
                name: 'noticeModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    UniqueKey: 'ANNC_1004',
                    type: '1002',
                    subtype: '10020019'
                },
                result: {}
            },
            {
                name: 'conditionCheckModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    UniqueKey: "ACU_001",
                    "business_type": "30012",//修改密码
                    "client_id": "($usercode)",
                    "password": "($password)",
                    "op_station": "($op_station)",
                    "pageTitle": "修改密码"
                },
                result: {}
            },
            {
                name: 'resetFundPwdModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {},
                result: {}
            }
        ]
    },

    //银证转账2.0
    bankTransferV2: {
        id: '',
        name: 'bankTransferV2',
        modules: [
            {
                name: 'bankTransferV2Module',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {},
                result: {}
            }
        ]
    },

    //银证转账3.0
    bankTransferV3: {
        id: '',
        name: 'bankTransferV3',
        modules: [
            {
                name: 'noticeGroupModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    UniqueKey: 'ANNC_1010',
                    type: '1003',
                    sub_type: '********'//3.0普通银证转账
                },
                result: {}
            },
            {
                name: 'bankTransferV3Module',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {},
                result: {}
            }
        ]
    },
    //信用银证转账2.0
    creditBankTransferV2: {
        id: '',
        name: 'creditBankTransferV2',
        modules: [
            {
                name: 'creditBankTransferV2Module',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {},
                result: {}
            }
        ]
    },

    //信用银证转账3.0
    creditBankTransferV3: {
        id: '',
        name: 'creditBankTransferV3Module',
        modules: [
            {
                name: 'noticeGroupModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    UniqueKey: 'ANNC_1010',
                    type: '1003',
                    sub_type: '********'//3.0信用银证转账
                },
                result: {}
            },
            {
                name: 'creditBankTransferV3Module',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {},
                result: {}
            }
        ]
    },
    //现金理财2.0
    cashV2: {
        id: '',
        name: 'cashV2',
        modules: [
            {
                name: 'noticeModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    UniqueKey: 'ANNC_1004',
                    type: '********',//2.0现金理财公告
                    subtype: '1003'
                },
                result: {}
            },
            {
                name: 'cashV2Module',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {},
                result: {}
            }
        ]
    },

    //现金理财3.0
    cashV3: {
        id: '',
        name: 'cashV3',
        modules: [
            {
                name: 'noticeGroupModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    UniqueKey: 'ANNC_1010',
                    type: '1003',
                    sub_type: '********'//3.0现金理财公告组
                },
                result: {}
            },
            {
                name: 'cashV3Module',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {},
                result: {}
            }
        ]
    },


    //条件检查测试
    // testCheck: {
    //     id: '',
    //     name: 'testCheck',
    //     modules: [
    //         {
    //             name: 'conditionCheckModule',
    //             setting: {
    //                 isOpenNewPage: false,
    //                 isCache: false
    //             },
    //             param: {
    //                 UniqueKey: "ACU_001",
    //                 "business_type": "30021",
    //                 "client_id": "($usercode)",
    //                 "password": "($password)",
    //                 "op_station": "($op_station)"
    //             },
    //             result: {}
    //         }
    //     ]
    // },

    //创业板转签查询
    // startUpBoardQueryForWeb: {
    //     id: '',
    //     name: 'startUpBoardQuery',
    //     modules: [
    //         {
    //             name: 'noticeModule',
    //             setting: {
    //                 isOpenNewPage: false,
    //                 isCache: false
    //             },
    //             param: {
    //                 UniqueKey: 'ANNC_1004',
    //                 type: '1002',
    //                 subtype: '********'
    //             }
    //         },
    //         {
    //             name: 'startUpBoardQueryModule',
    //             setting: {
    //                 isOpenNewPage: false,
    //                 isCache: false
    //             },
    //             param: {
    //                 UniqueKey: 'CWCNI',
    //                 client_id: '($usercode)',
    //                 fund_account: '($fund_account)',
    //                 password: '($password)'
    //             },
    //             result: {}
    //         }
    //     ]
    // },

    //港股通查询
    ggtQueryForWeb: {
        id: '',
        name: 'ggtQuery',
        param: {},
        modules: [
            {
                name: 'noticeModule',
                setting: {
                    isOpenNewPage: false,
                    isCache: false
                },
                param: {
                    UniqueKey: 'ANNC_1004',
                    type: '1002',
                    subtype: '********'
                },
                result: {}
            },
            {
                name: 'ggtQueryModule',
                setting: {
                    isOpenNewPage: false,
                    isCache: false
                },
                param: {
                    UniqueKey: 'CWHQS',
                    client_id: '($usercode)',
                    fund_account: '($fund_account)',
                    password: '($password)'
                },
                result: {}
            }
        ]
    },

    //身份更新
    idupdateForWeb: {
        id: '',
        name: 'idupdate',
        modules: [
            {
                name: 'noticeModule',
                setting: {
                    isOpenNewPage: false,
                    isCache: false
                },
                param: {
                    UniqueKey: 'ANNC_1004',
                    type: '1002',
                    subtype: '********'
                }
            },
            {
                name: 'idupdatewebModule',
                setting: {
                    isOpenNewPage: false,
                    isCache: false
                },
                param: {
                    clientId: '($usercode)',
                    fundAccount: '($fund_account)',
                    password: '($password)'
                }
            }
        ]
    },
    //A股账户补开户--展示
    "stockAForWeb": {
        "id": "",
        "name": "stockAForWeb",
        "modules": [
            {
                "name": "noticeModule",
                setting: {
                    isOpenNewPage: false,
                    isCache: false
                },
                "param": {
                    UniqueKey: 'ANNC_1004',
                    type: '1002',
                    subtype: '********'
                },
                "result": {}
            },
            {
                "name": "stockAModule",
                setting: {
                    isOpenNewPage: false,
                    isCache: false
                },
                "param": {},
                "result": {}
            }]
    },
    //指定交易状态查询
    "specifiedForWeb": {
        "id": "",
        "name": "specifiedForWeb",
        "modules": [
            {
                "name": "noticeModule",
                setting: {
                    isOpenNewPage: false,
                    isCache: false
                },
                "param": {
                    "UniqueKey": "ANNC_1004",
                    "type": "1002",
                    "subtype": "********"
                }
            },
            {
                "name": "specifiedModule",
                setting: {
                    isOpenNewPage: false,
                    isCache: false
                },
                "param": {
                    "UniqueKey": "SNS_001",
                    client_id: '($usercode)',
                    fund_account: '($fund_account)',
                    password: '($password)',
                    op_entrust_way: '7',
                    op_station: "($op_station)"
                }
            }]
    },
    //机构户提示
    "orgTip": {
        id: '',
        name: 'orgTip',
        param: {},
        modules: [
            {
                name: 'simpleTipModule',
                setting: {
                    isOpenNewPage: true,//由上个页面弹出
                    isCache: false
                },
                param: {
                    content: "本业务目前仅支持自然人账户线上办理。其他账户类型的客户，请前往就近营业部临柜办理。您可以联系客户经理或拨打客服热线咨询相关问题。感谢您的支持！",
                    btnName: "查找营业部",
                    btnValue: 'findDepartment',
                },
                result: {}
            }
        ]
    },
    //电子协议查询业务对应机构户提示
    "orgHint": {
        id: '',
        name: 'orgTip',
        param: {},
        modules: [
            {
                name: 'simpleHintModule',
                setting: {
                    isOpenNewPage: true,//由上个页面弹出
                    isCache: false
                },
                param: {
                    content: "本业务目前仅支持自然人账户线上办理。其他账户类型的客户，请前往就近营业部办理。您可以联系客户经理或拨打客服热线咨询相关问题。感谢您的支持！"
                },
                result: {}
            }
        ]
    },
    //开户
    "openStock": {
        id: '',
        name: 'openStock',
        param: {},
        modules: [
            {
                name: 'openStockModule',
                setting: {
                    isOpenNewPage: false,//由上个页面弹出
                    isCache: false
                },
                param: {},
                result: {}
            }
        ]
    },
    /**
     * 债券逆回购查询
     */
    reverseReposQuery: {
        id: '',
        name: 'reverseReposQuery',
        modules: [
            {
                name: 'noticeModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    UniqueKey: 'ANNC_1004',
                    type: '1002',
                    subtype: '********'//todo
                }
            },
            {
                name: 'reverseReposQueryModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    UniqueKey: 'CRRQ',//reverseRepos query
                    clientId: '($usercode)',
                    fund_account: '($fund_account)',
                    password: '($password)',
                },
                result: {}
            }
        ]
    },
    //债券逆回购开通
    reverseReposOpen: {
        id: '',
        name: 'reverseReposOpen',
        modules: [
            {
                name: 'conditionCheckModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    UniqueKey: "ACU_001",
                    "business_type": "30043",//todo reverseRepos busdines stype
                    "client_id": "($usercode)",
                    "password": "($password)",
                    "op_station": "($op_station)",
                    "pageTitle": "签署债券回购协议"//todo PAGE TITLE

                },
                result: {}
            },
            {
                name: 'signPactModule',
                setting: {
                    isOpenNewPage: false,
                    isCache: false
                },
                "param": {
                    type: 'reverseRepos'
                }//todo reverseRepos协议号
            },
            {
                name: 'reverseReposRetModule',
                setting: {
                    isOpenNewPage: false,
                    isCache: false
                },
                param: {
                    type: 1, //todo 1 开通  0 注销
                    UniqueKey: "CRRO",//todo reverseRepos open
                    client_id: '($usercode)',
                    fund_account: '($fund_account)',
                    password: "($password)",
                    op_station: "($op_station)",
                    branch_no: '($khbranch)',
                }
            }
        ]
    },
    //reverseRepos注销
    reverseReposCancellation: {
        id: '',
        name: 'reverseReposCancellation',
        modules: [
            {
                name: "conditionCheckModule",
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    UniqueKey: "ACU_001",
                    "business_type": "30044",//todo reverseRepos close
                    "client_id": "($usercode)",
                    "password": "($password)",
                    "op_station": "($op_station)",
                    "pageTitle": "签署债券回购协议"

                },
                result: {}
            },
            {
                name: 'reverseReposRetModule',
                setting: {
                    isOpenNewPage: false,
                    isCache: false
                },
                param: {
                    type: 0, // 0 开通  1 注销
                    UniqueKey: "CRRC",
                    client_id: '($usercode)',
                    fund_account: '($fund_account)',
                    password: "($password)",
                    op_station: "($op_station)",
                    branch_no: '($khbranch)',
                }
            }
        ]
    },
    //找回资金账号-首页
    goFindAccount: {
        id: '',
        name: 'goFindAccount',
        modules: [
            {
                name: 'goFindAccountModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {},
                result: {}
            }
        ]
    },
    //重置密码-首页
    goClearPwd: {
        id: '',
        name: 'goClearPwd',
        modules: [
            {
                name: 'goClearPwdModule',
                setting: {
                    isOpenNewPage: false,
                    isCache: false
                },
                param: {},
                result: {}
            }
        ]
    },

    /**
     * 沪伦通查询
     */
    londonCdrQuery: {
        id: '',
        name: 'londonCdrQuery',
        modules: [
            {
                name: 'noticeModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    UniqueKey: 'ANNC_1004',
                    type: '1002',
                    subtype: '********'//todo
                }
            },
            {
                name: 'londonCdrQueryModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    UniqueKey: 'ALCQ',//londonCdr query
                    clientId: '($usercode)',
                    fund_account: '($fund_account)',
                    password: '($password)',
                },
                result: {}
            }
        ]
    },
    //沪伦通开通
    londonCdrOpen: {
        id: '',
        name: 'londonCdrOpen',
        modules: [
            {
                name: 'conditionCheckModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    UniqueKey: "ACU_001",
                    "business_type": "30045",//todo londonCdr busdines stype
                    "client_id": "($usercode)",
                    "password": "($password)",
                    "op_station": "($op_station)",
                    "pageTitle": "沪伦通"//todo PAGE TITLE

                },
                result: {}
            },
            {
                //适当性
                name: 'suitablyModule',
                setting: {
                    isOpenNewPage: false,
                    isCache: false
                },
                param: {}
            },
            {
                name: 'needQuesListModule',
                setting: {
                    isOpenNewPage: false,
                    isCache: false
                },
                param: {
                    UniqueKey: 'CWEHP',
                    client_id: '($usercode)',
                    password: '($password)'
                }
            },
            {
                name: 'quesListModule',
                setting: {
                    isOpenNewPage: false,
                    isCache: false
                },
                param: {
                    clientId: '($usercode)',
                    fundAccount: '($fund_account)',
                    password: '($password)',
                    number: '试卷题号',
                    type: '试卷类型'
                }
            },
            {
                name: 'signPactModule',
                setting: {
                    isOpenNewPage: false,
                    isCache: false
                },
                "param": {
                    type: 'londonCdr'
                }
            },
            {
                name: 'londonCdrRetModule',
                setting: {
                    isOpenNewPage: false,
                    isCache: false
                },
                param: {
                    type: 1, // 1 开通  0 注销
                    UniqueKey: "ALCO",
                    client_id: '($usercode)',
                    fund_account: '($fund_account)',
                    password: "($password)",
                    op_station: "($op_station)",
                    branch_no: '($khbranch)',
                }
            }
        ]
    },
    //沪伦通注销
    londonCdrCancellation: {
        id: '',
        name: 'londonCdrCancellation',
        modules: [
            {
                name: "conditionCheckModule",
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    UniqueKey: "ACU_001",
                    "business_type": "30046",
                    "client_id": "($usercode)",
                    "password": "($password)",
                    "op_station": "($op_station)",
                    "pageTitle": "沪伦通"

                },
                result: {}
            },
            {
                name: 'londonCdrRetModule',
                setting: {
                    isOpenNewPage: false,
                    isCache: false
                },
                param: {
                    type: 0, // 0 开通  1 注销
                    UniqueKey: "ALCC",
                    client_id: '($usercode)',
                    fund_account: '($fund_account)',
                    password: "($password)",
                    op_station: "($op_station)",
                    branch_no: '($khbranch)',
                }
            }
        ]
    },
    reservationList: {
        id: '',
        name: 'reservationList',
        modules: [
            {
                name: 'reservationListModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {}
            }
        ]
    },
    accountCancellation: {
        id: '',
        name: 'accountCancellation',
        modules: [
            {
                name: 'noticeModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    UniqueKey: 'ANNC_1004',
                    type: '1002',
                    subtype: '********'
                }
            },
            {
                name: 'accountCancellationModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                },
                result: {}
            },
        ]
    },

    accountCancellations: {
        id: '',
        name: 'accountCancellations',
        modules: [
            {
                name: 'noticeModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    UniqueKey: 'ANNC_1004',
                    type: '1002',
                    subtype: '********'
                }
            },
            {
                name: 'accountCancellationModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                },
                result: {}
            },
        ]
    },

    accountCancellationPhoneVerify: {
        id: '',
        name: 'accountCancellationPhoneVerify',
        modules: [
            {
                name: 'noticeModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    UniqueKey: 'ANNC_1004',
                    type: '1002',
                    subtype: '********'
                }
            },
            {
                name: 'conditionCheckModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    UniqueKey: "ACU_001",
                    "business_type": "30047",
                    "client_id": "($usercode)",
                    "password": "($password)",
                    "op_station": "($op_station)",
                    "pageTitle": "预约转销户"

                },
                result: {}
            },
            {
                name: 'phoneVerifyModule',//手机号码短信验证模块
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    template_no: '14',
                    pageTitle: '预约转销户',
                    loggerCollectConfig: {
                        event_id_label: 'sms_validate',
                        logtype: 'cancel_sms_validate'
                    },
                    nextStepType: 'accountCancelationKeepOn',
                },
                result: {}
            },
        ]
    },
    accountCancellationQuestionnaire: {
        id: '',
        name: 'accountCancellationQuestionnaire',
        modules: [
            {
                name: 'noticeModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    UniqueKey: 'ANNC_1004',
                    type: '1002',
                    subtype: '********'
                }
            },
            {
                name: 'conditionCheckModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    UniqueKey: "ACU_001",
                    "business_type": "30047",
                    "client_id": "($usercode)",
                    "password": "($password)",
                    "op_station": "($op_station)",
                    "pageTitle": "预约转销户"

                },
                result: {}
            },
            {
                name: 'accountCancellationQuestionnaireModule',//销户问卷
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {},
                result: {}
            },
            {
                name: 'accountCancellationApplyRetModule',//提交预约结果页
                setting: {
                    isOpenNewPage: false,
                    isCache: false
                },
                param: {},
                result: {}
            }


        ]
    },
    accountCancellationQuery: {
        id: '',
        name: 'accountCancellationQuery',
        modules: [
            {
                name: 'noticeModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    UniqueKey: 'ANNC_1004',
                    type: '1002',
                    subtype: '********'
                }
            },
            {
                name: 'accountCancellationQueryModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {},
                result: {}
            },
        ]
    },
    accountCancellationInfoConfirm: {
        id: '',
        name: 'accountCancellationInfoConfirm',
        modules: [
            {
                name: 'noticeModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    UniqueKey: 'ANNC_1004',
                    type: '1002',
                    subtype: '********'
                }
            },
            {
                name: 'conditionCheckModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    UniqueKey: "ACU_001",
                    "business_type": "30048",
                    "client_id": "($usercode)",
                    "password": "($password)",
                    "op_station": "($op_station)",
                    "pageTitle": "预约转销户"

                },
                result: {}
            },
            {
                name: 'accountCancellationInfoModule',//销户内容展示
                setting: {
                    isOpenNewPage: false,
                    isCache: false
                },
                param: {},
                result: {}
            }
        ]
    },
    accountCancellationUploadId: {
        id: '',
        name: 'accountCancellationUploadId',
        modules: [
            {
                name: 'noticeModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    UniqueKey: 'ANNC_1004',
                    type: '1002',
                    subtype: '********'
                }
            },
            {
                name: 'conditionCheckModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    UniqueKey: "ACU_001",
                    "business_type": "30048",
                    "client_id": "($usercode)",
                    "password": "($password)",
                    "op_station": "($op_station)",
                    "pageTitle": "预约转销户"

                },
                result: {}
            },
            {
                name: 'accountCancellationUploadIdModule',
                setting: {
                    isOpenNewPage: false,
                    isCache: false
                },
                param: {},
                result: {}
            }
        ]
    },
    accountCancellationUploadSupplement: {
        id: '',
        name: 'accountCancellationInfoConfirm',
        modules: [
            {
                name: 'noticeModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    UniqueKey: 'ANNC_1004',
                    type: '1002',
                    subtype: '********'
                }
            },
            {
                name: 'conditionCheckModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    UniqueKey: "ACU_001",
                    "business_type": "30048",
                    "client_id": "($usercode)",
                    "password": "($password)",
                    "op_station": "($op_station)",
                    "pageTitle": "预约转销户"

                },
                result: {}
            },
            {
                name: 'accountCancellationUploadSupplementModule',
                setting: {
                    isOpenNewPage: false,
                    isCache: false
                },
                param: {},
                result: {}
            }
        ]
    },
    accountCancellationVideo: {
        id: '',
        name: 'accountCancellationInfoConfirm',
        modules: [
            {
                name: 'noticeModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    UniqueKey: 'ANNC_1004',
                    type: '1002',
                    subtype: '********'
                }
            },
            {
                name: 'conditionCheckModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    UniqueKey: "ACU_001",
                    "business_type": "30048",
                    "client_id": "($usercode)",
                    "password": "($password)",
                    "op_station": "($op_station)",
                    "pageTitle": "预约转销户"

                },
                result: {}
            },
            {
                name: 'accountCancellationVideoModule',
                setting: {
                    isOpenNewPage: false,
                    isCache: false
                },
                param: {},
                result: {}
            }
        ]
    },
    "yjbApp": {
        id: '',
        name: 'yjbApp',
        param: {},
        modules: [
            {
                name: 'yjbAppModule',
                setting: {
                    isOpenNewPage: false,
                    isCache: false
                },
                param: {},
                result: {}
            }
        ]
    },
    "londonCdrComingSoon": {
        id: '',
        name: 'londonCdrComingSoon',
        modules: [
            {
                name: 'comingSoonModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    content: "",
                    btnName: "",
                    btnValue: ''
                },
                result: {}
            }
        ]
    },
    reservationListForWeb: {
        id: '',
        name: 'reservationListForWeb',
        modules: [
            {
                name: 'reservationListModule',
                setting: {
                    isOpenNewPage: false,
                    isCache: false
                },
                param: {}
            }
        ]
    },
    //科创板查询
    stibQuery: {
        id: '',
        name: 'stibQuery',
        modules: [
            {
                name: 'noticeModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    UniqueKey: 'ANNC_1004',
                    type: '1002',
                    subtype: '********'
                }
            },
            {
                name: 'stibQueryModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    UniqueKey: 'ASRQ',//stib query todo
                    clientId: '($usercode)',
                    fund_account: '($fund_account)',
                    password: '($password)',
                },
                result: {}
            }
        ]
    },
    //科创板开通
    stibOpen: {
        id: '',
        name: 'stibOpen',
        modules: [
            {
                name: 'noticeModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    UniqueKey: 'ANNC_1004',
                    type: '1002',
                    subtype: '********'
                }
            },
            {
                name: 'conditionCheckModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    UniqueKey: "ACU_001",
                    "business_type": "30050",//
                    "client_id": "($usercode)",
                    "password": "($password)",
                    "op_station": "($op_station)",
                    "pageTitle": "科创板"//

                },
                result: {}
            },
            {
                name: 'additionalConditionCheckModule',
                setting: {
                    isOpenNewPage: false,
                    isCache: false
                },
                param: {
                    UniqueKey: "ACU_001",
                    "business_type": "30057",
                    "client_id": "($usercode)",
                    "password": "($password)",
                    "op_station": "($op_station)",
                    "pageTitle": "科创板"
                },
                result: {}
            },
            {
                name: 'phoneVerifyModule',
                setting: {
                    isOpenNewPage: false,
                    isCache: false
                },
                param: {
                    template_no: '21',
                    pageTitle: '科创板',
                    loggerCollectConfig: {
                        event_id_label: 'sms_validate',
                        logtype: 'cancel_sms_validate'
                    },
                },
                result: {}
            },
            {
                //适当性
                name: 'prefSuitablyModule',
                setting: {
                    isOpenNewPage: false,
                    isCache: false
                },
                param: {
                    UniqueKey: 'CWCPQ',
                    client_id: '($usercode)',
                    fund_account: '($fund_account)',
                    branch_no: '($khbranch)',
                    op_station: "($op_station)",
                    op_entrust_way: '7',
                    password: '($password)',
                    password_type: '2',
                }
            },
            {
                name: 'needQuesListTodoModule',
                setting: {
                    isOpenNewPage: false,
                    isCache: false
                },
                param: {
                    UniqueKey: 'QHQQ',
                    questionnaire_id: '***********',
                    user_account: '($usercode)',
                    account_type: '4'
                }
            },
            {
                name: 'quesListModule',
                setting: {
                    isOpenNewPage: false,
                    isCache: false
                },
                param: {
                    clientId: '($usercode)',
                    fundAccount: '($fund_account)',
                    password: '($password)',
                    number: '试卷题号',
                    type: '试卷类型'
                }
            },
            {
                name: 'signPactModule',
                setting: {
                    isOpenNewPage: false,
                    isCache: false
                },
                "param": {
                    type: 'stib'
                }
            },
            {
                name: 'stibRetModule',
                setting: {
                    isOpenNewPage: false,
                    isCache: false
                },
                param: {
                    type: 1, // 1 开通  0 注销
                    UniqueKey: "ASRO",
                    client_id: '($usercode)',
                    fund_account: '($fund_account)',
                    asset_prop: '0',
                    password: "($password)",
                    op_station: "($op_station)",
                    branch_no: '($khbranch)',
                }
            }
        ]
    },
    //信用科创板查询
    creditStibQuery: {
        id: '',
        name: 'creditStibQuery',
        modules: [
            {
                name: 'noticeModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    UniqueKey: 'ANNC_1004',
                    type: '1002',
                    subtype: '********'
                }
            },
            {
                name: 'creditStibQueryModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    UniqueKey: 'ASRQ',//stib query todo
                    clientId: '($usercode)',
                    fund_account: '($credit_fund_account)',
                    password: '($password)',
                },
                result: {}
            }
        ]
    },
    creditStibQueryForWeb: {
        id: '',
        name: 'creditStibQuery',
        modules: [
            {
                name: 'noticeModule',
                setting: {
                    isOpenNewPage: false,
                    isCache: false
                },
                param: {
                    UniqueKey: 'ANNC_1004',
                    type: '1002',
                    subtype: '********'
                }
            },
            {
                name: 'creditStibQueryModule',
                setting: {
                    isOpenNewPage: false,
                    isCache: false
                },
                param: {
                    UniqueKey: 'ASRQ',//stib query todo
                    clientId: '($usercode)',
                    fund_account: '($credit_fund_account)',
                    password: '($password)',
                },
                result: {}
            }
        ]
    },
    //信用科创板开通
    creditStibOpen: {
        id: '',
        name: 'creditStibOpen',
        modules: [
            {
                name: 'noticeModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    UniqueKey: 'ANNC_1004',
                    type: '1002',
                    subtype: '********'
                }
            },
            {
                name: 'conditionCheckModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    UniqueKey: "ACU_001",
                    "business_type": "30054",//
                    "client_id": "($usercode)",
                    "password": "($password)",
                    "op_station": "($op_station)",
                    "pageTitle": "信用科创板"//

                },
                result: {}
            },
            {
                name: 'additionalConditionCheckModule',
                setting: {
                    isOpenNewPage: false,
                    isCache: false
                },
                param: {
                    UniqueKey: "ACU_001",
                    "business_type": "30058",
                    "client_id": "($usercode)",
                    "password": "($password)",
                    "op_station": "($op_station)",
                    "pageTitle": "信用科创板"
                },
                result: {}
            },
            {
                name: 'phoneVerifyModule',
                setting: {
                    isOpenNewPage: false,
                    isCache: false
                },
                param: {
                    template_no: '22',
                    pageTitle: '信用科创板',
                    loggerCollectConfig: {
                        event_id_label: 'sms_validate',
                        logtype: 'cancel_sms_validate'
                    },
                },
                result: {}
            },
            {
                //适当性
                name: 'prefSuitablyModule',
                setting: {
                    isOpenNewPage: false,
                    isCache: false
                },
                param: {
                    UniqueKey: 'CWCPQ',
                    client_id: '($usercode)',
                    fund_account: '($fund_account)',
                    branch_no: '($khbranch)',
                    op_station: "($op_station)",
                    op_entrust_way: '7',
                    password: '($password)',
                    password_type: '2',
                }
            },
            {
                name: 'needQuesListTodoModule',
                setting: {
                    isOpenNewPage: false,
                    isCache: false
                },
                param: {
                    UniqueKey: 'QHQQ',
                    questionnaire_id: '***********',
                    user_account: '($usercode)',
                    account_type: '4'
                }
            },
            {
                name: 'quesListModule',
                setting: {
                    isOpenNewPage: false,
                    isCache: false
                },
                param: {
                    clientId: '($usercode)',
                    fundAccount: '($credit_fund_account)',
                    password: '($password)',
                    number: '试卷题号',
                    type: '试卷类型'
                }
            },
            {
                name: 'signPactModule',
                setting: {
                    isOpenNewPage: false,
                    isCache: false
                },
                "param": {
                    type: 'creditstib'
                }
            },
            {
                name: 'creditStibRetModule',
                setting: {
                    isOpenNewPage: false,
                    isCache: false
                },
                param: {
                    type: 1, // 1 开通  0 注销
                    UniqueKey: "ASRO",
                    client_id: '($usercode)',
                    fund_account: '($credit_fund_account)',
                    asset_prop: '7',
                    password: "($password)",
                    op_station: "($op_station)",
                    branch_no: '($khbranch)',
                }
            }
        ]
    },
    stibQueryForWeb: {
        id: '',
        name: 'stibQuery',
        modules: [
            {
                name: 'noticeModule',
                setting: {
                    isOpenNewPage: false,
                    isCache: false
                },
                param: {
                    UniqueKey: 'ANNC_1004',
                    type: '1002',
                    subtype: '********'
                }
            },
            {
                name: 'stibQueryModule',
                setting: {
                    isOpenNewPage: false,
                    isCache: false
                },
                param: {
                    UniqueKey: 'ASRQ',//stib query todo
                    clientId: '($usercode)',
                    fund_account: '($fund_account)',
                    password: '($password)',
                },
                result: {}
            }
        ]
    },
    fundSignUpForWeb: {
        id: '',
        name: 'fundSignUp',
        modules: [

            {
                name: 'fundSignUpModule',
                setting: {
                    isOpenNewPage: false,
                    isCache: false
                },
                param: {
                },
                result: {}
            }
        ]
    },
    //封闭式基金
    "closedEndFund": {
        "id": "",
        "name": "closedEndFund",
        "modules": [
            {
                "name": "noticeModule",
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                "param": {
                    UniqueKey: 'ANNC_1004',
                    type: '1002',
                    subtype: '********'//todo
                },
                "result": {}
            },
            {
                "name": "closedEndFundModule",
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                "param": {},
                "result": {}
            }]
    },
    //封闭式基金--开通
    "closedEndFundOpen": {
        "id": "",
        "name": "closedEndFundOpen",
        "modules": [
            {
                "name": "conditionCheckModule",
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                "param": {
                    UniqueKey: "ACU_001",
                    "business_type": "30053",//TODO
                    "client_id": "($usercode)",
                    "password": "($password)",
                    "op_station": "($op_station)",
                    "pageTitle": "封闭式基金账户"
                },
                "result": {}
            },
            {
                "name": "closedEndFundOpenModule",
                setting: {
                    isOpenNewPage: false,
                    isCache: false
                },
                "param": {},
                "result": {}
            },
            {
                name: 'closedEndFundRetModule',
                setting: {
                    isOpenNewPage: false,//todo
                    isCache: false
                },
                param: {},
                result: {}
            }
        ]
    },
    //电子协议查询
    contractQuery: {
        id: '',
        name: 'contractQuery',
        modules: [
            {
                name: 'noticeModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    UniqueKey: 'ANNC_1004',
                    type: '1002',
                    subtype: '10020028'
                }
            },
            {
                name: 'contractQueryModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                },
                result: {}
            },
        ]
    },
    contractInfo: {
        id: '',
        name: 'contractInfo',
        modules: [
            {
                name: 'contractInfoModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                },
                result: {}
            }

        ]
    },
    contractFileView: {
        id: '',
        name: 'contractFileView',
        modules: [
            {
                name: 'contractFileViewModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                },
                result: {}
            }

        ]
    },
    contractFileThirdView: {
        id: '',
        name: 'contractFileThirdView',
        modules: [
            {
                name: 'contractFileThirdViewModule',
                setting: {
                    isOpenNewPage: false,
                    isCache: false
                },
                param: {
                },
                result: {}
            }

        ]
    },
    contractContentQuery: {
        id: '',
        name: 'contractContentQuery',
        modules: [
            {
                name: 'contractContentModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                },
                result: {}
            }

        ]
    },
    // 财人汇两融预约开户双向视频
    videoWitness: {
        id: '',
        name: 'videoWitness',
        modules: [
            {
                name: "conditionCheckModule",
                setting: {
                    isOpenNewPage: false,
                    isCache: false
                },
                param: {
                    UniqueKey: "ACU_001",
                    business_type: "30112",
                    client_id: "($usercode)",
                    password: "($password)",
                    op_station: "($op_station)",
                    pageTitle: "视频见证"
                },
                result: {}
            },
            {
                name: 'queryToDoInfoModule',
                setting: {
                    isOpenNewPage: false,
                    isCache: false
                },
                param: {
                    UniqueKey: 'RESERVE_03',
                    todoId: ''
                },
                result: {}
            }
        ]
    },
    // 财人汇两融全线上开户双向视频
    videoWitnessCRHqxs: {
        id: '',
        name: 'videoWitnessCRHqxs',
        modules: [
            {
                name: "conditionCheckModule",
                setting: {
                    isOpenNewPage: false,
                    isCache: false
                },
                param: {
                    UniqueKey: "ACU_001",
                    business_type: "30111",
                    client_id: "($usercode)",
                    password: "($password)",
                    op_station: "($op_station)",
                    pageTitle: "视频见证"
                },
                result: {}
            },
            {
                name: 'queryToDoInfoModule',
                setting: {
                    isOpenNewPage: false,
                    isCache: false
                },
                param: {
                    UniqueKey: 'RESERVE_03',
                    todoId: ''
                },
                result: {}
            }
        ]
    },
    contractInfoThirdView: {
        id: '',
        name: 'contractInfoThirdView',
        modules: [
            {
                name: 'contractInfoThirdModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                },
                result: {}
            }

        ]
    },
    /*预约业务/特殊视频业务*/
    reserveListModule: {
        id: '',
        name: 'reserveListModule',
        modules: [
            {
                name: 'noticeModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    UniqueKey: 'ANNC_1004',
                    type: '1002',
                    subtype: '10020031'
                }
            },
            {
                name: 'reserveListModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    UniqueKey: 'RESERVE_02',
                    user_id: '($usercode)',
                    user_type: 12,
                    todo_status: 0,
                    op_station: '',
                },
                result: {}
            }
        ]
    },
    reserveListModuleForWeb: {
        id: '',
        name: 'reserveListModule',
        modules: [
            {
                name: 'noticeModule',
                setting: {
                    isOpenNewPage: false,
                    isCache: false
                },
                param: {
                    UniqueKey: 'ANNC_1004',
                    type: '1002',
                    subtype: '10020031'
                }
            },
            {
                name: 'reserveListModule',
                setting: {
                    isOpenNewPage: false,
                    isCache: false
                },
                param: {
                    UniqueKey: 'RESERVE_02',
                    user_id: '($usercode)',
                    user_type: 12,
                    todo_status: 0,
                    op_station: '',
                },
                result: {}
            }
        ]
    },
    reserveUploadIdCardModule: {
        id: '',
        name: 'reserveUploadIdCardModule',
        modules: [
            {
                name: 'reserveUploadIdCardModule',
                setting: {
                    isOpenNewPage: false,
                    isCache: false
                },
                param: {
                },
                result: {}
            }
        ]
    },
    reserveUploadSupplementCardModule: {
        id: '',
        name: 'reserveUploadSupplementCardModule',
        modules: [
            {
                name: 'reserveUploadSupplementCardModule',
                setting: {
                    isOpenNewPage: false,
                    isCache: false
                },
                param: {
                },
                result: {}
            }
        ]
    },
    reserveUploadHeadimgModule: {
        id: '',
        name: 'reserveUploadHeadimgModule',
        modules: [
            {
                name: 'reserveUploadHeadimgModule',
                setting: {
                    isOpenNewPage: false,
                    isCache: false
                },
                param: {
                },
                result: {}
            }
        ]
    },
    normalUploadIdCardModule: {
        id: '',
        name: 'normalUploadIdCardModule',
        modules: [
            {
                name: 'normalUploadIdCardModule',
                setting: {
                    isOpenNewPage: false,
                    isCache: false
                },
                param: {
                },
                result: {}
            }
        ]
    },
    normalUploadSupplementCardModule: {
        id: '',
        name: 'normalUploadSupplementCardModule',
        modules: [
            {
                name: 'normalUploadSupplementCardModule',
                setting: {
                    isOpenNewPage: false,
                    isCache: false
                },
                param: {
                },
                result: {}
            }
        ]
    },
    idInfoEditModule: {
        id: '',
        name: 'idInfoEditModule',
        modules: [
            {
                name: 'idInfoEditModule',
                setting: {
                    isOpenNewPage: false,
                    isCache: false
                },
                param: {
                },
                result: {}
            }
        ]
    },
    reserveVideoWitnessModule: {
        id: '',
        name: 'reserveVideoWitnessModule',
        modules: [
            {
                name: 'reserveVideoWitnessModule',
                setting: {
                    isOpenNewPage: false,
                    isCache: false
                },
                param: {
                },
                result: {}
            }
        ]
    },
    reserveRetModule: {
        id: '',
        name: 'reserveRetModule',
        modules: [
            {
                name: 'reserveRetModule',
                setting: {
                    isOpenNewPage: false,
                    isCache: false
                },
                param: {
                },
                result: {}
            }
        ]
    },
    /*产品购买双录列表*/
    doubleRecordList: {
        id: '',
        name: 'doubleRecordList',
        modules: [
            {
                name: 'noticeModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    UniqueKey: 'ANNC_1004',
                    type: '1002',
                    subtype: '********'
                }
            },
            {
                name: 'doubleRecordListModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    UniqueKey: 'DRQL',
                    fund_account: '($fund_account)',
                },
                result: {}
            }
        ]
    },
    doubleRecordListForWeb: {
        id: '',
        name: 'doubleRecordList',
        modules: [
            {
                name: 'noticeModule',
                setting: {
                    isOpenNewPage: false,
                    isCache: false
                },
                param: {
                    UniqueKey: 'ANNC_1004',
                    type: '1002',
                    subtype: '********'
                }
            },
            {
                name: 'preHandlerForWebModule',//机构户校验
                setting: {
                    isOpenNewPage: false,
                    isCache: false,
                    pageTitle:'产品购买双录',
                },
                param: {
                    needPersonal: true,
                    needCredit: false
                },
                result: {}
            },
            {
                name: 'doubleRecordListModule',
                setting: {
                    isOpenNewPage: false,
                    isCache: false
                },
                param: {
                    UniqueKey: 'DRQL',
                    fund_account: '($fund_account)',
                },
                result: {}
            }
        ]
    },

    /*产品购买双录历史*/
    doubleRecordHistory: {
        id: '',
        name: 'doubleRecordHistory',
        modules: [
            {
                name: 'doubleRecordHistoryModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    UniqueKey: 'HSDRQL',
                    fund_account: '($fund_account)'
                },
                result: {}
            }
        ]
    },
    doubleRecordRetModule: {
        id: '',
        name: 'doubleRecordRetModule',
        modules: [
            {
                name: 'doubleRecordRetModule',
                setting: {
                    isOpenNewPage: false,
                    isCache: false
                },
                param: {
                },
                result: {}
            }
        ]
    },
    // 老双录具体流程
    doubleRecordForWeb: {
        id: '',
        name: 'doubleRecordForWeb',
        modules: [
            {
                name: 'noticeModule',
                setting: {
                    isOpenNewPage: false,
                    isCache: false
                },
                param: {
                    UniqueKey: 'ANNC_1004',
                    type: '1002',
                    subtype: '********'
                }
            },
            {
                name: 'preHandlerForWebModule',//机构户校验
                setting: {
                    isOpenNewPage: false,
                    isCache: false
                },
                param: {
                    needPersonal: true,
                    needCredit: false,
                    pageTitle:'产品购买双录',
                },
                result: {}
            },
            {
                name: 'reserveListModule',
                setting: {
                    isOpenNewPage: false,
                    isCache: false
                },
                param: {
                    UniqueKey: 'RESERVE_02',
                    user_id: '($usercode)',
                    user_type: 12,
                    todo_status: 0,
                    op_station: '',
                },
                result: {}
            }
        ]
    },
    // 新双录具体流程
    // doubleRecordForWeb: {
    //     id: '',
    //     name: 'doubleRecordForWeb',
    //     modules: [
    //         {
    //             name: 'noticeModule',
    //             setting: {
    //                 isOpenNewPage: false,
    //                 isCache: false
    //             },
    //             param: {
    //                 UniqueKey: 'ANNC_1004',
    //                 type: '1002',
    //                 subtype: '********'
    //             }
    //         },
    //         {
    //             name: 'preHandlerForWebModule',//机构户校验
    //             setting: {
    //                 isOpenNewPage: false,
    //                 isCache: false
    //             },
    //             param: {
    //                 needPersonal: true,
    //                 needCredit: false,
    //                 pageTitle:'产品购买双录',
    //             },
    //             result: {}
    //         },
    //         {
    //             name: 'doubleRecordModule',
    //             setting: {
    //                 isOpenNewPage: false,
    //                 isCache: false
    //             },
    //             param: { },
    //             result: {}
    //         }
    //     ]
    // },
    investorList: {
        id: '',
        name: 'investorList',
        modules: [
            {
                name: 'noticeModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    UniqueKey: 'ANNC_1004',
                    type: '1002',
                    subtype: '********'
                }
            },
            {
                name: 'investorListModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    UniqueKey: 'IVQL',
                    branch_no: '($khbranch)',
                    client_id: '($usercode)',
                    fund_account: '($fund_account)',
                    password: '($password)',
                    op_entrust_way: '',
                    op_station: '($op_station)'
                },
                result: {}
            }
        ]
    },
    investorListForWeb: {
        id: '',
        name: 'investorList',
        modules: [
            {
                name: 'noticeModule',
                setting: {
                    isOpenNewPage: false,
                    isCache: false
                },
                param: {
                    UniqueKey: 'ANNC_1004',
                    type: '1002',
                    subtype: '********'
                }
            },
            {
                name: 'preHandlerForWebModule',//机构户校验
                setting: {
                    isOpenNewPage: false,
                    isCache: false
                },
                param: {
                    needPersonal: true,
                    needCredit: false,
                    pageTitle:'合格投资者认证',
                },
                result: {}
            },
            {
                name: 'investorListModule',
                setting: {
                    isOpenNewPage: false,
                    isCache: false
                },
                param: {
                    UniqueKey: 'IVQL',
                    branch_no: '($khbranch)',
                    client_id: '($usercode)',
                    fund_account: '($fund_account)',
                    password: '($password)',
                    op_entrust_way: '',
                    op_station: '($op_station)'
                },
                result: {}
            }
        ]
    },

    investorIntroModule: {
        id: '',
        name: 'investorIntroModule',
        modules: [
            {
                name: 'investorIntroModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                },
                result: {}
            }
        ]
    },
    investorEligibleOpen: { // 资管合格投资者认证
        id: '',
        name: 'investorEligibleOpen',
        modules: [
            {
                name: "conditionCheckModule",
                setting: {
                    isOpenNewPage: false,
                    isCache: false
                },
                param: {
                    UniqueKey: "ACU_001",
                    business_type: "30062",
                    client_id: "($usercode)",
                    password: "($password)",
                    op_station: "($op_station)",
                    pageTitle: "资管/信托合格投资者认证"
                },
                result: {}
            },
            {
                name: 'createPreEIBusinessInfoModule',
                setting: {
                    isOpenNewPage: false,
                    isCache: false
                },
                param: {
                    UniqueKey: 'CPEBI',
                    clientId: '($usercode)',
                    businessType: '30062',
                    fundAccount: '($fund_account)',
                    checkBusinessType: '30068',
                    password: '($password)',
                    op_station: "($op_station)",
                    opEntrustWay: '7',
                    userType: '12',
                    userId: '($usercode)',
                    clientName: '($client_name)',
                    branch_no: '($khbranch)'
                },
                result: {}
            },
        ]
    },
    investorEligibleOpenForWeb: { // 资管合格投资者认证
        id: '',
        name: 'investorEligibleOpen',
        modules: [
            {
                name: 'preHandlerForWebModule',//机构户校验
                setting: {
                    isOpenNewPage: false,
                    isCache: false
                },
                param: {
                    needPersonal: true,
                    needCredit: false,
                    pageTitle:'资管/信托合格投资者认证',
                },
                result: {}
            },
            {
                name: "conditionCheckModule",
                setting: {
                    isOpenNewPage: false,
                    isCache: false
                },
                param: {
                    UniqueKey: "ACU_001",
                    business_type: "30062",
                    client_id: "($usercode)",
                    password: "($password)",
                    op_station: "($op_station)",
                    pageTitle: "资管/信托合格投资者认证"
                },
                result: {}
            },
            {
                name: 'createPreEIBusinessInfoModule',
                setting: {
                    isOpenNewPage: false,
                    isCache: false
                },
                param: {
                    UniqueKey: 'CPEBI',
                    clientId: '($usercode)',
                    businessType: '30062',
                    fundAccount: '($fund_account)',
                    checkBusinessType: '30068',
                    password: '($password)',
                    op_station: "($op_station)",
                    opEntrustWay: '7',
                    userType: '12',
                    userId: '($usercode)',
                    clientName: '($client_name)',
                    branch_no: '($khbranch)'
                },
                result: {}
            },
        ]
    },

    investorPlacementOpen: { // 私募合格投资者认证
        id: '',
        name: 'investorPlacementOpen',
        modules: [
            {
                name: "conditionCheckModule",
                setting: {
                    isOpenNewPage: false,
                    isCache: false
                },
                param: {
                    UniqueKey: "ACU_001",
                    business_type: "30064",
                    client_id: "($usercode)",
                    password: "($password)",
                    op_station: "($op_station)",
                    pageTitle: "私募合格投资者认证"
                },
                result: {}
            },
            {
                name: 'createPreEIBusinessInfoModule',
                setting: {
                    isOpenNewPage: false,
                    isCache: false
                },
                param: {
                    UniqueKey: 'CPEBI',
                    clientId: '($usercode)',
                    businessType: '30064',
                    fundAccount: '($fund_account)',
                    checkBusinessType: '30069',
                    password: '($password)',
                    op_station: "($op_station)",
                    opEntrustWay: '7',
                    userType: '12',
                    userId: '($usercode)',
                    clientName: '($client_name)',
                    branch_no: '($khbranch)'
                },
                result: {}
            },
        ]
    },
    investorPlacementOpenForWeb: { // 私募合格投资者认证
        id: '',
        name: 'investorPlacementOpen',
        modules: [
            {
                name: "conditionCheckModule",
                setting: {
                    isOpenNewPage: false,
                    isCache: false
                },
                param: {
                    UniqueKey: "ACU_001",
                    business_type: "30064",
                    client_id: "($usercode)",
                    password: "($password)",
                    op_station: "($op_station)",
                    pageTitle: "私募合格投资者认证"
                },
                result: {}
            },
            {
                name: 'preHandlerForWebModule',//机构户校验
                setting: {
                    isOpenNewPage: false,
                    isCache: false
                },
                param: {
                    needPersonal: true,
                    needCredit: false,
                    pageTitle:'私募合格投资者认证',
                },
                result: {}
            },
            {
                name: 'createPreEIBusinessInfoModule',
                setting: {
                    isOpenNewPage: false,
                    isCache: false
                },
                param: {
                    UniqueKey: 'CPEBI',
                    clientId: '($usercode)',
                    businessType: '30064',
                    fundAccount: '($fund_account)',
                    checkBusinessType: '30069',
                    password: '($password)',
                    op_station: "($op_station)",
                    opEntrustWay: '7',
                    userType: '12',
                    userId: '($usercode)',
                    clientName: '($client_name)',
                    branch_no: '($khbranch)'
                },
                result: {}
            },
        ]
    },

    investorPhoneVerifyModule: {
        id: '',
        name: 'investorPhoneVerifyModule',
        modules: [
            {
                name: 'investorPhoneVerifyModule',
                setting: {
                    isOpenNewPage: false,
                    isCache: false
                },
                param: {
                    template_no: '23',
                    pageTitle: '合格投资者认证',
                    loggerCollectConfig: {
                        event_id_label: 'sms_validate',
                        logtype: 'cancel_sms_validate'
                    },
                    //nextStepType: 'investorNextStep'
                },
                result: {}
            }
        ]
    },
    investorUploadPicModule: {
        id: '',
        name: 'investorUploadPicModule',
        modules: [
            {
                name: 'investorUploadPicModule',
                setting: {
                    isOpenNewPage: false,
                    isCache: false
                },
                param: {
                },
                result: {}
            }
        ]
    },
    investorContractModule: {
        id: '',
        name: 'investorContractModule',
        modules: [
            {
                name: 'investorContractModule',
                setting: {
                    isOpenNewPage: false,
                    isCache: false
                },
                param: {
                },
                result: {}
            }
        ]
    },
    investorOpenModule: {
        id: '',
        name: 'investorOpenModule',
        modules: [
            {
                name: 'investorOpenModule',
                setting: {
                    isOpenNewPage: false,
                    isCache: false
                },
                param: {
                    branch_no: '($khbranch)',
                    client_id: '($usercode)',
                    fund_account: '($fund_account)',
                    password: '($password)',
                    op_entrust_way: 7
                },
                result: {}
            }
        ]
    },
    investorRetModule: {
        id: '',
        name: 'investorRetModule',
        modules: [
            {
                name: 'investorRetModule',
                setting: {
                    isOpenNewPage: false,
                    isCache: false
                },
                param: {
                },
                result: {}
            }
        ]
    },
    //理财商城-高端理财
    highFinancial: {
        id: '',
        name: 'highFinancial',
        modules: [
            {
                name: 'noticeGroupModule',//理财商城-高端理财-公告组
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    UniqueKey: 'ANNC_1010',
                    type: '1003',
                    sub_type: '10030025'
                },
                result: {}
            },
            {
                name: 'highFinancialModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {},
                result: {}
            }
        ]
    },
    // 反洗钱-存量客户信息确认
    userInfoConfirm: {
        id: '',
        name: 'userInfoConfirm',
        modules: [
            {
                name: 'userInfoConfirmModule',
                setting: {
                    isOpenNewPage: false,
                    isCache: false
                },
                param: {},
                result: {}
            }
        ]
    },
    //股票期权账户-查询
    optionAccount: {
        id: '',
        name: 'optionAccount',
        modules: [
            {
                name: 'noticeModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    UniqueKey: 'ANNC_1004',
                    type: '1002',
                    subtype: '********'//股票期权账户
                }
            },
            {
                name: 'optionAccountModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    // UniqueKey: 'ANNC_1004',
                    // type: '1002',
                    // subtype: '********',
                    UniqueKey: 'ACSO_01',
                    clientId: '($usercode)',
                    fund_account: '($fund_account)',
                    password: '($password)',
                },
                result: {}
            }
        ]
    },
    //股票期权账户-无期权资金账号
    optionAccountHint: {
        id: '',
        name: 'optionAccountHint',
        modules: [
            {
                name: 'optionAccountHintModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {},
                result: {}
            }
        ]
    },

    //股票期权账户-无沪市期权
    optionAccountSHHint: {
        id: '',
        name: 'optionAccountSHHint',
        modules: [
            {
                name: 'optionAccountSHHintModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {},
                result: {}
            }
        ]
    },

    optionAccountSZOpen: {
        id: '',
        name: 'optionAccountSZOpen',
        modules: [
            {
                name: 'conditionCheckModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    UniqueKey: "ACU_001",
                    "business_type": "30075",//条件检查-深市期权开通  
                    "client_id": "($usercode)",
                    "password": "($password)",
                    "op_station": "($op_station)",
                    "pageTitle": "股票期权账户"

                },
                result: {}
            },
            {
                name: 'optionAccountConditionCheckModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    UniqueKey: "ACU_001",
                    "business_type": "30076",//补充条件检查-深市期权开通
                    "client_id": "($usercode)",
                    "password": "($password)",
                    "op_station": "($op_station)",
                    "pageTitle": "股票期权账户"
                },
                result: {}
            },
            {
                name: 'createOptionAccountOpenSZModule',
                setting: {
                    isOpenNewPage: false,
                    isCache: false
                },
                param: {
                    //UniqueKey: 'CPEBI',
                    UniqueKey: 'ACSO_02',
                    clientId: '($usercode)',
                    businessType: '30075',
                    fundAccount: '($fund_account)',
                    password: '($password)',
                    op_station: "($op_station)",
                    //opEntrustWay: '7',
                    userType: '12',
                    userId: '($usercode)',
                    clientName: '($client_name)',
                    //branch_no: '($khbranch)'
                },
                result: {}
            },
        ]
    },

    optionPhoneVerifyModule: {
        id: '',
        name: 'optionPhoneVerifyModule',
        modules: [
            {
                name: 'investorPhoneVerifyModule',
                setting: {
                    isOpenNewPage: false,
                    isCache: false
                },
                param: {
                    template_no: '24',
                    pageTitle: '股票期权账户',
                    loggerCollectConfig: {
                        event_id_label: 'sms_validate',
                        logtype: 'cancel_sms_validate'
                    },
                    //nextStepType: 'investorNextStep'
                },
                result: {}
            }
        ]
    },

    optionConfirmPermission: {
        id: '',
        name: 'optionConfirmPermission',
        modules: [
            {
                name: 'optionConfirmPermissionModule',
                setting: {
                    isOpenNewPage: false,
                    isCache: false
                },
                param: {
                },
                result: {}
            }
        ]
    },

    optionSignAgreement: {
        id: '',
        name: 'optionSignAgreement',
        modules: [
            {
                name: 'signPactModule',
                setting: {
                    isOpenNewPage: false,
                    isCache: false
                },
                param: {
                    type:'option',
                    marketType:'sz'
                }
            },
        ]
    },
    optionUploadId: {
        id: '',
        name: 'optionUploadId',
        modules: [
            {
                name: 'reserveUploadIdCardModule', 
                setting: {
                    isOpenNewPage: false,
                    isCache: false
                },
                param: {
                },
                result: {}
            },
        ]
    },
    optionRetModule: {
        id: '',
        name: 'optionRetModule',
        modules: [
            {
                name: 'optionRetModule',
                setting: {
                    isOpenNewPage: false,
                    isCache: false
                },
                param: {
                },
                result: {}
            }
        ]
    },

    //新三板
    //新三板-查询
    "stockRotation": {
        "id": "",
        "name": "stockRotation",
        "modules": [
            {
                "name": "noticeModule",
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                "param": {
                    UniqueKey: 'ANNC_1004',
                    type: '1002',
                    subtype: '********'
                },
            },
            {
                "name": "stockRotationQueryModule",
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                "param": {
                    UniqueKey: 'CWSR_01',
                    clientId: '($usercode)',
                    fund_account: '($fund_account)',
                    password: '($password)',
                    branch_no: '($khbranch)'

                },
            }]
    },
    //新三板--开通
    stockRotationOpen: {
        id: '',
        name: 'stockRotationOpen',
        modules: [
            {
                name: 'conditionCheckModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    UniqueKey: "ACU_001",
                    "business_type": "30077",//条件检查-新三板交易权限
                    "client_id": "($usercode)",
                    "password": "($password)",
                    "op_station": "($op_station)",
                    "fund_account": '($fund_account)',
                    "pageTitle": "新三板交易权限"

                },
                result: {}
            },
            {
                name: 'stockRotationChooseTypeModule',
                setting: {
                    isOpenNewPage: false,
                    isCache: false
                },
                param: {
                    UniqueKey: "ACU_001",
                    "business_type": "30078",//补充条件检查-新三板交易权限
                    "client_id": "($usercode)",
                    "password": "($password)",
                    "op_station": "($op_station)",
                    "fund_account": '($fund_account)',
                    "pageTitle": "新三板交易权限"
                },
                result: {}
            },
        ]
    },
    stockRotationClose: {
        id: '',
        name: 'stockRotationClose',
        modules: [
            {
                name: 'conditionCheckModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    UniqueKey: "ACU_001",
                    "business_type": "30079",  //新三板注销-条件检查
                    "client_id": "($usercode)",
                    "password": "($password)",
                    "op_station": "($op_station)",
                    "pageTitle": "新三板交易权限"
                }
            },
            {
                "name": "cancelConfirmModule",//取消确认弹框
                setting: {
                    isOpenNewPage: false,
                    isCache: false
                },
                "param": {
                    //rightName:'',//权限名称
                },
                "result": {}
            },
            {
                name: 'stockRotationCloseRetModule',
                setting: {
                    isOpenNewPage: false,
                    isCache: false
                },
                param: {
                    client_id: '($usercode)',
                    fund_account: '($fund_account)',
                    password: '($password)',
                    UniqueKey: "CWSR_03",
                    clientId: '($usercode)',
                    fund_account: '($fund_account)',
                    password: '($password)',
                    branch_no: '($khbranch)',
                    //stock_account:'',
                    //sst_report_type:'',
                }
            }
        ]
    },
    thirdBoardCheckphone: {
        id: '',
        name: 'thirdBoardCheckphone',
        modules: [
            {
                name: 'investorPhoneVerifyModule',
                setting: {
                    isOpenNewPage: false,
                    isCache: false
                },
                param: {
                    template_no: '25',
                    pageTitle: '新三板交易权限',
                    loggerCollectConfig: {
                        event_id_label: 'sms_validate',
                        logtype: 'cancel_sms_validate'
                    },
                    //nextStepType: 'investorNextStep'
                },
                result: {}
            }
        ]
    },

    thirdBoardConfirmationOfAppropriateness: {
        id: '',
        name: 'thirdBoardConfirmationOfAppropriateness',
        modules: [
            {
                name: 'suitablyModule',
                setting: {
                    isOpenNewPage: false,
                    isCache: false
                },
                param: {
                    // type:'stib',
                    // marketType:'sh'
                }
            },
        ]
    },

    thirdBoardKnowledgeAssessment: {
        id: '',
        name: 'thirdBoardKnowledgeAssessment',
        modules: [
            {
                name: 'quesListModule',
                setting: {
                    isOpenNewPage: false,
                    isCache: false
                },
                param: {
                    questionType:'thirdBoardKnowledgeAssessment'
                }
            },
        ]
    },

    thirdBoardOneSignAgreement: {
        id: '',
        name: 'thirdBoardOneSignAgreement',
        modules: [
            {
                name: 'signPactModule',
                setting: {
                    isOpenNewPage: false,
                    isCache: false
                },
                param: {
                    type:'thirdBordOne',
                    marketType:'sz'
                }
            },
        ]
    },
    thirdBoardTwoSignAgreement: {
        id: '',
        name: 'thirdBoardTwoSignAgreement',
        modules: [
            {
                name: 'signPactModule',
                setting: {
                    isOpenNewPage: false,
                    isCache: false
                },
                param: {
                    type:'thirdBordTwo',
                    marketType:'sz'
                }
            },
        ]
    },
    thirdBoardThreeSignAgreement: {
        id: '',
        name: 'thirdBoardThreeSignAgreement',
        modules: [
            {
                name: 'signPactModule',
                setting: {
                    isOpenNewPage: false,
                    isCache: false
                },
                param: {
                    type:'thirdBordThree',
                    marketType:'sz'
                }
            },
        ]
    },

   
    thirdBoardOneManageOpen: {
        id: '',
        name: 'thirdBoardOneManageOpen',
        modules: [
            {
                name: 'thirdBoardOpenRetModule',
                setting: {
                    isOpenNewPage: false,
                    isCache: false
                },
                param: {
                    UniqueKey: "CWSR_02",
                    todoId:'',
                    busi_param:'',
                    branch_no: '($khbranch)',
                    client_id: '($usercode)',
                    fund_account: '($fund_account)',
                    sst_report_type:3,
                    password: "($password)",
                    op_station: "($op_station)",
                }
            }
        ]
    },
    thirdBoardTwoManageOpen: {
        id: '',
        name: 'thirdBoardTwoManageOpen',
        modules: [
            {
                name: 'thirdBoardOpenRetModule',
                setting: {
                    isOpenNewPage: false,
                    isCache: false
                },
                param: {
                    UniqueKey: "CWSR_02",
                    todoId:'',
                    busi_param:'',
                    branch_no: '($khbranch)',
                    client_id: '($usercode)',
                    fund_account: '($fund_account)',
                    sst_report_type:4,
                    password: "($password)",
                    op_station: "($op_station)",
                }
            }
        ]
    },
    thirdBoardThreeManageOpen: {
        id: '',
        name: 'thirdBoardThreeManageOpen',
        modules: [
            {
                name: 'thirdBoardOpenRetModule',
                setting: {
                    isOpenNewPage: false,
                    isCache: false
                },
                param: {
                    UniqueKey: "CWSR_02",
                    todoId:'',
                    busi_param:'',
                    branch_no: '($khbranch)',
                    client_id: '($usercode)',
                    fund_account: '($fund_account)',
                    sst_report_type:5,
                    password: "($password)",
                    op_station: "($op_station)",
                }
            }
        ]
    },

    //北交所
    "stockBeiQuery": {
        "id": "",
        "name": "stockBeiQuery",
        "modules": [
            {
                "name": "noticeModule",
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                "param": {
                    UniqueKey: 'ANNC_1004',
                    type: '1002',
                    subtype: '********'
                },
            },
            {
                "name": "stockBeiQueryModule",
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                "param": {
                    UniqueKey: 'CWSRBI_01',
                    clientId: '($usercode)',
                    fund_account: '($fund_account)',
                    password: '($password)',
                    branch_no: '($khbranch)'

                },
            }]
    },
    //北交所--开通
    stockBeiOpen: {
        id: '',
        name: 'stockBeiOpen',
        modules: [
            {
                name: 'conditionCheckModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    UniqueKey: "ACU_001",
                    "business_type": "30100",//条件检查-新三板交易权限
                    "client_id": "($usercode)",
                    "password": "($password)",
                    "op_station": "($op_station)",
                    "fund_account": '($fund_account)',
                    "pageTitle": "北交所交易权限"

                },
                result: {}
            },
            {
                name: 'stockBeiAdditionConditionModule',
                setting: {
                    isOpenNewPage: false,
                    isCache: false
                },
                param: {
                    UniqueKey: "ACU_001",
                    "business_type": "30101",//补充条件检查-新三板交易权限
                    "client_id": "($usercode)",
                    "password": "($password)",
                    "op_station": "($op_station)",
                    "fund_account": '($fund_account)',
                    "pageTitle": "北交所交易权限"
                },
                result: {}
            },
            // {
            //     name: 'createPreEIBusinessInfoModule',
            //     setting: {
            //         isOpenNewPage: false,
            //         isCache: false
            //     },
            //     param: {
            //         UniqueKey: 'ACSO_02',
            //         clientId: '($usercode)',
            //         businessType: '30100',
            //         fundAccount: '($fund_account)',
            //         password: '($password)',
            //         op_station: "($op_station)",
            //         //opEntrustWay: '7',
            //         userType: '12',
            //         userId: '($usercode)',
            //         clientName: '($client_name)',
            //     },
            //     result: {}
            // }, 
        ]
    },
  
    stockBeiCheckphone: {
        id: '',
        name: 'stockBeiCheckphone',
        modules: [
            {
                name: 'investorPhoneVerifyModule',
                setting: {
                    isOpenNewPage: false,
                    isCache: false
                },
                param: {
                    template_no: '27',
                    pageTitle: '北交所交易权限',
                    loggerCollectConfig: {
                        event_id_label: 'sms_validate',
                        logtype: 'cancel_sms_validate'
                    },
                    //nextStepType: 'investorNextStep'
                },
                result: {}
            }
        ]
    },

    stockBeiConfirmationOfAppropriateness: {
        id: '',
        name: 'stockBeiConfirmationOfAppropriateness',
        modules: [
            {
                name: 'suitablyModule',
                setting: {
                    isOpenNewPage: false,
                    isCache: false
                },
                param: {
                    type:'stockBei',
                    marketType:'sz'
                }
            },
        ]
    },

    stockBeiKnowledgeAssessment: {
        id: '',
        name: 'stockBeiKnowledgeAssessment',
        modules: [
            {
                name: 'quesListModule',
                setting: {
                    isOpenNewPage: false,
                    isCache: false
                },
                param: {
                    questionType:'stockBei'
                }
            },
        ]
    },

    stockBeiSignAgreement: {
        id: '',
        name: 'stockBeiSignAgreement',
        modules: [
            {
                name: 'signPactModule',
                setting: {
                    isOpenNewPage: false,
                    isCache: false
                },
                param: {
                    type:'stockBei',
                    marketType:'sz'
                }
            },
        ]
    },
   
    stockBeiManageOpen: {
        id: '',
        name: 'stockBeiManageOpen',
        modules: [
            {
                name: 'stockBeiOpenRetModule',
                setting: {
                    isOpenNewPage: false,
                    isCache: false
                },
                param: {
                    UniqueKey: "CWSRBOS_02",
                    todoId:'',
                    busi_param:'',
                    branch_no: '($khbranch)',
                    client_id: '($usercode)',
                    fund_account: '($fund_account)',
                    sst_report_type: 8,
                    password: "($password)",
                    op_station: "($op_station)",
                }
            }
        ]
    },
    stockBeiClose: {
        id: '',
        name: 'stockBeiClose',
        modules: [
            {
                name: 'conditionCheckModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    UniqueKey: "ACU_001",
                    "business_type": "30102",  //新三板注销-条件检查
                    "client_id": "($usercode)",
                    "password": "($password)",
                    "op_station": "($op_station)",
                    "pageTitle": "北交所交易权限"
                }
            },
            {
                "name": "cancelConfirmModule",//取消确认弹框
                setting: {
                    isOpenNewPage: false,
                    isCache: false
                },
                "param": {
                    rightName:'北交所交易权限',
                },
                "result": {}
            },
            {
                name: 'stockBeiCloseRetModule',
                setting: {
                    isOpenNewPage: false,
                    isCache: false
                },
                param: {
                    client_id: '($usercode)',
                    fund_account: '($fund_account)',
                    password: '($password)',
                    UniqueKey: "CWSRBC_03",
                    clientId: '($usercode)',
                    fund_account: '($fund_account)',
                    password: '($password)',
                    branch_no: '($khbranch)',
                    //stock_account:'',
                    //sst_report_type:'',
                }
            }
        ]
    },
    //新债
    "debentureQuery": {
        "id": "",
        "name": "debentureQuery",
        "modules": [
            {
                "name": "noticeModule",
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                "param": {
                    UniqueKey: 'ANNC_1004',
                    type: '1002',
                    subtype: '********'
                },
            },
            {
                "name": "debentureQueryModule",
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                "param": {
                    UniqueKey: 'CWCSINFO',
                    clientId: '($usercode)',
                    fund_account: '($fund_account)',
                    password: '($password)',
                    branch_no: '($khbranch)'

                },
            }]
    },
    //新债--开通
    "debentureOpen": {
        id: '',
        name: 'debentureOpen',
        modules: [
            {
                name: 'conditionCheckModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    UniqueKey: "ACU_001",
                    "business_type": "30105",//条件检查-新三板交易权限
                    "client_id": "($usercode)",
                    "password": "($password)",
                    "op_station": "($op_station)",
                    "fund_account": '($fund_account)',
                    "pageTitle": "公司债/企业债权限"

                },
                result: {}
            },
            {
                name: 'createPreEIBusinessInfoModule',
                setting: {
                    isOpenNewPage: false,
                    isCache: false
                },
                param: {
                    UniqueKey: 'ACSO_02',
                    clientId: '($usercode)',
                    businessType: '30105',
                    fundAccount: '($fund_account)',
                    password: '($password)',
                    op_station: "($op_station)",
                    opEntrustWay: '7',
                    branch_no: '($khbranch)',
                    userType: '12',
                    userId: '($usercode)',
                    clientName: '($client_name)',
                },
                result: {}
            },  
        ]
    },
    
    "ptxzConfirmation": {
        id: '',
        name: 'ptxzConfirmation',
        modules: [
            {
                name: 'suitablyModule',
                setting: {
                    isOpenNewPage: false,
                    isCache: false
                },
                param: {
                    type:'debenture',
                    marketType:'sz'
                }
            },
        ]
    },
    'ptxzSignAgreement': {
        id: '',
        name: 'ptxzSignAgreement',
        modules: [
            {
                name: 'signPactModule',
                setting: {
                    isOpenNewPage: false,
                    isCache: false
                },
                param: {
                    type:'debenture',
                    marketType:'sz'
                }
            },
        ]
    },
    'ptxzManageOpen': {
        id: '',
        name: 'ptxzManageOpen',
        modules: [
            {
                name: 'ptxzManageOpenRetModule',
                setting: {
                    isOpenNewPage: false,
                    isCache: false
                },
                param: {
                    UniqueKey: "CWCSOPEN",
                    todoId:'',
                    busi_param:'',
                    exchange_type: '2',
                    stock_account: '',
                    branch_no: '($khbranch)',
                    client_id: '($usercode)',
                    fund_account: '($fund_account)',
                    sst_report_type: 8,
                    password: "($password)",
                    op_station: "($op_station)",
                }
            }
        ]
    },
    debentureClose: {
        id: '',
        name: 'debentureClose',
        modules: [
            {
                name: 'conditionCheckModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    UniqueKey: "ACU_001",
                    "business_type": "30106", 
                    "client_id": "($usercode)",
                    "password": "($password)",
                    "op_station": "($op_station)",
                    "pageTitle": "公司债/企业债权限"
                }
            },
            {
                "name": "cancelConfirmModule",//取消确认弹框
                setting: {
                    isOpenNewPage: false,
                    isCache: false
                },
                "param": {
                    rightName:'公司债/企业债权限',
                },
                "result": {}
            },
            {
                name: 'debentureCloseRetModule',
                setting: {
                    isOpenNewPage: false,
                    isCache: false
                },
                param: {
                    UniqueKey: "CWCSCLOSE",
                    client_id: '($usercode)',
                    fund_account: '($fund_account)',
                    password: '($password)',
                    branch_no: '($khbranch)',
                    //stock_account:'',
                    //sst_report_type:'',
                }
            }
        ]
    },
    //信用新债
    "creditDebentureQuery": {
        "id": "",
        "name": "creditDebentureQuery",
        "modules": [
            {
                "name": "noticeModule",
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                "param": {
                    UniqueKey: 'ANNC_1004',
                    type: '1002',
                    subtype: '********'
                },
            },
            {
                "name": "creditDebentureQueryModule",
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                "param": {
                    UniqueKey: 'CWCSCINFO',
                    clientId: '($usercode)',
                    fund_account: '($credit_fund_account)',
                    password: '($password)',
                    branch_no: '($khbranch)'

                },
            }]
    },
    //信用新债--开通
    "creditDebentureOpen": {
        id: '',
        name: 'creditDebentureOpen',
        modules: [
            {
                name: 'conditionCheckModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    UniqueKey: "ACU_001",
                    "business_type": "30107",
                    "client_id": "($usercode)",
                    "password": "($password)",
                    "op_station": "($op_station)",
                    "fund_account": '($fund_account)',
                    "pageTitle": "信用公司债/企业债权限"

                },
                result: {}
            },
            {
                name: 'needInputCreditInfoModule',
                setting: {
                    isOpenNewPage: false,
                    isCache: false
                },
                param: {
                    UniqueKey: "UAGPDF",
                    "client_id": "($usercode)"

                },
                result: {}
            },
            {
                name: 'inputCreditInfoModule',
                setting: {
                    isOpenNewPage: false,
                    isCache: false
                },
                param: {
                    UniqueKey: 'CWCSINFO',
                    clientId: '($usercode)',
                    fund_account: '($fund_account)',
                    password: '($password)',
                    branch_no: '($khbranch)'

                },
                result: {}
            },
            {
                name: 'createPreEIBusinessInfoModule',
                setting: {
                    isOpenNewPage: false,
                    isCache: false
                },
                param: {
                    UniqueKey: 'ACSO_02',
                    clientId: '($usercode)',
                    businessType: '30107',
                    fundAccount: '($credit_fund_account)',
                    password: '($password)',
                    op_station: "($op_station)",
                    opEntrustWay: '7',
                    branch_no: '($khbranch)',
                    userType: '12',
                    userId: '($usercode)',
                    clientName: '($client_name)',
                },
                result: {}
            },  
        ]
    },
    "infoCreditInfo": {
        id: '',
        name: 'infoCreditInfo',
        modules: [
            {
                name: 'inputCreditInfoModule',
                setting: {
                    isOpenNewPage: false,
                    isCache: false
                },
                param: {
                    UniqueKey: "ACU_001",
                    "business_type": "30107",
                    "client_id": "($usercode)",
                    "password": "($password)",
                    "op_station": "($op_station)",
                    "fund_account": '($fund_account)',
                    "pageTitle": "信用公司债/企业债权限"

                },
                result: {}
            },
            {
                name: 'createPreEIBusinessInfoModule',
                setting: {
                    isOpenNewPage: false,
                    isCache: false
                },
                param: {
                    UniqueKey: 'ACSO_02',
                    clientId: '($usercode)',
                    businessType: '30107',
                    fundAccount: '($credit_fund_account)',
                    password: '($password)',
                    op_station: "($op_station)",
                    opEntrustWay: '7',
                    branch_no: '($khbranch)',
                    userType: '12',
                    userId: '($usercode)',
                    clientName: '($client_name)',
                },
                result: {}
            },  
        ]
    },
    "createPreEIBusinessInfo": {
        id: '',
        name: 'createPreEIBusinessInfo',
        modules: [
            {
                name: 'createPreEIBusinessInfoModule',
                setting: {
                    isOpenNewPage: false,
                    isCache: false
                },
                param: {
                    UniqueKey: 'ACSO_02',
                    clientId: '($usercode)',
                    businessType: '30107',
                    fundAccount: '($fund_account)',
                    password: '($password)',
                    op_station: "($op_station)",
                    opEntrustWay: '7',
                    branch_no: '($khbranch)',
                    userType: '12',
                    userId: '($usercode)',
                    clientName: '($client_name)',
                },
                result: {}
            },
        ]
    },
    "xyxzConfirmation": {
        id: '',
        name: 'xyxzConfirmation',
        modules: [
            {
                name: 'suitablyModule',
                setting: {
                    isOpenNewPage: false,
                    isCache: false
                },
                param: {
                    type:'creditDebenture',
                    marketType:'sz'
                }
            },
        ]
    },
    "xyxzConfirmation": {
        id: '',
        name: 'xyxzConfirmation',
        modules: [
            {
                name: 'suitablyModule',
                setting: {
                    isOpenNewPage: false,
                    isCache: false
                },
                param: {
                    type:'creditDebenture',
                    marketType:'sz'
                }
            },
        ]
    },
    'xyxzSignAgreement': {
        id: '',
        name: 'xyxzSignAgreement',
        modules: [
            {
                name: 'signPactModule',
                setting: {
                    isOpenNewPage: false,
                    isCache: false
                },
                param: {
                    type:'creditDebenture',
                    marketType:'sz'
                }
            },
        ]
    },
    'xyxzManageOpen': {
        id: '',
        name: 'xyxzManageOpen',
        modules: [
            {
                name: 'xyxzManageOpenRetModule',
                setting: {
                    isOpenNewPage: false,
                    isCache: false
                },
                param: {
                    UniqueKey: "CWCSOPEN",
                    todoId:'',
                    busi_param:'',
                    exchange_type: '2',
                    stock_account: '',
                    branch_no: '($khbranch)',
                    client_id: '($usercode)',
                    fund_account: '($credit_fund_account)',
                    sst_report_type: 8,
                    password: "($password)",
                    op_station: "($op_station)",
                }
            }
        ]
    },
    creditDebentureClose: {
        id: '',
        name: 'creditDebentureClose',
        modules: [
            {
                name: 'conditionCheckModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    UniqueKey: "ACU_001",
                    "business_type": "30108", 
                    "client_id": "($usercode)",
                    "password": "($password)",
                    "op_station": "($op_station)",
                    "pageTitle": "信用公司债/企业债权限"
                }
            },
            {
                "name": "cancelConfirmModule",//取消确认弹框
                setting: {
                    isOpenNewPage: false,
                    isCache: false
                },
                "param": {
                    rightName:'信用公司债/企业债权限',
                },
                "result": {}
            },
            {
                name: 'needInputCreditInfoModule',
                setting: {
                    isOpenNewPage: false,
                    isCache: false
                },
                param: {
                    UniqueKey: "UAGPDF",
                    "client_id": "($usercode)"

                },
                result: {}
            },
            {
                name: 'inputCreditInfoModule',
                setting: {
                    isOpenNewPage: false,
                    isCache: false
                },
                param: {
                    UniqueKey: 'CWCSINFO',
                    clientId: '($usercode)',
                    fund_account: '($fund_account)',
                    password: '($password)',
                    branch_no: '($khbranch)'

                },
                result: {}
            },
            {
                name: 'creditDebentureCloseRetModule',
                setting: {
                    isOpenNewPage: false,
                    isCache: false
                },
                param: {
                    UniqueKey: "CWCSCLOSE",
                    client_id: '($usercode)',
                    fund_account: '($credit_fund_account)',
                    password: '($password)',
                    branch_no: '($khbranch)',
                    //stock_account:'',
                    //sst_report_type:'',
                }
            }
        ]
    },
    'creditDebentureCloseRet': {
        id: '',
        name: 'creditDebentureCloseRet',
        modules: [
            {
                name: 'creditDebentureCloseRetModule',
                setting: {
                    isOpenNewPage: false,
                    isCache: false
                },
                param: {
                    UniqueKey: "CWCSCLOSE",
                    client_id: '($usercode)',
                    fund_account: '($credit_fund_account)',
                    password: '($password)',
                    branch_no: '($khbranch)',
                }
            }
        ]
    },
    //新三板-查询
    "iadviserBasic": {
        "id": "",
        "name": "iadviserBasic",
        "modules": [
            {
                "name": "noticeModule",
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                "param": {
                    UniqueKey: 'ANNC_1004',
                    type: '1002',
                    subtype: '********'
                },
            },
            {
                "name": "iadviserBasicQueryModule",
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                "param": {
                    UniqueKey: 'IAB_01',
                    client_id: '($usercode)',
                    // UniqueKey: 'ANNC_1004',
                    // type: '1002',
                    // subtype: '********'

                },
            }]
    },
    "iadviserBasicForWeb": {
        "id": "",
        "name": "iadviserBasicForWeb",
        "modules": [
            {
                "name": "noticeModule",
                setting: {
                    isOpenNewPage: false,
                    isCache: false
                },
                "param": {
                    UniqueKey: 'ANNC_1004',
                    type: '1002',
                    subtype: '********'
                },
            },
            {
                name: 'preHandlerForWebModule',//机构户校验
                setting: {
                    isOpenNewPage: false,
                    isCache: false,
                },
                param: {
                    needPersonal: true,
                    needCredit: false,
                    creditNextStep:'openCredit',
                    pageTitle:'签约标准咨询服务',
                },
                result: {}
            },
            {
                "name": "iadviserBasicQueryModule",
                setting: {
                    isOpenNewPage: false,
                    isCache: false
                },
                "param": {
                    UniqueKey: 'IAB_01',
                    client_id: '($usercode)',
                },
            }]
    },
    iadviserBasicOpen: {
        id: '',
        name: 'iadviserBasicOpen',
        modules: [
            {
                name: 'conditionCheckModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    UniqueKey: "ACU_001",
                    "business_type": "30080",//条件检查-
                    "client_id": "($usercode)",
                    "password": "($password)",
                    "op_station": "($op_station)",
                    "fund_account": '($fund_account)',
                    "pageTitle": "签约标准咨询服务"
                },
                result: {}
            },
            {
                name: 'createPreEIBusinessInfoModule',
                setting: {
                    isOpenNewPage: false,
                    isCache: false
                },
                param: {
                    UniqueKey: 'ACSO_02',
                    clientId: '($usercode)',
                    businessType: '30080',
                    fundAccount: '($fund_account)',
                    password: '($password)',
                    op_station: "($op_station)",
                    //opEntrustWay: '7',
                    userType: '12',
                    userId: '($usercode)',
                    clientName: '($client_name)',
                },
                result: {}
            },        
        ]
    },
    iadviserBasicOpenForWeb: {
        id: '',
        name: 'iadviserBasicOpenForWeb',
        modules: [
            {
                name: 'conditionCheckModule',
                setting: {
                    isOpenNewPage: false,
                    isCache: false
                },
                param: {
                    UniqueKey: "ACU_001",
                    "business_type": "30080",//条件检查-
                    "client_id": "($usercode)",
                    "password": "($password)",
                    "op_station": "($op_station)",
                    "fund_account": '($fund_account)',
                    "pageTitle": "签约标准咨询服务"
                },
                result: {}
            },
            {
                name: 'createPreEIBusinessInfoModule',
                setting: {
                    isOpenNewPage: false,
                    isCache: false
                },
                param: {
                    UniqueKey: 'ACSO_02',
                    clientId: '($usercode)',
                    businessType: '30080',
                    fundAccount: '($fund_account)',
                    password: '($password)',
                    op_station: "($op_station)",
                    //opEntrustWay: '7',
                    userType: '12',
                    userId: '($usercode)',
                    clientName: '($client_name)',
                },
                result: {}
            },        
        ]
    },
    counselingConfirmation: {
        id: '',
        name: 'counselingConfirmation',
        modules: [
            {
                name: 'counselingConfirmationModule',
                setting: {
                    isOpenNewPage: false,
                    isCache: false
                },
                param: {
                    type:'iadviserBasic',
                    marketType:'sh'
                }
            },
        ]
    },
    counselingSignAgreement: {
        id: '',
        name: 'counselingSignAgreement',
        modules: [
            {
                name: 'counselingSignAgreementModule',
                setting: {
                    isOpenNewPage: false,
                    isCache: false
                },
                param: {
                    type:'iadviserBasic',
                    "pageTitle": "签约标准咨询服务",
                    'group_nos':'5043'
                }
            },
        ]
    },

    counselingResult: {
        id: '',
        name: 'counselingResult',
        modules: [
            {
                name: 'counselingResultModule',
                setting: {
                    isOpenNewPage: false,
                    isCache: false
                },
                param: {
                },
                result: {}
            }
        ]
    },

    /**
     * 创业板改革
     */
    startUpBoardV2Query: {
        id: '',
        name: 'startUpBoardV2Query',
        modules: [
            {
                name: 'noticeModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    UniqueKey: 'ANNC_1004',
                    type: '1002',
                    subtype: '********' 
                }
            },
            {
                name: 'startUpBoardV2QueryModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    UniqueKey: 'CWCNI',
                    client_id: '($usercode)',
                    fund_account: '($fund_account)',
                    password: '($password)'
                },
                result: {}
            }
        ]
    },
    //首页-签署新版创业板风险揭示书,已签署需弹框
    startUpBoardV2SignQuery: {
        id: '',
        name: 'startUpBoardV2SignQuery',
        modules: [
            {
                name: 'noticeModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    UniqueKey: 'ANNC_1004',
                    type: '1002',
                    subtype: '********' 
                }
            },
            {
                name: 'startUpBoardV2QueryModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    UniqueKey: 'CWCNI',
                    client_id: '($usercode)',
                    fund_account: '($fund_account)',
                    password: '($password)'
                },
                result: {}
            }
        ]
    },

    startUpBoardV2Open: {
        id: '',
        name: 'startUpBoardV2Open',
        modules: [
            {
                name: 'conditionCheckModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    UniqueKey: "ACU_001",
                    "business_type": "30082",
                    "client_id": "($usercode)",
                    "password": "($password)",
                    "op_station": "($op_station)",
                    "pageTitle": "创业板"
                },
                result: {}
            },
            {
                name: 'createProcessAndStartModule',
                setting: {
                    isOpenNewPage: false,
                    isCache: false
                },
                param: {
                    UniqueKey: 'ACSO_02',
                    clientId: '($usercode)',
                    businessType: '30082',//todo
                    fundAccount: '($fund_account)',
                    password: '($password)',
                    op_station: "($op_station)",
                    opEntrustWay: '7',
                    branch_no: '($khbranch)',
                    userType: '12',
                    userId: '($usercode)',
                    clientName: '($client_name)',
                },
                result: {}
            },        
        ]
    },
    //创业板开通-适当性
    startUpBoardV2Confirmation: {
        id: '',
        name: 'startUpBoardV2Confirmation',
        modules: [
            {
                name: 'suitablyModule',
                setting: {
                    isOpenNewPage: false,
                    isCache: false
                },
                param: {
                     type:'startupboardV2',
                     marketType:'sz'
                }
            },
        ]
    },

    //创业板开通-风险揭示书
    startUpBoardV2SignAgreement: {
        id: '',
        name: 'startUpBoardV2SignAgreement',
        modules: [
            {
                name: 'signPactModule',
                setting: {
                    isOpenNewPage: false,
                    isCache: false
                },
                param: {
                    type:'startupboardV2',
                    marketType:'sz'
                }
            },
        ]
    },
    //创业板开通-开通
    startUpBoardV2ManageOpen: {
        id: '',
        name: 'startUpBoardV2ManageOpen',
        modules: [
            {
                name: 'startUpBoardV2OpenRetModule',
                setting: {
                    isOpenNewPage: false,
                    isCache: false
                },
                param: {
                    UniqueKey: "CWCNO",
                    todoId:'',
                    busi_param:'',
                    branch_no: '($khbranch)',
                    client_id: '($usercode)',
                    fund_account: '($fund_account)',
                    password: "($password)",
                    op_station: "($op_station)",
                }
            }
        ]
    },

    //创业板转签查询
    startUpBoardQuery: {
        id: '',
        name: 'startUpBoardQuery',
        modules: [
            {
                name: 'noticeModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    UniqueKey: 'ANNC_1004',
                    type: '1002',
                    subtype: '********'
                }
            },
            {
                name: 'startUpBoardQueryModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    UniqueKey: 'CWCNI',
                    client_id: '($usercode)',
                    fund_account: '($fund_account)',
                    password: '($password)'
                },
                result: {}
            }
        ]
    },
    //创业板转签更新 todo
    // startUpBoardUpdate: {
    //     id: '',
    //     name: 'startUpBoardUpdate',
    //     modules: [
    //         {
    //             name: 'conditionCheckModule',
    //             setting: {
    //                 isOpenNewPage: true,
    //                 isCache: false
    //             },
    //             param: {
    //                 UniqueKey: "ACU_001",
    //                 "business_type": "30005",
    //                 "client_id": "($usercode)",
    //                 "password": "($password)",
    //                 "op_station": "($op_station)",
    //                 "pageTitle": "创业板"
    //             },
    //             result: {}
    //         },
    //         {
    //             //适当性
    //             name: 'suitablyModule',
    //             setting: {
    //                 isOpenNewPage: false,
    //                 isCache: false
    //             },
    //             param: {}
    //         },
    //         {   //风险揭示书
    //             name: 'signPactModule',
    //             setting: {
    //                 isOpenNewPage: false,
    //                 isCache: false
    //             },
    //             param: {
    //                 type:'startupboard',
    //                 marketType:'sz'
    //             }
    //         },
    //         {
    //             name: 'startUpBoardUpdateModule',
    //             setting: {
    //                 isOpenNewPage: false,
    //                 isCache: false
    //             },
    //             param: {
    //                 UniqueKey: 'CWCNT',
    //                 client_id: '($usercode)',
    //                 fund_account: '($fund_account)',
    //                 password: '($password)'
    //                 //stock_account:'',
    //                 //gen_train_flag:''
    //             }
    //         }
    //     ]
    // },
    //创业板转签
    startUpBoardZq: {
        id: '',
        name: 'startUpBoardZq',
        modules: [
            {
                name: 'conditionCheckModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    UniqueKey: "ACU_001",
                    "business_type": "30083",
                    "client_id": "($usercode)",
                    "password": "($password)",
                    "op_station": "($op_station)",
                    "pageTitle": "创业板"
                },
                result: {}
            },
            {
                name: 'createProcessAndStartModule',
                setting: {
                    isOpenNewPage: false,
                    isCache: false
                },
                param: {
                    UniqueKey: 'ACSO_02',
                    clientId: '($usercode)',
                    businessType: '30083',
                    fundAccount: '($fund_account)',
                    password: '($password)',
                    op_station: "($op_station)",
                    opEntrustWay: '7',
                    branch_no: '($khbranch)',
                    userType: '12',
                    userId: '($usercode)',
                    clientName: '($client_name)',
                },
                result: {}
            },        
        ]
    },
    //创业板转签-适当性
    startUpBoardZqConfirmation: {
        id: '',
        name: 'startUpBoardZqConfirmation',
        modules: [
            {
                name: 'suitablyModule',
                setting: {
                    isOpenNewPage: false,
                    isCache: false
                },
                param: {
                     type:'startupboardZq',
                     marketType:'sz'
                }
            },
        ]
    },

    //创业板转签-风险揭示书
    startUpBoardZqSignAgreement: {
        id: '',
        name: 'startUpBoardZqSignAgreement',
        modules: [
            {
                name: 'signPactModule',
                setting: {
                    isOpenNewPage: false,
                    isCache: false
                },
                param: {
                    type:'startupboardZq',
                    marketType:'sz'
                }
            },
        ]
    },
    //创业板转签-开通
    startUpBoardZqManageOpen: {
        id: '',
        name: 'startUpBoardZqManageOpen',
        modules: [
            {
                name: 'startUpBoardZqOpenRetModule',//todo
                setting: {
                    isOpenNewPage: false,
                    isCache: false
                },
                param: {
                    UniqueKey: "CWCNO",
                    todoId:'',
                    busi_param:'',
                    branch_no: '($khbranch)',
                    client_id: '($usercode)',
                    fund_account: '($fund_account)',
                    password: "($password)",
                    op_station: "($op_station)",
                }
            }
        ]
    },
    //创业板补签
    startUpBoardBq: {
        id: '',
        name: 'startUpBoardBqOpen',
        modules: [
            {
                name: 'conditionCheckModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    UniqueKey: "ACU_001",
                    "business_type": "30084",
                    "client_id": "($usercode)",
                    "password": "($password)",
                    "op_station": "($op_station)",
                    "pageTitle": "创业板"
                },
                result: {}
            },
            {
                name: 'createProcessAndStartModule',
                setting: {
                    isOpenNewPage: false,
                    isCache: false
                },
                param: {
                    UniqueKey: 'ACSO_02',
                    clientId: '($usercode)',
                    businessType: '30084',
                    fundAccount: '($fund_account)',
                    password: '($password)',
                    op_station: "($op_station)",
                    opEntrustWay: '7',
                    branch_no: '($khbranch)',
                    userType: '12',
                    userId: '($usercode)',
                    clientName: '($client_name)',
                },
                result: {}
            },        
        ]
    },
    //创业板补签-适当性
    startUpBoardBqConfirmation: {
        id: '',
        name: 'startUpBoardBqConfirmation',
        modules: [
            {
                name: 'suitablyModule',
                setting: {
                    isOpenNewPage: false,
                    isCache: false
                },
                param: {
                     type:'startupboardBq',
                     marketType:'sz'
                }
            },
        ]
    },

    //创业板补签-风险揭示书
    startUpBoardBqSignAgreement: {
        id: '',
        name: 'startUpBoardBqSignAgreement',
        modules: [
            {
                name: 'signPactModule',
                setting: {
                    isOpenNewPage: false,
                    isCache: false
                },
                param: {
                    type:'startupboardBq',
                    marketType:'sz'
                }
            },
        ]
    },
    //创业板补签-开通
    startUpBoardBqManageOpen: {
        id: '',
        name: 'startUpBoardBqManageOpen',
        modules: [
            {
                name: 'startUpBoardBqOpenRetModule',//todo
                setting: {
                    isOpenNewPage: false,
                    isCache: false
                },
                param: {
                    UniqueKey: "CWCNO",
                    todoId:'',
                    busi_param:'',
                    branch_no: '($khbranch)',
                    client_id: '($usercode)',
                    fund_account: '($fund_account)',
                    password: "($password)",
                    op_station: "($op_station)",
                }
            }
        ]
    },
    startUpBoardXkToZq: {
        id: '',
        name: 'startUpBoardXkToZq',
        modules: [
            {
                name: 'startUpBoardXkToZqModule',//todo
                setting: {
                    isOpenNewPage: false,
                    isCache: false
                },
                param: {
                }
            }
        ]
    },

    startUpBoardZqToXk: {
        id: '',
        name: 'startUpBoardZqToXk',
        modules: [
            {
                name: 'startUpBoardZqToXkModule',//todo
                setting: {
                    isOpenNewPage: false,
                    isCache: false
                },
                param: {
                }
            }
        ]
    },
    //信用创业板
    creditStartUpBoardQuery: {
        id: '',
        name: 'creditStartUpBoardQuery',
        modules: [
            // {
            //     name: 'preHandlerModule',//机构户/信用资金账号校验
            //     setting: {
            //         isOpenNewPage: true,
            //         isCache: false
            //     },
            //     param: {
            //         //needPersonal: true,
            //         pageTitle:'信用科创板',
            //         needCredit: true,
            //         creditNextStep:'openCredit',
            //     },
            //     result: {}
            // },
            {
                name: 'noticeModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    UniqueKey: 'ANNC_1004',
                    type: '1002',
                    subtype: '********' 
                }
            },
            {
                name: 'creditStartUpBoardModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    UniqueKey: 'CWCCNI',//todo
                    client_id: '($usercode)',
                    fund_account: '($fund_account)',
                    password: '($password)'
                },
                result: {}
            }
        ]
    },
    creditStartUpBoardQueryForWeb: {
        id: '',
        name: 'creditStartUpBoardQueryForWeb',
        modules: [
            {
                name: 'preHandlerForWebModule',//机构户/信用资金账号校验
                setting: {
                    isOpenNewPage: false,
                    isCache: false
                },
                param: {
                    needPersonal: true,
                    needCredit: true,
                    pageTitle:'信用创业板',
                    creditNextStep:'openCreditHint',
                },
                result: {}
            },
            {
                name: 'noticeModule',
                setting: {
                    isOpenNewPage: false,
                    isCache: false
                },
                param: {
                    UniqueKey: 'ANNC_1004',
                    type: '1002',
                    subtype: '********' 
                }
            },
            {
                name: 'creditStartUpBoardModule',
                setting: {
                    isOpenNewPage: false,
                    isCache: false
                },
                param: {
                    UniqueKey: 'CWCCNI',
                    client_id: '($usercode)',
                    fund_account: '($fund_account)',
                    password: '($password)'
                },
                result: {}
            }
        ]
    },

    //信用创业板-新开流程
    creditStartUpBoardOpen: {
        id: '',
        name: 'creditStartUpBoardOpen',
        modules: [
            {
                name: 'conditionCheckModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    UniqueKey: "ACU_001",
                    "business_type": "30086",
                    "client_id": "($usercode)",
                    "password": "($password)",
                    "op_station": "($op_station)",
                    "pageTitle": "信用创业板"
                },
                result: {}
            },
            {
                name: 'createProcessAndStartModule',
                setting: {
                    isOpenNewPage: false,
                    isCache: false
                },
                param: {
                    UniqueKey: 'ACSO_02',
                    clientId: '($usercode)',
                    businessType: '30086',//信用创业板开通
                    fundAccount: '($credit_fund_account)',
                    password: '($password)',
                    op_station: "($op_station)",
                    opEntrustWay: '7',
                    branch_no: '($khbranch)',
                    userType: '12',
                    userId: '($usercode)',
                    clientName: '($client_name)',
                },
                result: {}
            },        
        ]
    },
    // //信用创业板新开-适当性
    // creditStartUpBoardConfirmation: {
    //     id: '',
    //     name: 'creditStartUpBoardConfirmation',
    //     modules: [
    //         {
    //             name: 'suitablyModule',
    //             setting: {
    //                 isOpenNewPage: false,
    //                 isCache: false
    //             },
    //             param: {
    //                  type:'creditStartUpBoard',
    //                  marketType:'sz'
    //             }
    //         },
    //     ]
    // },

    //信用创业板新开-风险揭示书
    creditStartUpBoardSignAgreement: {
        id: '',
        name: 'creditStartUpBoardSignAgreement',
        modules: [
            {
                name: 'signPactModule',
                setting: {
                    isOpenNewPage: false,
                    isCache: false
                },
                param: {
                    type:'creditStartUpBoard',
                    marketType:'sz'
                }
            },
        ]
    },
    //信用创业板新开-开通
    creditStartUpBoardManageOpen: {
        id: '',
        name: 'creditStartUpBoardManageOpen',
        modules: [
            {
                name: 'creditStartUpBoardOpenRetModule',//
                setting: {
                    isOpenNewPage: false,
                    isCache: false
                },
                param: {
                    UniqueKey: "CWCCNO",
                    todoId:'',
                    business_type:'30086',//信用创业板-开通
                    busi_param:'',
                    branch_no: '($khbranch)',
                    client_id: '($usercode)',
                    //fund_account: '($fund_account)',
                    password: "($password)",
                    op_station: "($op_station)",
                }
            }
        ]
    },
    //信用创业板补签流程
    creditStartUpBoardBq: {
        id: '',
        name: 'creditStartUpBoardBq',
        modules: [
            {
                name: 'conditionCheckModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    UniqueKey: "ACU_001",
                    "business_type": "30085",//信用创业板补签
                    "client_id": "($usercode)",
                    "password": "($password)",
                    "op_station": "($op_station)",
                    "pageTitle": "信用创业板"
                },
                result: {}
            },
            {
                name: 'createProcessAndStartModule',
                setting: {
                    isOpenNewPage: false,
                    isCache: false
                },
                param: {
                    UniqueKey: 'ACSO_02',
                    clientId: '($usercode)',
                    businessType: '30085',//信用创业板补签
                    fundAccount: '($credit_fund_account)',
                    password: '($password)',
                    op_station: "($op_station)",
                    //opEntrustWay: '7',
                    userType: '12',
                    userId: '($usercode)',
                    clientName: '($client_name)',
                },
                result: {}
            },        
        ]
    },
    // //信用创业板补签-适当性
    // creditStartUpBoardBqConfirmation: {
    //     id: '',
    //     name: 'creditStartUpBoardBqConfirmation',
    //     modules: [
    //         {
    //             name: 'suitablyModule',
    //             setting: {
    //                 isOpenNewPage: false,
    //                 isCache: false
    //             },
    //             param: {
    //                  type:'creditStartupboardBq',
    //                  marketType:'sz'
    //             }
    //         },
    //     ]
    // },

    // //信用创业板补签-风险揭示书
    // creditStartUpBoardBqSignAgreement: {
    //     id: '',
    //     name: 'creditStartUpBoardBqSignAgreement',
    //     modules: [
    //         {
    //             name: 'signPactModule',
    //             setting: {
    //                 isOpenNewPage: false,
    //                 isCache: false
    //             },
    //             param: {
    //                 type:'creditStartupboardBq',
    //                 marketType:'sz'
    //             }
    //         },
    //     ]
    // },
    //信用创业板补签
    // creditStartUpBoardBqManageOpen: {
    //     id: '',
    //     name: 'creditStartUpBoardBqManageOpen',
    //     modules: [
    //         {
    //             name: 'creditStartUpBoardOpenRetModule',
    //             setting: {
    //                 isOpenNewPage: false,
    //                 isCache: false
    //             },
    //             param: {
    //                 UniqueKey: "CWCCNO",
    //                 todoId:'',
    //                 business_type:'30085',
    //                 busi_param:'',
    //                 branch_no: '($khbranch)',
    //                 client_id: '($usercode)',
    //                 //fund_account: '($fund_account)',
    //                 password: "($password)",
    //                 op_station: "($op_station)",
    //             }
    //         }
    //     ]
    // },
        //
        /**
     * CDR查询
     */
    cdrQuery: {
        id: '',
        name: 'cdrQuery',
        modules: [
            {
                name: 'noticeModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    UniqueKey: 'ANNC_1004',
                    type: '1002',
                    subtype: '********'//todo
                }
            },
            {
                name: 'cdrQueryModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    UniqueKey: 'AIEC_01',//cdr query
                    clientId: '($usercode)',
                    fund_account: '($fund_account)',
                    password: '($password)',
                },
                result: {

                }
            }
        ]
    },
    //CDR开通
    cdrOpen: {
        id: '',
        name: 'cdrOpen',
        modules: [
            {
                name: 'conditionCheckModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    UniqueKey: "ACU_001",
                    "business_type": "30034",//cdr busdines stype
                    "client_id": "($usercode)",
                    "password": "($password)",
                    "op_station":"($op_station)",
                    "pageTitle":"CDR"//PAGE TITLE

                },
                result: {}
            },
            {
                //适当性
                name: 'prefSuitablyModule',
                setting: {
                    isOpenNewPage: false,
                    isCache: false
                },
                param: {
                    UniqueKey: 'CWCPQ',
                    client_id: '($usercode)',
                    fund_account: '($fund_account)',
                    branch_no: '($khbranch)',
                    op_station: "($op_station)",
                    op_entrust_way: '7',
                    password: '($password)',
                    password_type: '2',
                }//cdr 相关参数
            },
            {
                name: 'signPactModule',
                setting: {
                    isOpenNewPage: false,
                    isCache: false
                },
                "param": {}//cdr协议号
            },
            {
                name: 'cdrRetModule',
                setting: {
                    isOpenNewPage: false,
                    isCache: false
                },
                param: {
                    type: 1, //1 开通  0 注销
                    UniqueKey: "AIEC_02",//cdr open
                    client_id: '($usercode)',
                    fund_account: '($fund_account)',
                    password: "($password)",
                    op_station: "($op_station)",
                    branch_no: '($khbranch)',
                }
            }
        ]
    },
    //CDR注销
    cdrCancellation: {
        id: '',
        name: 'cdrCancellation',
        modules: [
            {
                name: "conditionCheckModule",
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    UniqueKey: "ACU_001",
                    "business_type": "30035",//cdr close
                    "client_id": "($usercode)",
                    "password": "($password)",
                    "op_station":"($op_station)",
                    "pageTitle":"CDR"

                },
                result: {}
            },
            {
                "name": "cancelConfirmModule",//取消确认弹框
                setting: {
                    isOpenNewPage: false,
                    isCache: false
                },
                "param": {
                    //rightName:'',//权限名称
                },
                "result": {}
            },
            {
                name: 'cdrRetModule',
                setting: {
                    isOpenNewPage: false,
                    isCache: false
                },
                param: {
                    type: 0, // 0 开通  1 注销
                    UniqueKey: "AIEC_03",
                    client_id: '($usercode)',
                    fund_account: '($fund_account)',
                    password: "($password)",
                    op_station: "($op_station)",
                    branch_no: '($khbranch)',
                }
            }
        ]
    },
    /**
     * 创新企业股票交易权限  查询
     */
    innovationStockQuery: {
        id: '',
        name: 'innovationStockQuery',
        modules: [
            {
                name: 'noticeModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    UniqueKey: 'ANNC_1004',
                    type: '1002',
                    subtype: '********'//
                }
            },
            {
                name: 'innovationStockQueryModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    UniqueKey: 'AIES_01',// innovationStock query
                    clientId: '($usercode)',
                    fund_account: '($fund_account)',
                    password: '($password)',
                },
                result: {

                }
            }
        ]
    },
    //创新企业股票交易权限 开通
    innovationStockOpen: {
        id: '',
        name: 'innovationStockOpen',
        modules: [
            {
                name: 'conditionCheckModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    UniqueKey: "ACU_001",
                    "business_type": "30036",//innovationStock busdines stype
                    "client_id": "($usercode)",
                    "password": "($password)",
                    "op_station":"($op_station)",
                    "pageTitle":"创新企业股票"//PAGE TITLE

                },
                result: {}
            },
            {
                //适当性
                name: 'prefSuitablyModule',
                setting: {
                    isOpenNewPage: false,
                    isCache: false
                },
                param: {
                    UniqueKey: 'CWCPQ',
                    client_id: '($usercode)',
                    fund_account: '($fund_account)',
                    branch_no: '($khbranch)',
                    op_station: "($op_station)",
                    op_entrust_way: '7',
                    password: '($password)',
                    password_type: '2',
                }// innovationStock 相关参数
            },
            {
                name: 'signPactModule',
                setting: {
                    isOpenNewPage: false,
                    isCache: false
                },
                "param": {}//innovationStock协议号
            },
            {
                name: 'innovationStockRetModule',
                setting: {
                    isOpenNewPage: false,
                    isCache: false
                },
                param: {
                    type: 1, //1 开通  0 注销
                    UniqueKey: "AIES_02",// innovationStock open
                    client_id: '($usercode)',
                    fund_account: '($fund_account)',
                    password: "($password)",
                    op_station: "($op_station)",
                    branch_no: '($khbranch)',
                }
            }
        ]
    },
    //创新企业股票交易权限 注销
    innovationStockCancellation: {
        id: '',
        name: 'innovationStockCancellation',
        modules: [
            {
                name: "conditionCheckModule",
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    UniqueKey: "ACU_001",
                    "business_type": "30037",// innovationStock close
                    "client_id": "($usercode)",
                    "password": "($password)",
                    "op_station":"($op_station)",
                    "pageTitle":"创新企业股票"

                },
                result: {}
            },
            {
                "name": "cancelConfirmModule",//取消确认弹框
                setting: {
                    isOpenNewPage: false,
                    isCache: false
                },
                "param": {
                    //rightName:'',//权限名称
                },
                "result": {}
            },
            {
                name: 'innovationStockRetModule',
                setting: {
                    isOpenNewPage: false,
                    isCache: false
                },
                param: {
                    type: 0, // 0 开通  1 注销
                    UniqueKey: "AIES_03",
                    client_id: '($usercode)',
                    fund_account: '($fund_account)',
                    password: "($password)",
                    op_station: "($op_station)",
                    branch_no: '($khbranch)',
                }
            }
        ]
    },
    /**
     *  信用CDR查询
     */
    // creditCdrQuery: {
    //     id: '',
    //     name: 'creditCdrQuery',
    //     modules: [
    //         {
    //             name: 'noticeModule',
    //             setting: {
    //                 isOpenNewPage: true,
    //                 isCache: false
    //             },
    //             param: {
    //                 UniqueKey: 'ANNC_1004',
    //                 type: '1002',
    //                 subtype: '********'//
    //             }
    //         },
    //         {
    //             name: 'creditCdrQueryModule',
    //             setting: {
    //                 isOpenNewPage: true,
    //                 isCache: false
    //             },
    //             param: {
    //                 UniqueKey: 'AIEC_01',//creditCdr query
    //                 clientId: '($usercode)',
    //                 fund_account: '($credit_fund_account)',
    //                 password: '($password)',
    //             },
    //             result: {

    //             }
    //         }
    //     ]
    // },
    // //信用CDR开通
    // creditCdrOpen: {
    //     id: '',
    //     name: 'creditCdrOpen',
    //     modules: [
    //         {
    //             name: 'conditionCheckModule',
    //             setting: {
    //                 isOpenNewPage: true,
    //                 isCache: false
    //             },
    //             param: {
    //                 UniqueKey: "ACU_001",
    //                 "business_type": "30039",//creditCdr busdines stype
    //                 "client_id": "($usercode)",
    //                 "password": "($password)",
    //                 "op_station":"($op_station)",
    //                 "pageTitle":"信用CDR"//PAGE TITLE

    //             },
    //             result: {}
    //         },
    //         {
    //             //适当性
    //             name: 'suitablyModule',
    //             setting: {
    //                 isOpenNewPage: false,
    //                 isCache: false
    //             },
    //             param: {}//creditCdr 相关参数
    //         },
    //         {
    //             name: 'signPactModule',
    //             setting: {
    //                 isOpenNewPage: false,
    //                 isCache: false
    //             },
    //             "param": {}//creditCdr协议号
    //         },
    //         {
    //             name: 'creditCdrRetModule',
    //             setting: {
    //                 isOpenNewPage: false,
    //                 isCache: false
    //             },
    //             param: {
    //                 type: 1, //1 开通  0 注销
    //                 UniqueKey: "AIEC_02",//creditCdr open
    //                 client_id: '($usercode)',
    //                 fund_account: '($credit_fund_account)',
    //                 password: "($password)",
    //                 op_station: "($op_station)",
    //                 branch_no: '($khbranch)',
    //             }
    //         }
    //     ]
    // },
    // //信用CDR注销
    // creditCdrCancellation: {
    //     id: '',
    //     name: 'creditCdrCancellation',
    //     modules: [
    //         {
    //             name: "conditionCheckModule",
    //             setting: {
    //                 isOpenNewPage: true,
    //                 isCache: false
    //             },
    //             param: {
    //                 UniqueKey: "ACU_001",
    //                 "business_type": "30040",//creditCdr close
    //                 "client_id": "($usercode)",
    //                 "password": "($password)",
    //                 "op_station":"($op_station)",
    //                 "pageTitle":"信用CDR"

    //             },
    //             result: {}
    //         },
    //         {
    //             name: 'creditCdrRetModule',
    //             setting: {
    //                 isOpenNewPage: false,
    //                 isCache: false
    //             },
    //             param: {
    //                 type: 0, // 0 开通  1 注销
    //                 UniqueKey: "AIEC_03",
    //                 client_id: '($usercode)',
    //                 fund_account: '($credit_fund_account)',
    //                 password: "($password)",
    //                 op_station: "($op_station)",
    //                 branch_no: '($khbranch)',
    //             }
    //         }
    //     ]
    // },
    /**
     * 信用创新企业股票交易权限 查询
     */
    // creditInnovationStockQuery: {
    //     id: '',
    //     name: 'creditInnovationStockQuery',
    //     modules: [
    //         {
    //             name: 'noticeModule',
    //             setting: {
    //                 isOpenNewPage: true,
    //                 isCache: false
    //             },
    //             param: {
    //                 UniqueKey: 'ANNC_1004',
    //                 type: '1002',
    //                 subtype: '********'//
    //             }
    //         },
    //         {
    //             name: 'creditInnovationStockQueryModule',
    //             setting: {
    //                 isOpenNewPage: true,
    //                 isCache: false
    //             },
    //             param: {
    //                 UniqueKey: 'AIES_01',//creditInnovationStock query
    //                 clientId: '($usercode)',
    //                 fund_account: '($credit_fund_account)',
    //                 password: '($password)',
    //             },
    //             result: {

    //             }
    //         }
    //     ]
    // },
    // //信用创新企业股票交易权限 开通
    // creditInnovationStockOpen: {
    //     id: '',
    //     name: 'creditInnovationStockOpen',
    //     modules: [
    //         {
    //             name: 'conditionCheckModule',
    //             setting: {
    //                 isOpenNewPage: true,
    //                 isCache: false
    //             },
    //             param: {
    //                 UniqueKey: "ACU_001",
    //                 "business_type": "30041",//creditInnovationStock busdines stype
    //                 "client_id": "($usercode)",
    //                 "password": "($password)",
    //                 "op_station":"($op_station)",
    //                 "pageTitle":"信用创新企业股票"//PAGE TITLE

    //             },
    //             result: {}
    //         },
    //         {
    //             //适当性
    //             name: 'suitablyModule',
    //             setting: {
    //                 isOpenNewPage: false,
    //                 isCache: false
    //             },
    //             param: {}// creditInnovationStock 相关参数
    //         },
    //         {
    //             name: 'signPactModule',
    //             setting: {
    //                 isOpenNewPage: false,
    //                 isCache: false
    //             },
    //             "param": {}// creditInnovationStock协议号
    //         },
    //         {
    //             name: 'creditInnovationStockRetModule',
    //             setting: {
    //                 isOpenNewPage: false,
    //                 isCache: false
    //             },
    //             param: {
    //                 type: 1, //1 开通  0 注销
    //                 UniqueKey: "AIES_02",// creditInnovationStock open
    //                 client_id: '($usercode)',
    //                 fund_account: '($credit_fund_account)',
    //                 password: "($password)",
    //                 op_station: "($op_station)",
    //                 branch_no: '($khbranch)',
    //             }
    //         }
    //     ]
    // },
    // //信用创新企业股票交易权限 注销
    // creditInnovationStockCancellation: {
    //     id: '',
    //     name: 'creditInnovationStockCancellation',
    //     modules: [
    //         {
    //             name: "conditionCheckModule",
    //             setting: {
    //                 isOpenNewPage: true,
    //                 isCache: false
    //             },
    //             param: {
    //                 UniqueKey: "ACU_001",
    //                 "business_type": "30042",//creditInnovationStock close
    //                 "client_id": "($usercode)",
    //                 "password": "($password)",
    //                 "op_station":"($op_station)",
    //                 "pageTitle":"信用创新企业股票"

    //             },
    //             result: {}
    //         },
    //         {
    //             name: 'creditInnovationStockRetModule',
    //             setting: {
    //                 isOpenNewPage: false,
    //                 isCache: false
    //             },
    //             param: {
    //                 type: 0, // 0 开通  1 注销
    //                 UniqueKey: "AIES_03",
    //                 client_id: '($usercode)',
    //                 fund_account: '($credit_fund_account)',
    //                 password: "($password)",
    //                 op_station: "($op_station)",
    //                 branch_no: '($khbranch)',

    //             }
    //         }
    //     ]
    // },
    //信用创业板补签告知书
    creditStartUpBqConfirmForWeb: {
        id: '',
        name: 'creditStartUpBqConfirmForWeb',
        modules: [
            {
                name: "creditStartUpBqConfirmModule",
                setting: {
                    isOpenNewPage: false,
                    isCache: false
                },
                param: {
                    // UniqueKey: "ACU_001",
                    // "business_type": "30042",//creditInnovationStock close
                    // "client_id": "($usercode)",
                    // "password": "($password)",
                    // "op_station":"($op_station)",
                    // "pageTitle":"信用创新企业股票"
                },
                result: {}
            }
        ]
    },

    //财人汇-专业投资者
    professionalInvestor: {
        id: '',
        name: 'professionalInvestor',
        modules: [
            {
                name: 'noticeModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    UniqueKey: 'ANNC_1004',
                    type: '1002',
                    subtype: '10020044'//
                }
            },
            {
                name: 'goCRHModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    pageType:'professionalInvestor'
                }
            },
        ]
    },
    //财人汇-开通两融合约展期自动申请权限
    marginRenewal: {
        id: '',
        name: 'marginRenewal',
        modules: [
            {
                name: 'noticeModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    UniqueKey: 'ANNC_1004',
                    type: '1002',
                    subtype: '10020069'
                }
            },
            {
                name: 'goCRHModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    pageType:'marginRenewal',
                }
            }
        ]
    },  
    //财人汇-全线上开户
    onLineOpenStock: {
        id: '',
        name: 'onLineOpenStock',
        modules: [
            {
                name: 'noticeModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    UniqueKey: 'ANNC_1004',
                    type: '1002',
                    subtype: '10020069'
                }
            },
            {
                name: 'goCRHModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    pageType:'onLineOpenStock',
                }
            }
        ]
    },  
    //财人汇-中签资金预冻结
    fundPrefrozen: {
        id: '',
        name: 'fundPrefrozen',
        modules: [
            {
                name: 'noticeModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    UniqueKey: 'ANNC_1004',
                    type: '1002',
                    subtype: '10020065'
                }
            },
            {
                name: 'goCRHModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    pageType:'fundPrefrozen',
                }
            }
        ]
    },  
    //财人汇-开通报价回购权限
    quoteRepurchase: {
        id: '',
        name: 'quoteRepurchase',
        modules: [
            {
                name: 'noticeModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    UniqueKey: 'ANNC_1004',
                    type: '1002',
                    subtype: '10020064'
                }
            },
            {
                name: 'goCRHModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    pageType:'quoteRepurchase',
                }
            }
        ]
    }, 
    //财人汇-可转债
    holderRights: {
        id: '',
        name: 'holderRights',
        modules: [
            {
                name: 'noticeModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    UniqueKey: 'ANNC_1004',
                    type: '1002',
                    subtype: '********'//
                }
            },
            {
                name: 'goCRHModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    pageType:'holderRights',
                }
            }
        ]
    },
    //财人汇-开通行权融资业务权限
    exerciseFinancing: {
        id: '',
        name: 'exerciseFinancing',
        modules: [
            {
                name: 'noticeModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    UniqueKey: 'ANNC_1004',
                    type: '1002',
                    subtype: '10020066'
                }
            },
            {
                name: 'goCRHModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    pageType:'exerciseFinancing',
                }
            }
        ]
    }, 
    //财人汇-股权激励授权签署
    equityIncentiveSign: {
        id: '',
        name: 'equityIncentiveSign',
        modules: [
            {
                name: 'noticeModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    UniqueKey: 'ANNC_1004',
                    type: '1002',
                    subtype: '10020070'
                }
            },
            {
                name: 'goCRHModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    pageType:'equityIncentiveSign',
                }
            }
        ]
    }, 
    //财人汇-两融预约
    marginTradeReserve: {
        id: '',
        name: 'marginTradeReserve',
        modules: [
            {
                name: 'noticeModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    UniqueKey: 'ANNC_1004',
                    type: '1002',
                    subtype: '********'//
                }
            },
            {
                name: 'checkSdVersionModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    version: '8.02.002' // 该业务支持的客户端版本
                }
            },
            {
                name: 'goSdModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    specialLink: 'lrIntroduce' // 特殊路由
                }
            }
        ]
    },  
    //财人汇-特转A账户开通
    specialRights: {
        id: '',
        name: 'specialRights',
        modules: [
            {
                name: 'noticeModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    UniqueKey: 'ANNC_1004',
                    type: '1002',
                    subtype: '10020062'
                }
            },
            {
                name: 'goCRHModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    pageType:'specialRights',
                }
            }
        ]
    },   

    //财人汇-退市板块权限开通
    delistRights: {
        id: '',
        name: 'delistRights',
        modules: [
            {
                name: 'noticeModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    UniqueKey: 'ANNC_1004',
                    type: '1002',
                    subtype: '10020063'
                }
            },
            {
                name: 'goCRHModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    pageType:'delistRights',
                }
            }
        ]
    },   

    // 开通可转债退市整理权限
    delistHolderRights: {
        id: '',
        name: 'delistHolderRights',
        modules: [
            {
                name: 'noticeModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    UniqueKey: 'ANNC_1004',
                    type: '1002',
                    subtype: '********',
                }
            },
            {
                name: 'delistHolderRightsModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    pageType:'delistHolderRights',
                    type:'delistHolderRights',
                }
            }
        ]
    },   
    // 开通可转债退市整理权限   开通
    delistHolderRightsOpen: {
        id: '',
        name: 'delistHolderRightsOpen',
        modules: [
            {
                name: "conditionCheckModule",
                setting: {
                    isOpenNewPage: true,
                    isCache: false,
                },
                param: {
                    UniqueKey: 'ACU_001',
                    business_type: '30114', 
                    client_id: '($usercode)',
                    password: '($password)',
                    op_station: '($op_station)',
                    pageTitle: '可转债退市整理权限',
                },
                result: {},
            },
            {
                name: 'createProcessAndStartModule',
                setting: {
                    isOpenNewPage: false,
                    isCache: false
                },
                param: {
                    UniqueKey: 'ACSO_02',
                    clientId: '($usercode)',
                    businessType: '30114',
                    fundAccount: '($fund_account)',
                    password: '($password)',
                    op_station: "($op_station)",
                    opEntrustWay: '7',
                    branch_no: '($khbranch)',
                    userType: '12',
                    userId: '($usercode)',
                    clientName: '($client_name)',
                },
                result: {}
            },   
        ]
    },   
    //开通可转债退市整理权限-适当性
    delistHolderRightsSuitably: {
        id: '',
        name: 'delistHolderRightsSuitably',
        modules: [
            {
                name: 'prefSuitablyModule',
                setting: {
                    isOpenNewPage: false,
                    isCache: false
                },
                param: {
                    UniqueKey: 'CWCPQ',
                    client_id: '($usercode)',
                    fund_account: '($fund_account)',
                    branch_no: '($khbranch)',
                    op_station: "($op_station)",
                    op_entrust_way: '7',
                    password: '($password)',
                    password_type: '2',
                    type:'delistHolderRights',
                    marketType:'sz'
                }
            },
        ]
    },
    //开通可转债退市整理权限-协议签署
    delistHolderRightsAgreement: {
        id: '',
        name: 'delistHolderRightsAgreement',
        modules: [
            {
                name: 'signPactModule',
                setting: {
                    isOpenNewPage: false,
                    isCache: false
                },
                param: {
                    type:'delistHolderRights',
                    marketType:'sz'
                }
            },
        ]
    },
    //开通可转债退市整理权限-权限开通
    delistHolderRightsApply :{
        id: '',
        name: 'delistHolderRightsApply',
        modules: [
            {
                name: 'delistHolderRightsRetModule',
                setting: {
                    isOpenNewPage: false,
                    isCache: false
                },
                param: {
                    UniqueKey: "CWSRKOS",
                    todoId:'',
                    branch_no: '($khbranch)',
                    client_id: '($usercode)',
                    fund_account: '($fund_account)',
                    stock_accounts:'',
                    busi_param:'',
                    password: "($password)",
                    op_station: "($op_station)",
                }
            }
        ]
    },
    // 可转债退市整理权限 -- 取消权限
    delistHolderRightsCancel :{
        id: '',
        name: 'delistHolderRightsCancel',
        modules: [
            {
                name: 'delistHolderRightsCancelModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    pageType:'delistHolderRightsCancel',
                    type:'delistHolderRightsCancel',
                }
            }
        ]
    },
    // 可转债退市整理权限 -- 取消权限结果页
    delistHolderRightsCancelRet :{
        id: '',
        name: 'delistHolderRightsCancelRet',
        modules: [
            {
                name: "conditionCheckModule",
                setting: {
                    isOpenNewPage: false,
                    isCache: false,
                },
                param: {
                    UniqueKey: 'ACU_001',
                    business_type: '30116', 
                    client_id: '($usercode)',
                    password: '($password)',
                    op_station: '($op_station)',
                    pageTitle: '可转债退市整理权限',
                },
                result: {},
            },
            {
                name: 'delistHolderRightsCancelRetModule',
                setting: {
                    isOpenNewPage: false,
                    isCache: false
                },
                param: {
                    UniqueKey: 'CWSRKC',
                    branch_no: '($khbranch)',
                    client_id: '($usercode)',
                    fund_account: '($fund_account)',
                    password: '($password)',
                    op_station: '($op_station)',
                }
            }
        ]
    },

   
    //独立条件检查功能，用于理财商城反洗钱迭代
    singleConditionCheck:{
        id: '',
        name: 'singleConditionCheck',
        modules: [
            {
                name: "singleConditionCheckPreModule",
                setting: {
                    isOpenNewPage: false,
                    isCache: false
                },
                param: {
                },
                result: {}
            },
        ]
    },
    // singleCheck: {
    //     id: '',
    //     name: 'singleCheck',
    //     modules: [
    //         {
    //             name: "conditionCheckModule",
    //             setting: {
    //                 isOpenNewPage: true,
    //                 isCache: false
    //             },
    //             param: {
    //                 UniqueKey: "ACU_001",
    //                 "business_type": "30091",//todo
    //                 "client_id": "($usercode)",
    //                 "password": "($password)",
    //                 "op_station":"($op_station)",
    //                 "pageTitle":"todo "//todo

    //             },
    //             result: {}
    //         },
    //         {
    //             name: 'goBackModule',
    //             setting: {
    //                 isOpenNewPage: false,
    //                 isCache: false
    //             },
    //             param: {
    //             }
    //         }
    //     ]
    // },
    // 五层四级调价，佣金、融资利率调整
    priceAdjustConfirm: {
        id: '',
        name: 'priceAdjustConfirm',
        modules: [
            {
                name: 'priceAdjustConfirmModule',
                setting: {
                    isOpenNewPage: false,
                    isCache: false
                },
                param: {},
                result: {}
            }
        ]
    },
    // 专项头寸权限-查询
    specialSecuritiesQuery: {
        id: '',
        name: 'specialSecuritiesQuery',
        modules: [
            {
                name: 'noticeModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false,
                },
                param: {
                    UniqueKey: 'ANNC_1004',
                    type: '1002',
                    subtype: '********',
                },
            },
            {
                name: 'specialSecuritiesQueryModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false,
                },
                param: {
                    UniqueKey: 'CWSSI',
                    branch_no: '($khbranch)',
                    client_id: '($usercode)',
                    fund_account: '($fund_account)',
                    password: '($password)',
                    op_station: '($op_station)',
                },
                result: {},
            },
        ],
    },
    // 专项头寸权限-条件检查、创建预约单
    specialSecuritiesPre: {
        id: '',
        name: 'specialSecuritiesPre',
        modules: [
            {
                name: 'conditionCheckModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false,
                },
                param: {
                    UniqueKey: 'ACU_001',
                    business_type: '30095',
                    client_id: '($usercode)',
                    password: '($password)',
                    op_station: '($op_station)',
                    pageTitle: '专项头寸权限',
                },
                result: {},
            },
            {
                name: 'createProcessAndStartModule',
                setting: {
                    isOpenNewPage: false,
                    isCache: false,
                },
                param: {
                    UniqueKey: 'ACSO_02',
                    clientId: '($usercode)',
                    businessType: '30095',
                    fundAccount: '($fund_account)',
                    password: '($password)',
                    op_station: "($op_station)",
                    //opEntrustWay: '7',
                    userType: '12',
                    userId: '($usercode)',
                    clientName: '($client_name)',
                },
                result: {},
            },
        ],
    },
    // 专项头寸权限-签署风险揭示书、推动待办
    specialSecuritiesSignAgreement: {
        id: '',
        name: 'specialSecuritiesSignAgreement',
        modules: [
            {
                name: 'signPactModule',
                setting: {
                    isOpenNewPage: false,
                    isCache: false,
                },
                param: {
                    type: 'specialSecurities',
                    marketType: 'sh',
                },
            },
        ],
    },
    // 专项头寸权限-开通、推动待办
    specialSecuriManageOpen: {
        id: '',
        name: 'specialSecuriManageOpen',
        modules: [
            {
                name: 'specialSecuritiesRetModule',
                setting: {
                    isOpenNewPage: false,
                    isCache: false,
                },
                param: {
                    UniqueKey: 'CWSSOS',
                    todoId: '',
                    busi_param: '',
                    branch_no: '($khbranch)',
                    client_id: '($usercode)',
                    fund_account: '($credit_fund_account)',
                    password: "($password)",
                    op_station: "($op_station)",
                },
            },
        ],
    },
    // 专项头寸权限-注销
    specialSecuritiesCancellation: {
        id: '',
        name: 'specialSecuritiesCancellation',
        modules: [
            {
                name: "conditionCheckModule",
                setting: {
                    isOpenNewPage: true,
                    isCache: false,
                },
                param: {
                    UniqueKey: 'ACU_001',
                    business_type: '30096',
                    client_id: '($usercode)',
                    password: '($password)',
                    op_station: '($op_station)',
                    pageTitle: '专项头寸权限',
                },
                result: {},
            },
            {
                name: 'cancelConfirmModule', // 取消确认弹框
                setting: {
                    isOpenNewPage: false,
                    isCache: false,
                },
                param: {
                    rightName: '专项头寸权限', // 权限名称
                },
                result: {},
            },
            {
                name: 'specialSecuritiesCancelRetModule',
                setting: {
                    isOpenNewPage: false,
                    isCache: false,
                },
                param: {
                    UniqueKey: 'CWSSC',
                    branch_no: '($khbranch)',
                    client_id: '($usercode)',
                    fund_account: '($credit_fund_account)',
                    password: "($password)",
                    op_station: "($op_station)",
                },
            },
        ],
    },

    crhInfrastructureFund: {
        id: '',
        name: 'crhInfrastructureFund',
        modules: [
            {
                name: 'noticeModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    UniqueKey: 'ANNC_1004',
                    type: '1002',
                    subtype: '********'//基础设施基金
                }
            },
            {
                name: 'goCRHModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    pageType:'crhInfrastructureFund',
                }
            }
        ]
    },      

    crhStockOptions: {
        id: '',
        name: 'crhStockOptions',
        modules: [
            {
                name: 'noticeModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    UniqueKey: 'ANNC_1004',
                    type: '1002',
                    subtype: '10020053'
                }
            },
            {
                name: 'goCRHModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    pageType:'crhStockOptions',
                }
            }
        ]
    },      
    crhFundOpenTpye: {
        id: '',
        name: 'crhFundOpenTpye',
        modules: [
            {
                name: 'noticeModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    UniqueKey: 'ANNC_1004',
                    type: '1002',
                    subtype: '10020054'
                }
            },
            {
                name: 'goCRHModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    pageType:'crhFundOpenTpye',
                }
            }
        ]
    },
    crhTaRelationInfo: {
        id: '',
        name: 'crhTaRelationInfo',
        modules: [
            {
                name: 'noticeModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    UniqueKey: 'ANNC_1004',
                    type: '1002',
                    subtype: '********'//todo
                }
            },
            {
                name: 'goCRHModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    pageType:'crhTaRelationInfo',
                }
            }
        ]
    },      
    crhCheckAccount: {
        id: '',
        name: 'crhCheckAccount',
        modules: [
            {
                name: 'noticeModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    UniqueKey: 'ANNC_1004',
                    type: '1002',
                    subtype: '********'//todo
                }
            },
            {
                name: 'goCRHModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    pageType:'crhCheckAccount',
                }
            }
        ]
    },
    holderRightsSecond: {
        id: '',
        name: 'holderRightsSecond',
        modules: [
            // {
            //     name: 'noticeModule',
            //     setting: {
            //         isOpenNewPage: true,
            //         isCache: false
            //     },
            //     param: {
            //         UniqueKey: 'ANNC_1004',
            //         type: '1002',
            //         subtype: '********'//
            //     }
            // },
            {
                name: 'goCRHModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    pageType:'holderRights',
                    hostType:'second'
                }
            }
        ]
    },      
    crhOpenConfirm: {
        id: '',
        name: 'crhOpenConfirm',
        modules: [
            {
                name: 'noticeModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    UniqueKey: 'ANNC_1004',
                    type: '1002',
                    subtype: '10020056'
                }
            },
            {
                name: 'goCRHModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    pageType:'crhOpenConfirm',
                }
            }
        ]
    },      
    //理财商城，场外基金转场内
    "mallFundTransfer": {
        id: '',
        name: 'mallFundTransfer',
        "modules": [
            // {
            //     "name": "noticeModule",
            //     setting: {
            //         isOpenNewPage: true,
            //         isCache: false
            //     },
            //     "param": {
            //         UniqueKey: 'ANNC_1004',
            //         type: '1002',
            //         subtype: '********'
            //     },
            //     "result": {}
            // },
            {
                "name": "mallFundTransferModule",
                "setting": {
                    isOpenNewPage: true,
                    isCache: false
                },
                "param": {},
                "result": {}
            },
        ]
    },

    //补开股东户-活体检测
    liveDetection: {
        id: '',
        name: 'liveDetection',
        modules: [
            {
                name: 'liveDetectionModule',
                setting: {
                    isOpenNewPage: false,
                    isCache: true,
                },
                param: {
                },
            },
        ],
    },
    //补开股东户-手机号码校验
    openStockCheckPhone: {
        id: '',
        name: 'openStockCheckPhone',
        modules: [
            {
                name: 'investorPhoneVerifyModule',
                setting: {
                    isOpenNewPage: false,
                    isCache: false
                },
                param: {
                    template_no: '26',//短信验证码模板
                    pageTitle: 'A股账户',
                    loggerCollectConfig: {
                        event_id_label: 'sms_validate',
                        logtype: 'cancel_sms_validate'
                    },
                },
                result: {}
            }
        ]
    },
    // 税收居民身份信息
    taxInfo: {
        id: '',
        name: 'taxInfo',
        modules: [
            {
                name: 'conditionCheckModule',
                setting: {
                    isOpenNewPage: false,
                    isCache: false
                },
                param: {
                    UniqueKey: "ACU_001",
                    "business_type": "30011", // 个人资料
                    "client_id": "($usercode)",
                    "password": "($password)",
                    "op_station": "($op_station)",
                    "pageTitle": "税收居民身份信息"
                },
                result: {}
            },
            {
                name: 'taxInfoModule',
                setting: {
                    isOpenNewPage: false,
                    isCache: false
                },
                param: {},
                result: {}
            }
        ]
    },
    //修改交易密码，webapps版
    changeTradePassword: {
        id: '',
        name: 'changeTradePassword',
        modules: [
            {
                name: 'noticeModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    UniqueKey: 'ANNC_1004',
                    type: '1002',
                    subtype: '10020019'
                },
                result: {}
            },
            {
                name: 'conditionCheckModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    UniqueKey: "ACU_001",
                    "business_type": "30012",//修改密码
                    "client_id": "($usercode)",
                    "password": "($password)",
                    "op_station": "($op_station)",
                    "pageTitle": "修改密码"
                },
                result: {}
            },
            {
                name: 'changeTradePasswordModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {},
                result: {}
            }
        ]
    },
    //修改资金密码，webapps版
    changeFundPassword: {
        id: '',
        name: 'changeFundPassword',
        modules: [
            {
                name: 'noticeModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    UniqueKey: 'ANNC_1004',
                    type: '1002',
                    subtype: '10020019'
                },
                result: {}
            },
            {
                name: 'conditionCheckModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    UniqueKey: "ACU_001",
                    "business_type": "30012",//修改密码
                    "client_id": "($usercode)",
                    "password": "($password)",
                    "op_station": "($op_station)",
                    "pageTitle": "修改密码"
                },
                result: {}
            },
            {
                name: 'changeFundPasswordModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {},
                result: {}
            }
        ]
    },
    // 关联关系确认
    associationConfirm: {
        id: '',
        name: 'associationConfirm',
        modules: [
            {
                name: 'noticeModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    UniqueKey: 'ANNC_1004',
                    type: '1002',
                    subtype: '10020059'
                }
            },
            {
                name: 'associationConfirmModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {},
                result: {}
            }
        ]
    },
    associationList: {
        id: '',
        name: 'associationList',
        modules: [
            {
                name: 'associationListModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {},
                result: {}
            }
        ]
    },
    // 无持仓证明
    positionProve: {
        id: '',
        name: 'positionProve',
        modules: [
            {
                name: 'positionProveModule',
                setting: {
                    isOpenNewPage: false,
                    isCache: false
                },
                param: {},
                result: {}
            }
        ]
    },
    //关联关系确认--流程检查
    associationConfirmCheck: {
        id: '',
        name: 'associationConfirmCheck',
        modules: [
            {
                name: 'conditionCheckModule',
                setting: {
                    isOpenNewPage: false,
                    isCache: false
                },
                param: {
                    UniqueKey: "ACU_001",
                    "business_type": "30104", // 关联关系确认
                    "client_id": "($usercode)",
                    "password": "($password)",
                    "op_station": "($op_station)",
                    "pageTitle": "关联关系确认"
                },
                result: {}
            },
            {
                name: 'createACPrAeEIBusinessInfoModule',
                setting: {
                    isOpenNewPage: false,
                    isCache: false
                },
                param: {},
                result: {}
            }
        ]
    },
    associationConfirmSignAgreement: {
        id: '',
        name: 'associationConfirmSignAgreement',
        modules: [
            {
                name: 'signPactModule',
                setting: {
                    isOpenNewPage: false,
                    isCache: false
                },
                param: {
                    type:'associationConfirm',
                    marketType:'sz'
                }
            },
        ]
    },
    // 关联关系结果页
    associationConfirmResult: {
        id: '',
        name: 'associationConfirmResult',
        modules: [
            {
                name: 'associationConfirmResultModule',
                setting: {
                    isOpenNewPage: false,
                    isCache: false
                },
                param: {},
                result: {}
            }
        ]
    },
    // 业务办理进度查询----账户权限视图
    searchProgress: {
        id: '',
        name: 'searchProgress',
        modules: [
            {
                name: 'noticeModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    UniqueKey: 'ANNC_1004',
                    type: '1002',
                    subtype: '********'
                }
            },
            {
                name: 'searchProgressModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {},
                result: {}
            }
        ]
    },
    // 双元对账单
    checkAccount: {
        id: '',
        name: 'checkAccount',
        modules: [
            {
                name: 'checkAccountModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {},
                result: {}
            }
        ]
    },
    checkAccountResult: {
        id: '',
        name: 'checkAccountResult',
        modules: [
            {
                name: 'checkAccountResultModule',
                setting: {
                    isOpenNewPage: false,
                    isCache: false
                },
                param: {},
                result: {}
            }
        ]
    },
    //信用北交所
    creditStockBeiQuery: {
        id: '',
        name: 'creditStockBeiQuery',
        modules: [
            {
                name: 'noticeModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    UniqueKey: 'ANNC_1004',
                    type: '1002',
                    subtype: '********'
                }
            },
            {
                name: 'creditStockBeiQueryModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    UniqueKey: 'CWSRCBI', // 待补充
                    branch_no:'($khbranch)',
                    clientId: '($usercode)',
                    fund_account: '($fund_account)',
                    password: '($password)',
                    // password_type:'',
                    op_entrust_way:'7',// 默认7
                    op_station:"($op_station)",
                },
                result: {}
            },     
        ]
    },
    // 信用北交所开通
    creditStockBeiOpen:{
        id: '',
        name: 'creditStockBeiOpen',
        modules: [
            // 条件检查
            {
                name: 'conditionCheckModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    UniqueKey: "ACU_001",
                    "business_type": "30110",// 信用北交所
                    "client_id": "($usercode)",
                    "password": "($password)",
                    "op_station": "($op_station)",
                    "fund_account": '($fund_account)',
                    "pageTitle": "信用北交所权限"

                },
                result: {}
            },
            // 进入投教音频页面
            {
                name:'creditStockBeiAudioVerdictModule',
                setting:{
                    isOpenNewPage: false,
                    isCache: false
                },
                param:{},
                result: {}
            },
            {
                name:'creditStockBeiAudioModule',
                setting:{
                    isOpenNewPage: false,
                    isCache: false
                },
                param:{},
                result: {}
            },
            // 是否需要密码
            {
                name: 'needInputCreditInfoModule',
                setting: {
                    isOpenNewPage: false,
                    isCache: false
                },
                param: {
                    UniqueKey: "UAGPDF",
                    "client_id": "($usercode)"

                },
                result: {}
            },
            // 进入密码页面
            {
                name: 'inputCreditInfoModule',
                setting: {
                    isOpenNewPage: false,
                    isCache: false
                },
                param: {
                    UniqueKey: 'CWCSINFO',
                    clientId: '($usercode)',
                    fund_account: '($credit_fund_account)',
                    password: '($password)',
                    branch_no: '($khbranch)',
                    pageTitle: '信用北交所权限'
                },
                result: {}
            },
             // 创建预约单
             {
                name: 'createPreEIBusinessInfoModule',
                setting: {
                    isOpenNewPage: false,
                    isCache: false
                },
                param: {
                    UniqueKey: 'ACSO_02',
                    clientId: '($usercode)',
                    businessType: '30110',
                    fundAccount: '($credit_fund_account)',
                    password: '($password)',
                    op_station: "($op_station)",
                    opEntrustWay: '7',
                    branch_no: '($khbranch)',
                    userType: '12',
                    userId: '($usercode)',
                    clientName: '($client_name)',
                },
                result: {}
            },  
        ]
    },

    //信用北交所-特转A
    creditStockBeiSpecial: {
        "id": "",
        "name": "creditStockBeiSpecial",
        "modules": [
            {
                "name": "creditStockBeiSpecialModule",
                setting: {
                    isOpenNewPage: false,
                    isCache: false
                },
                "param": {},
            }
        ]
    },

    //信用北交所-董监高信息收集
    creditStockBeiInfoCollect: {
        "id": "",
        "name": "creditStockBeiInfoCollect",
        "modules": [
            {
                "name": "creditStockBeiInfoCollectModule",
                setting: {
                    isOpenNewPage: false,
                    isCache: false
                },
                "param": {},
            }
        ]
    },
    //信用北交所-适当性
    creditStockBeiConfirmation: {
        id: '',
        name: 'creditStockBeiConfirmation',
        modules: [
            {
                name: 'suitablyModule',
                setting: {
                    isOpenNewPage: false,
                    isCache: false
                },
                param: {
                     type:'creditStockBei',
                     marketType:'sz'
                }
            },
        ]
    },
    //信用北交所-协议签署
    creditStockBeiSignAgreement: {
        id: '',
        name: 'creditStockBeiSignAgreement',
        modules: [
            {
                name: 'signPactModule',
                setting: {
                    isOpenNewPage: false,
                    isCache: false
                },
                param: {
                    type:'creditStockBei',
                    marketType:'sz'
                }
            },
        ]
    },
    // 信用北交所开通并推动代办
    creditStockBeiManagerOpen: {
        id: '',
        name: 'creditStockBeiManagerOpen',
        modules: [
            {
                name: 'creditStockBeiOpenRetModule',
                setting: {
                    isOpenNewPage: false,
                    isCache: false
                },
                param: {
                    UniqueKey: "CWCSRBOS",
                    todoId:'',
                    busi_param:'',
                    branch_no: '($khbranch)',
                    client_id: '($usercode)',
                    fund_account: '($credit_fund_account)',
                    stock_account:'',// 信用主股东,module 取缓存
                    exchange_type:9,
                    relationship_acct:"", // 传绑定的北交所账户，module取缓存
                    password_type:"2", // 默认，交易密码
                    password: '($password)',
                    op_entrust_way: '7',
                    op_station: "($op_station)",
                }
            }
        ]
    },
    reserveSHOptAccount_sd: {
        id: '',
        name: 'reserveSHOptAccount',
        modules: [
            {
                name: 'noticeModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    UniqueKey: 'ANNC_1004',
                    type: '1002',
                    subtype: '********'
                }
            },
            {
                name: 'checkSdVersionModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    version: '8.01.004' // 该业务支持的客户端版本
                }
            },
            {
                name: 'goSdModule',
                setting: {
                    isOpenNewPage: true,
                    isCache: false
                },
                param: {
                    bizType:'010169'
                }
            }
        ]
    },
};

var _groupConfig = JSON.parse(JSON.stringify(groupConfig));

if(window) window.groupConfig = groupConfig;
if(window) window._groupConfig = _groupConfig;