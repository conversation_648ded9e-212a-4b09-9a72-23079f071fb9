const MSG_DICT = {
    ALLTIME_97:
        "<p>当前是清算时间，无法查询该信息。您可以继续完成申请，我司将在下一交易日核实该信息后再为您办理权限开通。</p>", //7*24业务-清算提示话术
};

class ConditionCheckDict {
    constructor() {
        //账户类型
        this.custTypePersonal = {
            title: "账户必须是个人客户",
            remarks: "客户类型（个人）",
            renderWhenPass: false,
            "0": {
                description: {
                    default:
                        "<p>本业务目前仅支持自然人账户线上办理。其他账户类型的客户，请前往就近营业部临柜办理。感谢您的支持！</p>",
                    //重置密码
                    "30032":
                        "<p>亲，宝宝仅能为个人客户办理在线密码重置业务</p>",
                    "30033":
                        "<p>亲，宝宝仅能为个人客户办理在线密码重置业务</p>",
                },
                link_description: "",
                link: "",
            },
            "1": {},
        };
        this.custTypeOrgan = {
            title: "客户类型（机构）",
            remarks: "客户类型（机构）",
            renderWhenPass: false,
            "0": {
                description: "<p>不满足</p>" + "<p></p>",
                link_description: "",
                link: "",
            },
            "1": {},
        };
        //业务相关
        //业务办理app无后续操作，交易app跳转至银证转账模块
        //日均资产
        this.averageAssetsDelist = {
            title: "近20个交易日日均资产不低于50万元",
            remarks: "开通退市整理权限",
            renderWhenPass: true,
            "0": {
                description:
                    "<p>您的账户近20个交易日日均资产为：{{assets}}元</p>" +
                    "<p>温馨提示：银证转账后次日再来试试！</p>",
                link_description: "",
                link: "",
            },
            "1": {},
        };
        {
            //根据系统版本设定银证转账参数。
            if (Controller.getAppId() == "yjb3.0") {
                this.averageAssetsDelist["0"].link_description = "银证转账";
                this.averageAssetsDelist["0"].link = "bankTransferV3";
            } else if (Controller.getAppId() == "yjbjyd") {
                this.averageAssetsDelist["0"].link_description = "银证转账";
                this.averageAssetsDelist["0"].link = "bankTransferV2";
            } else {
            }
        }
        this.averageAssetsCredit = {
            title: "近20个交易日日均资产不低于50万元",
            remarks: "融资融券开户",
            renderWhenPass: true,
            "0": {
                description:
                    "<p>您的账户近20个交易日日均资产为：{{assets}}元</p>" +
                    "<p>温馨提示：银证转账后次日再来试试！</p>",
                link_description: "",
                link: "",
            },
            "1": {},
        };
        {
            //根据系统版本设定银证转账参数。
            if (Controller.getAppId() == "yjb3.0") {
                this.averageAssetsCredit["0"].link_description = "银证转账";
                this.averageAssetsCredit["0"].link = "bankTransferV3";
            } else if (Controller.getAppId() == "yjbjyd") {
                this.averageAssetsCredit["0"].link_description = "银证转账";
                this.averageAssetsCredit["0"].link = "bankTransferV2";
            } else {
            }
        }
        this.averageAssetsHKStock = {
            title: "证券账户及资金账户近20个交易日日均资产不低于50万元",
            remarks: "开通港股通权限",
            renderWhenPass: true,
            "0": {
                description:
                    "<p>您的账户近20个交易日日均资产为：{{assets}}元</p>" +
                    "<p>根据交易所规定，信用负债、OTC资产和部分场外基金账户资产不包含在内。您可以咨询在线客服或95310热线了解详情。</p>" +
                    "<p>温馨提示：银证转账后次日再来试试！</p>",
                link_description: "",
                link: "",
            },
            "1": {},
        };
        {
            //根据系统版本设定银证转账参数。
            if (Controller.getAppId() == "yjb3.0") {
                this.averageAssetsHKStock["0"].link_description = "银证转账";
                this.averageAssetsHKStock["0"].link = "bankTransferV3";
            } else if (Controller.getAppId() == "yjbjyd") {
                this.averageAssetsHKStock["0"].link_description = "银证转账";
                this.averageAssetsHKStock["0"].link = "bankTransferV2";
            } else {
            }
        }
        this.averageAssetsShHKStock = {
            title: "证券账户及资金账户近20个交易日日均资产不低于50万元",
            remarks: "开通沪港通权限",
            renderWhenPass: true,
            "0": {
                description:
                    "<p>您的账户近20个交易日日均资产为：{{assets}}元</p>" +
                    "<p>根据交易所规定，信用负债、OTC资产和部分场外基金账户资产不包含在内。您可以咨询在线客服或95310热线了解详情。</p>" +
                    "<p>温馨提示：银证转账后次日再来试试！</p>",
                link_description: "",
                link: "",
            },
            "1": {},
        };
        {
            //根据系统版本设定银证转账参数。
            if (Controller.getAppId() == "yjb3.0") {
                this.averageAssetsShHKStock["0"].link_description = "银证转账";
                this.averageAssetsShHKStock["0"].link = "bankTransferV3";
            } else if (Controller.getAppId() == "yjbjyd") {
                this.averageAssetsShHKStock["0"].link_description = "银证转账";
                this.averageAssetsShHKStock["0"].link = "bankTransferV2";
            } else {
            }
        }
        this.averageAssetsSzHKStock = {
            title: "证券账户及资金账户近20个交易日日均资产不低于50万元",
            remarks: "开通深港股权限",
            renderWhenPass: true,
            "0": {
                description:
                    "<p>您的账户近20个交易日日均资产为：{{assets}}元</p>" +
                    "<p>根据交易所规定，信用负债、OTC资产和部分场外基金账户资产不包含在内。您可以咨询在线客服或95310热线了解详情。</p>" +
                    "<p>温馨提示：银证转账后次日再来试试！</p>",
                link_description: "",
                link: "",
            },
            "1": {},
        };

        {
            //根据系统版本设定银证转账参数。
            if (Controller.getAppId() == "yjb3.0") {
                this.averageAssetsSzHKStock["0"].link_description = "银证转账";
                this.averageAssetsSzHKStock["0"].link = "bankTransferV3";
            } else if (Controller.getAppId() == "yjbjyd") {
                this.averageAssetsSzHKStock["0"].link_description = "银证转账";
                this.averageAssetsSzHKStock["0"].link = "bankTransferV2";
            } else {
            }
        }
        //沪伦通CDR
        this.averageAssetsLondonCdr = {
            title: "证券账户及资金账户近20个交易日日均资产不低于300万元",
            remarks: "开通沪伦通存托凭证权限",
            renderWhenPass: true,
            "0": {
                description:
                    "<p>您的账户近20个交易日日均资产为：{{assets}}元</p>" +
                    "<p>温馨提示：银证转账后次日再来试试！</p>",
                link_description: "",
                link: "",
            },
            "1": {},
        };

        {
            //根据系统版本设定银证转账参数。
            if (Controller.getAppId() == "yjb3.0") {
                this.averageAssetsLondonCdr["0"].link_description = "银证转账";
                this.averageAssetsLondonCdr["0"].link = "bankTransferV3";
            } else if (Controller.getAppId() == "yjbjyd") {
                this.averageAssetsLondonCdr["0"].link_description = "银证转账";
                this.averageAssetsLondonCdr["0"].link = "bankTransferV2";
            } else {
            }
        }
        //专项头寸
        this.averageAssetsZXTC = {
            title: "证券账户及资金账户近20个交易日日均净资产不低于300万元",
            remarks: "开通专项头寸交易权限",
            renderWhenPass: true,
            "0": {
                description:
                    "<p>您的账户近20个交易日日均净资产为：{{assets}}元</p>" +
                    "<p>温馨提示：银证转账后次日再来试试！</p>",
                link_description: "",
                link: "",
            },
            "1": {},
            "97": {
                description: {
                    default:
                        "<p>当前是清算时间，不支持查询，请稍后或交易时间再来办理吧！</p>",
                    //科创板
                    "30050": MSG_DICT.ALLTIME_97,
                    "30054": MSG_DICT.ALLTIME_97,
                },
                link_description: "",
                link: "",
                statusIcon: "warning",
            },
        };
        {
            //根据系统版本设定银证转账参数。
            if (Controller.getAppId() == "yjb3.0") {
                this.averageAssetsZXTC["0"].link_description = "银证转账";
                this.averageAssetsZXTC["0"].link = "bankTransferV3";
            } else if (Controller.getAppId() == "yjbjyd") {
                this.averageAssetsZXTC["0"].link_description = "银证转账";
                this.averageAssetsZXTC["0"].link = "bankTransferV2";
            } else {
            }
        }
        //科创板
        this.averageAssetsSTIB = {
            title: "证券账户及资金账户近20个交易日日均资产不低于50万元",
            remarks: "开通科创板股票交易权限",
            renderWhenPass: true,
            "0": {
                description:
                    "<p>您的账户近20个交易日日均资产为：{{assets}}元</p>" +
                    "<p>温馨提示：银证转账后次日再来试试！</p>",
                link_description: "",
                link: "",
            },
            "1": {},
            "97": {
                description: {
                    default:
                        "<p>当前是清算时间，不支持查询，请稍后或交易时间再来办理吧！</p>",
                    //科创板
                    "30050": MSG_DICT.ALLTIME_97,
                    "30054": MSG_DICT.ALLTIME_97,
                },
                link_description: "",
                link: "",
                statusIcon: "warning",
            },
        };

        {
            //根据系统版本设定银证转账参数。
            if (Controller.getAppId() == "yjb3.0") {
                this.averageAssetsSTIB["0"].link_description = "银证转账";
                this.averageAssetsSTIB["0"].link = "bankTransferV3";
            } else if (Controller.getAppId() == "yjbjyd") {
                this.averageAssetsSTIB["0"].link_description = "银证转账";
                this.averageAssetsSTIB["0"].link = "bankTransferV2";
            } else {
            }
        }
        //新三板
        this.averageAssetsStockRotation = {
            title: "近10个交易日日均资产不低于100万元",
            remarks: "",
            renderWhenPass: true,
            "0": {
                description:
                    "<p>您的账户近10个交易日日均资产为：{{assets}}元</p>" +
                    "<p>温馨提示：银证转账后次日再来试试！</p>",
                link_description: "",
                link: "",
            },
            "1": {},
            "97": {
                description: {
                    default:
                        "<p>当前是清算时间，不支持查询，请稍后或交易时间再来办理吧！</p>",
                },
                link_description: "",
                link: "",
                statusIcon: "warning",
            },
        };

        {
            //根据系统版本设定银证转账参数。
            if (Controller.getAppId() == "yjb3.0") {
                this.averageAssetsStockRotation["0"].link_description =
                    "银证转账";
                this.averageAssetsStockRotation["0"].link = "bankTransferV3";
            } else if (Controller.getAppId() == "yjbjyd") {
                this.averageAssetsStockRotation["0"].link_description =
                    "银证转账";
                this.averageAssetsStockRotation["0"].link = "bankTransferV2";
            } else {
            }
        }

        //北交所
        this.averageAssetsBJXStockRotation = {
            title: "证券账户及资金账户近20个交易日日均资产不低于50万元",
            remarks: "",
            renderWhenPass: false,
            "0": {
                description:
                    "<p>您的账户近20个交易日日均资产为：{{assets}}元</p>" +
                    "<p>温馨提示：银证转账后次日再来试试！</p>",
                link_description: "",
                link: "",
            },
            "1": {},
            "97": {
                description: {
                    default:
                        MSG_DICT.ALLTIME_97,
                },
                link_description: "",
                link: "",
                statusIcon: "warning",
            },
        };

        {
            //根据系统版本设定银证转账参数。
            if (Controller.getAppId() == "yjb3.0") {
                this.averageAssetsBJXStockRotation["0"].link_description =
                    "银证转账";
                this.averageAssetsBJXStockRotation["0"].link = "bankTransferV3";
            } else if (Controller.getAppId() == "yjbjyd") {
                this.averageAssetsBJXStockRotation["0"].link_description =
                    "银证转账";
                this.averageAssetsBJXStockRotation["0"].link = "bankTransferV2";
            } else {
            }
        }

        // 可转债退市整理权限
        this.hasKzzRight = {
            title: "深圳A股账户可转债权限开通",
            remarks: "",
            renderWhenPass: true,
            "0": {
                description:
                    "<p>深圳A股账户可转债权限开通未通过</p>",
                link_description: "立即开通",
                link: "holderRights",
            },
            "1": {},
            "97": {
                description: {
                    default:
                        MSG_DICT.ALLTIME_97,
                },
                link_description: "",
                link: "",
                statusIcon: "warning",
            },
        };
        
        //可转债退市整理权限-资产
        this.averageAssetKzztsStock = {
            title: "权限开通前20个交易日证券账户及资金账户内的资产日均不低于人民币50万元（不包括该投资者通过融资融券融入的资金和证券）",
            remarks: "",
            renderWhenPass: true,
            "0": {
                description:
                    "<p>您的账户近20个交易日日均资产为：{{assets}}元</p>" +
                    "<p>温馨提示：银证转账后次日再来试试！</p>",
                link_description: "银证转账",
                link: "bankTransferV3",
            },
            "1": {},
            "97": {
                description: {
                    default:
                        "<p>当前是清算时间，不支持查询，请稍后或交易时间再来办理吧！</p>",
                },
                link_description: "",
                link: "",
                statusIcon: "warning",
            },
        };
        
        // 可转债退市整理权限风险等级不符
        // this.riskEvaluationValidMatchBusiness = {
        //     title: "风险等级符合本业务要求",
        //     remarks: "风险测评（有效期内&新试卷）",
        //     renderWhenPass: false,
        //     "0": {
        //         description: "<p></p>" + "<p></p>",
        //         link_description: "重新评测",
        //         link: "risk",
        //     },
        //     "1": {},
        //     "2": {
        //         description:
        //             "<p>您当前的风险测评结果不是最新版本，或结果已过期，请重新参与测评。</p>",
        //         link_description: "重新评测",
        //         link: "risk",
        //     },
        //     "3": {
        //         description:
        //             "<p>您当前的风险测评结果不是最新版本，或结果已过期，请重新参与测评。</p>",
        //         link_description: "重新评测",
        //         link: "risk",
        //     },
        //     "4": {
        //         description:
        //             "<p>该业务等级为“{{businessRiskValidValue}}”您最近一次的投资风险测评结果为“{{clientRiskbearValue}}”不符合该业务的要求。</p>",
        //         link_description: "重新评测",
        //         link: "risk",
        //     },
        //     "9": {
        //         description:
        //             "<p>由于风险测评题目有更新，您当前的风险测评结果不是最新版本，请重新参与测评。</p>",
        //         link_description: "重新评测",
        //         link: "risk",
        //     },
        // };
        // 风险等级强匹配
        this.riskEvaluationValidMatchBusiness = {
            title: "风险等级符合本业务要求",
            remarks: "",
            renderWhenPass: true,
            "0": {
                description: "<p></p>" + "<p></p>",
                link_description: "重新评测",
                link: "risk"
            },
            "1": {},
            "2": {
                description: {
                    default:
                        "<p>您当前的风险测评结果不是最新版本，或结果已过期，请重新参与测评。</p>",
                    // 股票期权
                    "30075":
                        "<p>根据交易所的要求，开通衍生品合约账户，须满足风险承受能力为C3及以上，且有效期不低于1年。您的风险测评有效期至{{evaluEndDateValue}}，距今不满一年，请重新测评。</p>",
                },
                link_description: "重新评测",
                link: "risk"
            },
            "3": {
                description: "<p>您当前的风险测评结果不是最新版本，或结果已过期，请重新参与测评。</p>",
                link_description: "重新评测",
                link: "risk"
            },
            "4": {
                description: "<p>该业务等级为“{{businessRiskValidValue}}”您最近一次的投资风险测评结果为“{{clientRiskbearValue}}”，不符合开通本业务的要求。</p>",
                link_description: "重新测评",
                link: "risk"
            },
            "9": {
                description: "<p>由于风险测评题目有更新，您当前的风险测评结果不是最新版本，请重新参与测评。</p>",
                link_description: "重新评测",
                link: "risk"
            }
        };

        //创业板-资产
        this.averageAssetsCybStock = {
            title: "近20个交易日日均资产不低于10万元",
            remarks: "",
            renderWhenPass: true,
            "0": {
                description:
                    "<p>您的账户近20个交易日日均资产为：{{assets}}元</p>" +
                    "<p>温馨提示：银证转账后次日再来试试！</p>",
                link_description: "",
                link: "",
            },
            "1": {},
            "97": {
                description: {
                    default:
                        "<p>当前是清算时间，不支持查询，请稍后或交易时间再来办理吧！</p>",
                    //创业板
                    "30082": MSG_DICT.ALLTIME_97,
                    "30083": MSG_DICT.ALLTIME_97,
                    "30084": MSG_DICT.ALLTIME_97,
                },
                link_description: "",
                link: "",
                statusIcon: "warning",
            },
        };

        {
            //根据系统版本设定银证转账参数。
            if (Controller.getAppId() == "yjb3.0") {
                this.averageAssetsCybStock["0"].link_description = "银证转账";
                this.averageAssetsCybStock["0"].link = "bankTransferV3";
            } else {
            }
        }

        //创业板-是否可新开
        this.cybXkCsdcRisk = {
            title: "新增创业板交易投资者",
            remarks: "",
            renderWhenPass: false,
            "0": {
                description: "",
                link_description: "",
                link: "",
            },
            "1": {},
            "3": {
                //中登无正常股东户
                description:
                    "<p>您的深圳A股账户可能已注销或存在其他异常，无法开通创业板权限，请联系您的服务经理或致电95310。</p>",
                link_description: "",
                link: "",
            },
            "5": {
                description: {
                    default:
                        "<p>您已在其他券商开通了创业板权限。点击下方链接，马上申请同步权限到国金证券。</p>",
                },
                link_description: "同步创业板权限",
                link: "startUpBoardXkToZq",
            },
            // "95": {//中登非交易时间-未查到
            //     description: "<p>当前无法查询您的历史权限信息。请在交易时间登录业务办理，选择新开或同步创业板权限。</p>",
            //     link_description: "",
            //     link: ""
            // },
            "95": {
                //非中登时间，不允许查询 7*24
                description:
                    "<p>当前无法查询您的历史权限信息。您可以继续完成申请，我司将在下一交易日核实该信息后再为您办理权限开通。</p>",
                link_description: "",
                link: "",
                statusIcon: "warning",
            },
        };
        //创业板-是否可转签
        this.cybZqCsdcRisk = {
            title: "已在其他券商开通创业板交易权限",
            remarks: "",
            renderWhenPass: true,
            "0": {
                description: "",
                link_description: "",
                link: "",
            },
            "1": {},
            "3": {
                //中登无正常股东户
                description:
                    "<p>您的深圳A股账户可能已注销或存在其他异常，无法开通创业板权限，请联系您的服务经理或致电95310。</p>",
                link_description: "",
                link: "",
            },
            "5": {
                description: {
                    default:
                        "<p>创业板支持在线开通啦！点击下方链接，马上申请开通。</p>",
                },
                link_description: "开通创业板权限",
                link: "startUpBoardZqToXk",
            },
            // "95": {//中登非交易时间-未查到
            //     description: "<p>当前无法查询您的历史权限信息。请在交易时间登录业务办理，选择同步或新开创业板权限。</p>",
            //     link_description: "",
            //     link: ""
            // },
            "95": {
                //非中登时间，不允许查询 7*24
                description:
                    "<p>当前无法查询您的历史权限信息。您可以继续完成申请，我司将在下一交易日核实该信息后再为您办理权限开通。</p>",
                link_description: "",
                link: "",
                statusIcon: "warning",
            },
        };
        //信用创业板业务-普通账户已开通创业板权限
        this.ordinarySaStockHolderHadW = {
            title: "需先开通普通账户创业板权限",
            remarks: "",
            renderWhenPass: false,
            "0": {
                description: {
                    default:
                        "<p>在申请信用账户创业板权限前，您需要先开通普通账户创业板权限。</p>",
                    //信用创业板补签
                    "30085":
                        "<p>在申请信用账户注册制创业板交易权限前，您需要先开通普通账户注册制创业板交易权限。</p>",
                    //信用创业板新开
                    "30086":
                        "<p>在申请信用账户创业板权限前，您需要先开通普通账户创业板权限。</p>",
                },
                link_description: "开通普通账户创业板",
                link: "startUpBoardV2Query",
            },
            "1": {},
        };

        //日均资产end
        //风险等级
        this.riskEvaluationValid = {
            title: "风险测评结果有效",
            remarks: "风险测评（有效期内&新试卷）",
            renderWhenPass: false,
            "0": {
                description: "<p></p>" + "<p></p>",
                link_description: "重新评测",
                link: "risk",
            },
            "1": {},
            "2": {
                description:
                    "<p>您当前的风险测评结果不是最新版本，或结果已过期，请重新参与测评。</p>",
                link_description: "重新评测",
                link: "risk",
            },
            "3": {
                description:
                    "<p>您当前的风险测评结果不是最新版本，或结果已过期，请重新参与测评。</p>",
                link_description: "重新评测",
                link: "risk",
            },
            "9": {
                description:
                    "<p>由于风险测评题目有更新，您当前的风险测评结果不是最新版本，请重新参与测评。</p>",
                link_description: "重新评测",
                link: "risk",
            },
        };
        // 产品购买双录
        this.productDoubleRecordRiskEvaluationValid = {
            // title: "风险测评结果有效",
            title:{
                '30061':'您的风险测评结果与产品购买双录风险等级匹配',
                'default': '风险测评结果有效',
            },
            remarks: "风险测评（有效期内&新试卷）",
            renderWhenPass: false,
            "0": {
                description: "<p></p>" + "<p></p>",
                link_description: "重新评测",
                link: "risk",
            },
            "1": {},
            "2": {
                description:
                    "<p>您当前的风险测评结果不是最新版本，或结果已过期，请重新参与测评。</p>",
                link_description: "重新评测",
                link: "risk",
            },
            "3": {
                description:
                    "<p>您当前的风险测评结果不是最新版本，或结果已过期，请重新参与测评。</p>",
                link_description: "重新评测",
                link: "risk",
            },
            "9": {
                description:
                    "<p>由于风险测评题目有更新，您当前的风险测评结果不是最新版本，请重新参与测评。</p>",
                link_description: "重新评测",
                link: "risk",
            },
            "10": {
                description:
                    "<p>该双录产品风险等级为{{productDoubleRecordRiskLevelValue}}，您最近一次的投资风险测评结果为“{{riskLevelValue}}”不符合该业务的要求。</p>",
                link_description: "重新评测",
                link: "risk",
            },
        };
        this.riskEvaluationNormal = {
            title: {
                default: "风险测评结果有效",
                "30080": "风险等级符合标准咨询服务业务要求（风险等级为C1最低类别的客户不允许签约）",
                "30095": "风险等级符合开通专项头寸权限业务要求（风险等级为C1最低类别的客户不允许开通）",
                "30105": "风险等级符合公司债/企业债权限开通业务要求（风险等级为C1最低类别的客户不允许开通）",
                "30106": "风险等级符合公司债/企业债权限开通业务要求（风险等级为C1最低类别的客户不允许开通）",
                "30107": "风险等级符合公司债/企业债权限开通业务要求（风险等级为C1最低类别的客户不允许开通）",
                "30108": "风险等级符合公司债/企业债权限开通业务要求（风险等级为C1最低类别的客户不允许开通）",
                "30110": "风险等级符合信用北交所权限开通业务要求（风险等级为C1最低类别的客户不允许开通）",
            },
            remarks: "风险测评（有效期内&新试卷）",
            renderWhenPass: false,
            "0": {
                description: "<p></p>" + "<p></p>",
                link_description: "重新评测",
                link: "risk",
            },
            "1": {},
            "2": {
                description:
                    "<p>您当前的风险测评结果不是最新版本，或结果已过期，请重新参与测评。</p>",
                link_description: "重新评测",
                link: "risk",
            },
            "3": {
                description:
                    "<p>您当前的风险测评结果不是最新版本，或结果已过期，请重新参与测评。</p>",
                link_description: "重新评测",
                link: "risk",
            },
            "4": {
                description: {
                    default: "<p>您当前的风险测评结果不是最新版本，或结果已过期，请重新参与测评。</p>",
                    "30095": "<p>您最近一次的投资风险测评结果为“{{riskLevelNameValue}}”投资者，不符合开通专项头寸权限的业务要求。</p>",
                    "30110": "<p>您最近一次的投资风险测评结果为“{{riskLevelNameValue}}”投资者，不符合开通信用北交所权限的要求。</p>",
                },
                link_description: "重新评测",
                link: "risk",
            },
            "5": {
                description: {
                    default: "<p>您当前的风险测评结果不是最新版本，或结果已过期，请重新参与测评。</p>",
                    "30095": "<p>您最近一次的投资风险测评结果为“{{riskLevelNameValue}}”投资者，不符合开通专项头寸权限的业务要求。</p>",
                    "30110": "<p>您最近一次的投资风险测评结果为“{{riskLevelNameValue}}”投资者，不符合开通信用北交所权限的要求。</p>",
                },
                link_description: "重新评测",
                link: "risk",
            },
            "9": {
                description:
                    "<p>由于风险测评题目有更新，您当前的风险测评结果不是最新版本，请重新参与测评。</p>",
                link_description: "重新评测",
                link: "risk",
            },
            "97": {
                description: {
                    default:
                        "<p>当前是清算时间，不支持查询，请稍后或交易时间再来办理吧！</p>",
                },
                link_description: "",
                link: "",
                statusIcon: "warning",
            },
        };

        this.riskEvaluationProper = {
            title: {
                default: "风险等级符合业务要求",
                "30006":
                    "风险等级符合港股通业务投资者准入要求（风险等级为C1最低类别的客户不允许开通）",
                "30017":
                    "风险等级符合港股通业务投资者准入要求（风险等级为C1最低类别的客户不允许开通）",
                "30005":
                    "风险等级符合创业板业务投资者准入要求（风险等级为C1最低类别的客户不允许办理）",
                "30007":
                    "风险等级符合风险警示业务投资者准入要求（风险等级为C1最低类别的客户不允许开通）",
                "30008":
                    "风险等级符合退市整理业务投资者准入要求（风险等级为C1最低类别的客户不允许开通）",
                "30018":
                    "风险等级符合港股通业务投资者准入要求（风险等级为C1最低类别的客户不允许开通）",
                "30034":
                    "风险等级符合存托凭证交易业务要求（风险等级为C1最低类别的客户不允许开通）",
                "30039":
                    "风险等级符合存托凭证交易业务要求（风险等级为C1最低类别的客户不允许开通）",
                "30036":
                    "风险等级符合创新企业股票交易业务要求（风险等级为C1最低类别的客户不允许开通）",
                "30041":
                    "风险等级符合创新企业股票交易业务要求（风险等级为C1最低类别的客户不允许开通）",
                "30045":
                    "风险等级符合沪伦通业务投资者准入要求（风险等级为C1最低类别的客户不允许开通）",
                "30050":
                    "风险等级符合科创板股票交易业务要求（风险等级为C1最低类别的客户不允许开通）",
                "30054":
                    "风险等级符合科创板股票交易业务要求（风险等级为C1最低类别的客户不允许开通）",
                "30075": "风险等级符合股票期权交易业务要求",
                "30077":
                    "风险等级符合新三板股票交易业务要求（风险等级为C1最低类别的客户不允许开通）",
                "30082":
                    "风险等级符合创业板业务投资者准入要求（风险等级为C1最低类别的客户不允许开通）", //创业板新开
                "30083":
                    "风险等级符合创业板业务投资者准入要求（风险等级为C1最低类别的客户不允许开通）", //创业板转签
                "30084":
                    "风险等级符合创业板业务投资者准入要求（风险等级为C1最低类别的客户不允许开通）", //创业板补签
                "30085":
                    "风险等级符合创业板业务投资者准入要求（风险等级为C1最低类别的客户不允许开通）", //信用创业板补签
                "30086":
                    "风险等级符合创业板业务投资者准入要求（风险等级为C1最低类别的客户不允许开通）", //信用创业板开通
                "30100":
                    "风险等级符合北交所股票交易业务要求（风险等级为C1最低类别的客户不允许开通）", //信用创业板开通
            },
            remarks: "风险测评（有效期内&新试卷&非C1（最低）&匹配柜台设置）",
            renderWhenPass: true,
            "0": {
                description: "<p></p>" + "<p></p>",
                link_description: "重新评测",
                link: "risk",
            },
            "1": {},
            "2": {
                description:
                    "<p>您当前的风险测评结果不是最新版本，或结果已过期，请重新参与测评。</p>",
                link_description: "重新评测",
                link: "risk",
            },
            "3": {
                description:
                    "<p>您当前的风险测评结果不是最新版本，或结果已过期，请重新参与测评。</p>",
                link_description: "重新评测",
                link: "risk",
            },
            "4": {
                description: {
                    default:
                        "<p>您最近一次的投资风险测评结果为“{{riskLevelNameValue}}”投资者，不符合该业务交易权限的要求。</p>",
                    //创业板转签
                    "30005":
                        "<p>您最近一次的投资风险测评结果为“{{riskLevelNameValue}}”投资者，不符合开通或同步创业板交易权限的要求。</p>",
                    //开通港股通权限
                    "30006":
                        "<p>您最近一次的投资风险测评结果为“{{riskLevelNameValue}}”投资者，不符合开通港股通交易权限的要求。</p>",
                    "30017":
                        "<p>您最近一次的投资风险测评结果为“{{riskLevelNameValue}}”投资者，不符合开通港股通交易权限的要求。</p>",
                    //两融开户
                    "30018":
                        "<p>您最近一次的投资风险测评结果为“{{riskLevelNameValue}}”投资者，不符合开通融资融券账户的要求。</p>",
                    //风险警示板开通
                    "30007":
                        "<p>您最近一次的投资风险测评结果为“{{riskLevelNameValue}}”投资者，不符合开通风险警示股票交易权限的要求。</p>",
                    //风险警示板注销
                    "30019":
                        "<p>您最近一次的投资风险测评结果为“{{riskLevelNameValue}}”投资者，不符合注销风险警示股票交易权限的要求。</p>",
                    //退市整理
                    "30008":
                        "<p>您最近一次的投资风险测评结果为“{{riskLevelNameValue}}”投资者，不符合开通退市整理期股票交易权限的要求。</p>",
                    //cdr
                    "30034":
                        "<p>您最近一次的投资风险测评结果为“{{riskLevelNameValue}}”投资者，不符合开通存托凭证交易权限的要求。</p>",
                    "30039":
                        "<p>您最近一次的投资风险测评结果为“{{riskLevelNameValue}}”投资者，不符合开通存托凭证交易权限的要求。</p>",
                    //新企业股票
                    "30036":
                        "<p>您最近一次的投资风险测评结果为“{{riskLevelNameValue}}”投资者，不符合开通创新企业股票交易权限的要求。</p>",
                    "30041":
                        "<p>您最近一次的投资风险测评结果为“{{riskLevelNameValue}}”投资者，不符合开通创新企业股票交易权限的要求。</p>",
                    "30045":
                        "<p>您最近一次的投资风险测评结果为“{{riskLevelNameValue}}”投资者，不符合开通沪伦通存托凭证交易权限的要求。</p>",
                    "30050":
                        "<p>您最近一次的投资风险测评结果为“{{riskLevelNameValue}}”投资者，不符合开通科创板股票交易权限的要求。</p>",
                    "30054":
                        "<p>您最近一次的投资风险测评结果为“{{riskLevelNameValue}}”投资者，不符合开通科创板股票交易权限的要求。</p>",
                    //"30075": "<p>您最近一次的投资风险测评结果为“{{riskLevelNameValue}}”投资者，不符合开通股票期权交易权限的要求。</p>",
                    "30077":
                        "<p>您最近一次的投资风险测评结果为“{{riskLevelNameValue}}”投资者，不符合开通新三板交易权限的要求。</p>",
                },
                link_description: "重新评测",
                link: "risk",
            },
            "5": {
                description: {
                    default:
                        "<p>您最近一次的投资风险测评结果为“{{riskLevelNameValue}}”投资者，不符合该业务交易权限的要求。</p>",
                    //创业板转签
                    "30005":
                        "<p>您最近一次的投资风险测评结果为“{{riskLevelNameValue}}”投资者，不符合开通或同步创业板交易权限的要求。</p>",
                    //开通港股通权限
                    "30006":
                        "<p>您最近一次的投资风险测评结果为“{{riskLevelNameValue}}”投资者，不符合开通港股通交易权限的要求。</p>",
                    "30017":
                        "<p>您最近一次的投资风险测评结果为“{{riskLevelNameValue}}”投资者，不符合开通港股通交易权限的要求。</p>",
                    //两融开户
                    "30018":
                        "<p>您最近一次的投资风险测评结果为“{{riskLevelNameValue}}”投资者，不符合开通融资融券账户的要求。</p>",
                    //风险警示板开通
                    "30007":
                        "<p>您最近一次的投资风险测评结果为“{{riskLevelNameValue}}”投资者，不符合开通风险警示股票交易权限的要求。</p>",
                    //风险警示板注销
                    "30019":
                        "<p>您最近一次的投资风险测评结果为“{{riskLevelNameValue}}”投资者，不符合注销风险警示股票交易权限的要求。</p>",
                    //退市整理
                    "30008":
                        "<p>您最近一次的投资风险测评结果为“{{riskLevelNameValue}}”投资者，不符合开通退市整理期股票交易权限的要求。</p>",
                    //cdr
                    "30034":
                        "<p>您最近一次的投资风险测评结果为“{{riskLevelNameValue}}”投资者，不符合开通存托凭证交易权限的要求。</p>",
                    "30039":
                        "<p>您最近一次的投资风险测评结果为“{{riskLevelNameValue}}”投资者，不符合开通存托凭证交易权限的要求。</p>",
                    //新企业股票
                    "30036":
                        "<p>您最近一次的投资风险测评结果为“{{riskLevelNameValue}}”投资者，不符合开通创新企业股票交易权限的要求。</p>",
                    "30041":
                        "<p>您最近一次的投资风险测评结果为“{{riskLevelNameValue}}”投资者，不符合开通创新企业股票交易权限的要求。</p>",
                    "30045":
                        "<p>您最近一次的投资风险测评结果为“{{riskLevelNameValue}}”投资者，不符合开通沪伦通存托凭证交易权限的要求。</p>",
                    "30050":
                        "<p>您最近一次的投资风险测评结果为“{{riskLevelNameValue}}”投资者，不符合开通科创板股票交易权限的要求。</p>",
                    "30054":
                        "<p>您最近一次的投资风险测评结果为“{{riskLevelNameValue}}”投资者，不符合开通科创板股票交易权限的要求。</p>",
                    "30077":
                        "<p>您最近一次的投资风险测评结果为“{{riskLevelNameValue}}”投资者，不符合开通新三板交易权限的要求。</p>",
                },
                link_description: "重新评测",
                link: "risk",
            },
            "9": {
                description:
                    "<p>由于风险测评题目有更新，您当前的风险测评结果不是最新版本，请重新参与测评。</p>",
                link_description: "重新评测",
                link: "risk",
            },
            "97": {
                description: {
                    default:
                        "<p>当前是清算时间，不支持查询，请稍后或交易时间再来办理吧！</p>",
                    //科创板
                    "30050": MSG_DICT.ALLTIME_97,
                    "30054": MSG_DICT.ALLTIME_97,
                    //创业板
                    "30082": MSG_DICT.ALLTIME_97,
                    "30083": MSG_DICT.ALLTIME_97,
                    "30084": MSG_DICT.ALLTIME_97,
                    "30100": MSG_DICT.ALLTIME_97,
                },
                link_description: "",
                link: "",
                statusIcon: "warning",
            },
        };
        this.riskEvaluationProperPassNotShow = {
            title: {
                default: "风险等级符合业务要求",
                "30080":
                    "风险等级符合标准咨询服务业务要求（风险等级为C1最低类别的客户不允许签约）",
            },
            remarks: "风险测评（有效期内&新试卷&非C1（最低）&匹配柜台设置）",
            renderWhenPass: false,
            "0": {
                description: "<p></p>" + "<p></p>",
                link_description: "重新评测",
                link: "risk",
            },
            "1": {},
            "2": {
                description:
                    "<p>您当前的风险测评结果不是最新版本，或结果已过期，请重新参与测评。</p>",
                link_description: "重新评测",
                link: "risk",
            },
            "3": {
                description:
                    "<p>您当前的风险测评结果不是最新版本，或结果已过期，请重新参与测评。</p>",
                link_description: "重新评测",
                link: "risk",
            },
            "4": {
                description: {
                    default:
                        "<p>您最近一次的投资风险测评结果为“{{riskLevelNameValue}}”投资者，不符合该业务交易权限的要求。</p>",
                },
                link_description: "重新评测",
                link: "risk",
            },
            "5": {
                description: {
                    default:
                        "<p>您最近一次的投资风险测评结果为“{{riskLevelNameValue}}”投资者，不符合该业务交易权限的要求。</p>",
                },
                link_description: "重新评测",
                link: "risk",
            },
            "97": {
                description: {
                    default:
                        "<p>当前是清算时间，不支持查询，请稍后或交易时间再来办理吧！</p>",
                },
                link_description: "",
                link: "",
                statusIcon: "warning",
            },
        };

        //风险等级end
        this.firstTradeMonthSix = {
            title: "参与证券交易的时间不少于6个月",
            remarks: "融资融券开户",
            renderWhenPass: true,
            "2": {
                //不满足，且没有首次交易
                description:
                    "<p>您还没有做过证券交易。</p>" +
                    "<p>温馨提示：首次交易满6个月后再来试试哦！</p>",
                link_description: "",
                link: "",
            },
            "0": {
                //不满足，有首次交易日期
                description:
                    "<p>您首次证券交易的时间为：{{firstTradeTime}}</p>" +
                    "<p>温馨提示：{{firstTradeMonthSixTime}}后再来试试哦！</p>",
                link_description: "",
                link: "",
            },
            "95": {
                //非中登时间，不允许查询
                description:
                    "<p>温馨提示：请在首次交易满6个月后再来开户。</p>" +
                    "<p>如果您在其他券商做过交易，请在交易时间前来办理。</p>",
                link_description: "",
                link: "",
                statusIcon: "warning",
            },
            "97": {
                //清算时间，不允许查询
                description:
                    "<p>温馨提示：请在首次交易满6个月后再来开户。</p>" +
                    "<p>如果您在其他券商做过交易，请在交易时间前来办理。</p>",
                link_description: "",
                link: "",
                statusIcon: "warning",
            },
            "1": {},
        };
        this.firstTradeYearTwo = {
            title: "证券交易经验满2年",
            remarks: "",
            renderWhenPass: false,
            "2": {
                //不满足，且没有首次交易
                description: {
                    default:
                        "<p>您还没有做过证券交易。</p>" +
                        "<p>温馨提示：首次交易满2年后再来试试哦！</p>",
                    "30077":
                        "当前您的全市场证券交易经验尚不满两年。如果您有2年以上金融产品设计、投资、风险管理及相关工作经历，或者具有指定类型金融机构的高级管理人员任职经历，请联系您的专属客服或致电95310，预约前往营业部申请办理。",
                },
                link_description: "",
                link: "",
            },
            "0": {
                //不满足，有首次交易日期
                description: {
                    default:
                        "<p>您首次证券交易的时间为：{{firstTradeTime}}</p>" +
                        "<p>温馨提示：{{firstTradeYearTwoTime}}后再来试试哦！</p>",
                    "30077":
                        "当前您的全市场证券交易经验尚不满两年。如果您有2年以上金融产品设计、投资、风险管理及相关工作经历，或者具有指定类型金融机构的高级管理人员任职经历，请联系您的专属客服或致电95310，预约前往营业部申请办理。",
                },
                link_description: "",
                link: "",
            },
            "95": {
                //非中登时间，不允许查询
                description: {
                    default:
                        "<p>温馨提示：请在首次交易满2年后再来开通权限。</p>" +
                        "<p>如果您在其他券商做过交易，请在交易时间前来办理。</p>",
                    //科创板
                    "30050":
                        "<p>当前无法查询您的全市场交易经验。您可以继续完成申请，我司将在下一交易日核实该信息后再为您办理权限开通。</p>",
                    "30054":
                        "<p>当前无法查询您的全市场交易经验。您可以继续完成申请，我司将在下一交易日核实该信息后再为您办理权限开通。</p>",
                    //创业板
                    "30082":
                        "<p>当前无法查询您的全市场交易经验。您可以继续完成申请，我司将在下一交易日核实该信息后再为您办理权限开通。</p>",
                    "30083":
                        "<p>当前无法查询您的全市场交易经验。您可以继续完成申请，我司将在下一交易日核实该信息后再为您办理权限开通。</p>",
                    "30084":
                        "<p>当前无法查询您的全市场交易经验。您可以继续完成申请，我司将在下一交易日核实该信息后再为您办理权限开通。</p>",
                    "30100":
                        "<p>当前无法查询您的全市场交易经验。您可以继续完成申请，我司将在下一交易日核实该信息后再为您办理权限开通。</p>",
                    "30110":
                        "<p>当前无法查询您的全市场交易经验。您可以继续完成申请，我司将在下一交易日核实该信息后再为您办理权限开通。</p>",
                    "30114": // 可转债退市整理权限
                        "<p>当前无法查询您的全市场交易经验。您可以继续完成申请，我司将在下一交易日核实该信息后再为您办理权限开通。</p>",
                },
                link_description: "",
                link: "",
                statusIcon: "warning",
            },
            "97": {
                description: {
                    default:
                        "<p>温馨提示：请在首次交易满2年后再来开通权限。</p>" +
                        "<p>如果您在其他券商做过交易，请在交易时间前来办理。</p>",
                    //科创板
                    "30050": MSG_DICT.ALLTIME_97,
                    "30054": MSG_DICT.ALLTIME_97,
                    //创业板
                    "30082": MSG_DICT.ALLTIME_97,
                    "30083": MSG_DICT.ALLTIME_97,
                    "30084": MSG_DICT.ALLTIME_97,
                    "30100": MSG_DICT.ALLTIME_97,
                },
                link_description: "",
                link: "",
                statusIcon: "warning",
            },

            "1": {},
        };
        this.csdcCreditAStockholder = {
            //本功能迁移到移动端之前，本条件可沿用之前的方式，未查到时不显示。
            title: "未在其他券商开立信用账户",
            remarks: "融资融券开户",
            renderWhenPass: true,
            "0": {
                description: "<p>请前往原券商取消信用账户后再来申请。</p>",
                link_description: "",
                link: "",
            },
            "95": {
                description:
                    "<p>如您已在其他券商开立了信用账户，宝宝为您开户可能失败。</p>",
                link_description: "",
                link: "",
                statusIcon: "warning",
            },
            "97": {
                description:
                    "<p>如您已在其他券商开立了信用账户，宝宝为您开户可能失败。</p>",
                link_description: "",
                link: "",
                statusIcon: "warning",
            },
            "1": {},
        };
        this.creditScoreFirst = {
            title: "征信分数满足融资融券开户条件",
            remarks: "融资融券开户",
            renderWhenPass: true,
            "0": {
                description:
                    "<p>很抱歉，您当前的征信得分没有达到融资融券开户的要求，宝宝暂时不能为您开通信用账户。您可以通过提高账户总资产或重新进行风险测评来增加征信评分。</p>" +
                    "<p>您可以联系在线客服咨询开户事项。</p>",
                link_description: "在线客服",
                link: "onlineService",
            },
            "1": {},
        };
        this.creditScoreAgain = {
            title: "征信分数满足调整额度要求",
            remarks: "调整信用额度",
            renderWhenPass: true,
            "0": {
                description:
                    "<p>很抱歉，您当前的征信得分没有达到调整额度的要求，宝宝暂时不能为您调整额度。您可以通过提高账户总资产或重新进行风险测评来增加征信评分。 </p>",
                link_description: "",
                link: "",
            },
            "1": {},
        };
        this.csdcGemInfo = {
            title: "已在其他券商开通创业板交易权限",
            remarks: "创业板转签",
            renderWhenPass: true,
            "0": {
                //未开通
                description:
                    "<p>创业板开通一定要临柜办理，您可以到附近的国金证券营业网点申请开通创业板，或先到原券商营业部开通创业板权限，然后在交易时间登录业务办理，直接同步权限至国金证券。 </p>",
                link_description: "",
                link: "",
            },
            "95": {
                //未查到
                description:
                    "<p>创业板开通一定要临柜办理，您可以到附近的国金证券营业网点申请开通创业板，或先到原券商营业部开通创业板权限，然后在交易时间登录业务办理，直接同步权限至国金证券。</p>" +
                    "<p>宝宝提醒您：如您已在其他券商开通了创业板交易权限，请于交易时间前来同步权限。</p>",
                link_description: "",
                link: "",
                statusIcon: "warning",
            },
            "97": {
                //未查到
                description:
                    "<p>创业板开通一定要临柜办理，您可以到附近的国金证券营业网点申请开通创业板，或先到原券商营业部开通创业板权限，然后在交易时间登录业务办理，直接同步权限至国金证券。</p>" +
                    "<p>宝宝提醒您：如您已在其他券商开通了创业板交易权限，请于交易时间前来同步权限。</p>",
                link_description: "",
                link: "",
                statusIcon: "warning",
            },
            "1": {},
        };
        this.accordCybInfo = {
            title: "新增创业板交易投资者",
            remarks: "创业板开通",
            renderWhenPass: false,
            "0": {
                //正常无该场景
                description: "",
                link_description: "",
                link: "",
            },
            "2": {
                //未开通
                description:
                    "<p>您已在其他券商开通了创业板权限。请在交易时间登录业务办理，点击“同步创业板权限”，按页面引导办理开通。</p>",
                link_description: "",
                link: "",
            },
            "95": {
                //未查到
                description:
                    "<p>当前无法查询您的历史权限信息。如果您已在其他券商开通了创业板权限，请在交易时间登录业务办理，点击“同步创业板权限”，按页面引导办理开通。</p>",
                link_description: "",
                link: "",
                statusIcon: "warning",
            },
            "97": {
                //未查到
                description:
                    "<p>当前无法查询您的历史权限信息。如果您已在其他券商开通了创业板权限，请在交易时间登录业务办理，点击“同步创业板权限”，按页面引导办理开通。</p>",
                link_description: "",
                link: "",
                statusIcon: "warning",
            },
            "1": {},
        };

        /*三方存管*/
        //三方存管-账户资金余额为0
        this.fundBananceCheck = {
            title: "账户资金余额为0",
            remarks: "账户资金余额为0",
            renderWhenPass: true,
            "0": {
                description:
                    "<p>申请变更三方存管账户前，请先通过银证转账将资金全部转出；如果您开通了现金理财功能，也可以将保留金额设置为0。然后于下一交易日前来申请变更。 </p>",
                link_description: "",
                link: "",
            },
            "1": {},
        };
        {
            //根据系统版本设定参数。
            if (Controller.getAppId() == "yjb3.0") {
                this.fundBananceCheck["0"].link_description = [
                    "银证转账",
                    "设置保留金额",
                ];
                this.fundBananceCheck["0"].link = ["bankTransferV3", "cashV3"];
            } else if (Controller.getAppId() == "yjbjyd") {
                this.fundBananceCheck["0"].link_description = [
                    "银证转账",
                    "设置保留金额",
                ];
                this.fundBananceCheck["0"].link = ["bankTransferV2", "cashV2"];
            } else {
                //this.fundBananceCheck["0"].link_description=["银证转账","设置保留金额"];
                //this.fundBananceCheck["0"].link=["bankTransferV3","cashV3"];
            }
        }
        //信用三方存管-账户资金余额为0
        this.creditFundBananceCheck = {
            title: "账户资金余额为0",
            remarks: "信用账户资金余额为0",
            renderWhenPass: true,
            "0": {
                description:
                    "<p>申请变更三方存管账户前，请先通过银证转账将资金全部转出；如果您开通了现金理财功能，也可以将保留金额设置为0。然后于下一交易日前来申请变更。 </p>",
                link_description: "",
                link: "",
            },
            "1": {},
        };
        {
            //根据系统版本设定参数。
            if (Controller.getAppId() == "yjb3.0") {
                this.creditFundBananceCheck["0"].link_description = [
                    "银证转账",
                ];
                this.creditFundBananceCheck["0"].link = [
                    "creditBankTransferV3",
                ];
            } else if (Controller.getAppId() == "yjbjyd") {
                this.creditFundBananceCheck["0"].link_description = [
                    "银证转账",
                ];
                this.creditFundBananceCheck["0"].link = [
                    "creditBankTransferV2",
                ];
            } else {
                //this.creditFundBananceCheck["0"].link_description=["银证转账"];
                //this.creditFundBananceCheck["0"].link=["creditBankTransferV2"];
            }
        }
        //三方存管-账户当日无资金流水
        this.fundJourCheck = {
            title: "账户当日无资金流水",
            remarks: "账户当日无资金流水",
            renderWhenPass: true,
            "0": {
                description:
                    "<p>您今日存在资金流水，无法办理变更，请于下一交易日重新申请。</p>" +
                    "<p>宝宝提醒：如发生转账、交易、查询银行余额等产生资金流水的操作，则当日无法办理三方存管变更。</p>",
                link_description: "",
                link: "",
            },
            "1": {},
        };
        //三方存管-账户当日无证券委托
        this.securityEntrustCheck = {
            title: "账户当日无证券委托",
            remarks: "账户当日无证券委托",
            renderWhenPass: true,
            "0": {
                description:
                    "<p>您今日存在证券委托，无法办理变更，请于下一交易日重新申请。</p>" +
                    "<p>宝宝提醒：新股申购也属于证券委托哦。</p>",
                link_description: "",
                link: "",
            },
            "1": {},
        };
        //三方存管-账户当日不存在在途业务
        this.businessUnfinishCheck = {
            title: "账户当日不存在在途业务",
            remarks: "账户当日不存在在途业务",
            renderWhenPass: true,
            "0": {
                description:
                    "<p>宝宝检测到您今日存在未清算完结的业务，请在清算完成后次日再来申请变更三方存管。</p>" +
                    "<p>宝宝提醒：港股通交易、正/逆回购等交易需待清算完毕后次日方能变更三方存管。 </p>",
                link_description: "",
                link: "",
            },
            "1": {},
        };
        /*三方存管 end*/

        //业务办理时间
        this.businessTime = {
            title: "符合业务办理时间要求",
            remarks: "业务办理时间",
            renderWhenPass: false,
            "0": {
                description: {
                    default:
                        "<p>该业务办理时间为：{{acceptable_time}}。请在此时间段前来申请哦。</p>",
                    //找回资金账号
                    "30013":
                        "<p>该业务办理时间为：{{acceptable_time}}。请在此时间段前来申请哦。</p>",
                    //重置密码
                    "30012":
                        "<p>重置密码的业务办理时间为：{{acceptable_time}}。请在此时间段前来申请哦。</p>",
                    //修改个人资料
                    "30011":
                        "<p>当前是系统清算时间，暂不支持办理本业务。请稍后或交易时间再来办理吧！</p>",
                    //修改交易密码
                    "30012":
                        "<p>当前是系统清算时间，暂不支持修改密码，请稍后或交易时间再来办理吧！</p>",
                    //修改资金密码
                    "30012":
                        "<p>当前是系统清算时间，暂不支持修改密码，请稍后或交易时间再来办理吧！</p>",
                    //补开A股股东户
                    "30002":
                        "<p>开通或下挂A股股东户的业务办理时间为：{{acceptable_time}}。请在此时间段前来申请哦。</p>",
                    //指定交易
                    "30004":
                        "<p>指定交易的业务办理时间为：{{acceptable_time}}。请在此时间段前来申请哦。</p>",
                    //创业板转签
                    "30005":
                        "<p>同步创业板交易权限的业务办理时间为：{{acceptable_time}}。请在此时间段前来申请哦。</p>",
                    //开通港股通权限
                    "30006":
                        "<p>开通港股通权限的业务办理时间为：{{acceptable_time}}。请在此时间段前来申请哦。</p>",
                    //开通港股通权限
                    "30017":
                        "<p>开通港股通权限的业务办理时间为：{{acceptable_time}}。请在此时间段前来申请哦。</p>",
                    //注销港股通权限
                    "30022":
                        "<p>注销港股通权限的业务办理时间为：{{acceptable_time}}。请在此时间段前来申请哦。</p>",
                    //注销港股通权限
                    "30023":
                        "<p>注销港股通权限的业务办理时间为：{{acceptable_time}}。请在此时间段前来申请哦。</p>",
                    //开通风险警示板权限
                    "30007":
                        "<p>开通风险警示股票交易权限的业务办理时间为：{{acceptable_time}}。请在此时间段前来申请哦。</p>",
                    //注销风险警示板权限
                    "30019":
                        "<p>注销风险警示股票交易权限的业务办理时间为：{{acceptable_time}}。请在此时间段前来申请哦。</p>",
                    //开通退市整理权限
                    "30008":
                        "<p>开通退市整理期股票交易权限的业务办理时间为：{{acceptable_time}}。请在此时间段前来申请哦。</p>",
                    //注销退市整理权限
                    "30020":
                        "<p>注销退市整理期股票交易权限的业务办理时间为：{{acceptable_time}}。请在此时间段前来申请哦。</p>",
                    //补开信用A股股东户
                    "30009":
                        "<p>开通信用A股股东户的业务办理时间为：{{acceptable_time}}。请在此时间段前来申请哦。</p>",
                    //调整信用额度
                    "30010":
                        "<p>调整信用额度的业务办理时间为：{{acceptable_time}}。请在此时间段前来申请哦。</p>",
                    //风险测评
                    "30014":
                        "<p>当前是系统清算时间，暂不支持办理本业务。请稍后或交易时间再来办理吧！</p>",
                    //身份证更新
                    "30015": "<p></p>",
                    //问卷调查
                    "30016": "<p></p>",
                },
                link_description: "",
                link: "",
            },
            "1": {},
            "2": {
                description: {
                    default:
                        "<p>当前是系统清算时间，暂不支持办理本业务。请稍后或交易时间再来办理吧！</p>",
                    //修改个人资料
                    "30011":
                        "<p>当前是系统清算时间，暂不支持办理本业务。请稍后或交易时间再来办理吧！</p>",
                    //修改交易密码
                    "30012":
                        "<p>当前是系统清算时间，暂不支持修改密码，请稍后或交易时间再来办理吧！</p>",
                    //修改资金密码
                    "30012":
                        "<p>当前是系统清算时间，暂不支持修改密码，请稍后或交易时间再来办理吧！</p>",
                    //风险测评
                    "30014":
                        "<p>当前是系统清算时间，暂不支持办理本业务。请稍后或交易时间再来办理吧！</p>",
                },
                link_description: "",
                link: "",
            },
            "97": {
                description: {
                    default:
                        "<p>当前是清算时间，不支持查询，请稍后或交易时间再来办理吧！</p>",
                    //科创板
                    "30050": MSG_DICT.ALLTIME_97,
                    "30054": MSG_DICT.ALLTIME_97,
                    //创业板
                    "30082": MSG_DICT.ALLTIME_97,
                    "30083": MSG_DICT.ALLTIME_97,
                    "30084": MSG_DICT.ALLTIME_97,
                    "30100": MSG_DICT.ALLTIME_97,

                },
                link_description: "",
                link: "",
                statusIcon: "warning",
            },
        };

        //业务办理时间end
        //业务相关 end

        //资料有效性、完整性相关：可通过线上办理完善。
        // this.certificateValid = {
        //     title: "身份证件有效",
        //     remarks: "",
        //     renderWhenPass: false,
        //     "0": {
        //         description: "<p>您在我司留存的身份证件已过期，请先更新您的证件信息，再来办理哦！</p>",
        //         link_description: "更新身份证",
        //         //点击后跳转至身份证更新独立功能
        //         link: "idupdate"
        //     },
        //     "1": {},
        //     "97": {
        //         description: {
        //             "default": "<p>当前是清算时间，不支持查询，请稍后或交易时间再来办理吧！</p>",
        //             //科创板
        //             "30050": MSG_DICT.ALLTIME_97,
        //             "30054": MSG_DICT.ALLTIME_97,
        //             //创业板
        //             "30082": MSG_DICT.ALLTIME_97,
        //             "30083": MSG_DICT.ALLTIME_97,
        //             "30084": MSG_DICT.ALLTIME_97,
        //         },
        //         link_description: "",
        //         link: "",
        //         statusIcon: "warning"
        //     },
        // }
        this.secondContactsDataIntegrity = {
            title: "第二联系人信息完整",
            remarks: "",
            renderWhenPass: false,
            "0": {
                description:
                    "<p>请先补充完整您的第二联系人信息，再来办理哦！</p>",
                link_description: "补充第二联系人信息",
                //点击后跳转至第二联系人编辑页面
                link: "personal",
            },
            "1": {},
            "97": {
                description: {
                    default:
                        "<p>当前是清算时间，不支持查询，请稍后或交易时间再来办理吧！</p>",
                    //科创板
                    "30050": MSG_DICT.ALLTIME_97,
                    "30054": MSG_DICT.ALLTIME_97,
                    //创业板
                    "30082": MSG_DICT.ALLTIME_97,
                    "30083": MSG_DICT.ALLTIME_97,
                    "30084": MSG_DICT.ALLTIME_97,
                    "30100": MSG_DICT.ALLTIME_97,

                },
                link_description: "",
                link: "",
                statusIcon: "warning",
            },
        };
        this.controlBenefitDataIntegrity = {
            title: "账户实际控制人、实际受益人信息完整",
            remarks: "",
            renderWhenPass: false,
            "0": {
                description:
                    "<p>请先补充完整您的账户实际控制人、实际受益人信息，再来办理哦！</p>",
                link_description: "补充实际控制人、实际受益人",
                //点击后跳转至“个人资料”页面
                link: "personal",
            },
            "1": {},
            "97": {
                description: {
                    default:
                        "<p>当前是清算时间，不支持查询，请稍后或交易时间再来办理吧！</p>",
                    //科创板
                    "30050": MSG_DICT.ALLTIME_97,
                    "30054": MSG_DICT.ALLTIME_97,
                    //创业板
                    "30082": MSG_DICT.ALLTIME_97,
                    "30083": MSG_DICT.ALLTIME_97,
                    "30084": MSG_DICT.ALLTIME_97,
                    "30100": MSG_DICT.ALLTIME_97,

                },
                link_description: "",
                link: "",
                statusIcon: "warning",
            },
        };
        this.creditRecord = {
            title: "诚信记录完整",
            remarks: "诚信记录完整",
            renderWhenPass: false,
            "0": {
                description:
                    "<p>请先补充完整您的诚信记录信息，再来办理哦！</p>",
                link_description: "补充诚信记录",
                link: "personal",
            },
            "1": {},
            "97": {
                description: {
                    default:
                        "<p>当前是清算时间，不支持查询，请稍后或交易时间再来办理吧！</p>",
                    //科创板
                    "30050": MSG_DICT.ALLTIME_97,
                    "30054": MSG_DICT.ALLTIME_97,
                    //创业板
                    "30082": MSG_DICT.ALLTIME_97,
                    "30083": MSG_DICT.ALLTIME_97,
                    "30084": MSG_DICT.ALLTIME_97,
                    "30100": MSG_DICT.ALLTIME_97,
                },
                link_description: "",
                link: "",
                statusIcon: "warning",
            },
        };

        this.certificateValid = {
            title: "证件资料齐全",
            remarks: "",
            renderWhenPass: false,
            "0": {
                //交由具体渲染组件处理
                // description: "<p>您在我司留存的身份证件已过期，请先更新您的证件信息，再来办理哦！</p>",
                // link_description: "更新身份证",
                // //点击后跳转至身份证更新独立功能
                // link: "idupdate"
            },
            "1": {},
            "97": {
                description: {
                    default:
                        "<p>当前是清算时间，不支持查询，请稍后或交易时间再来办理吧！</p>",
                    //科创板
                    "30050": MSG_DICT.ALLTIME_97,
                    "30054": MSG_DICT.ALLTIME_97,
                    //创业板
                    "30082": MSG_DICT.ALLTIME_97,
                    "30083": MSG_DICT.ALLTIME_97,
                    "30084": MSG_DICT.ALLTIME_97,
                    "30100": MSG_DICT.ALLTIME_97,
                },
                link_description: "",
                link: "",
                statusIcon: "warning",
            },
        };
        this.certificateValidIntact = {
            title: "证件资料齐全",
            remarks: "",
            renderWhenPass: false,
            "0": {
                //交由具体渲染组件处理
                // description: "<p>您在我司留存的身份证件已过期，请先更新您的证件信息，再来办理哦！</p>",
                // link_description: "更新身份证",
                // //点击后跳转至身份证更新独立功能
                // link: "idupdate"
            },
            "1": {},
            "97": {
                description: {
                    default:
                        "<p>当前是清算时间，不支持查询，请稍后或交易时间再来办理吧！</p>",
                },
                link_description: "",
                link: "",
                statusIcon: "warning",
            },
        };
        // this.certificateValidTc = {
        //     title: "证件资料齐全",
        //     remarks: "",
        //     renderWhenPass: false,
        //     "0": {//交由具体渲染组件处理
        //         // description: "<p>您在我司留存的身份证件已过期，请先更新您的证件信息，再来办理哦！</p>",
        //         // link_description: "更新身份证",
        //         // //点击后跳转至身份证更新独立功能
        //         // link: "idupdate"
        //     },
        //     "1": {},
        //     "97": {
        //         description: {
        //             "default": "<p>当前是清算时间，不支持查询，请稍后或交易时间再来办理吧！</p>",
        //         },
        //         link_description: "",
        //         link: "",
        //         statusIcon: "warning"
        //     },
        // }
        this.checkPersonalDocument = {
            title: "账户资料齐全",
            remarks: "",
            renderWhenPass: false,
            "0": {
                //交由具体渲染组件处理
                description: "<p></p>",
                link_description: "更新个人资料",
                //点击后跳转至“个人资料”页面
                link: "personal",
            },
            "1": {},
        };

        this.occupationNotMatchAge = {
            title: "年龄与职业不匹配",
            remarks: "",
            renderWhenPass: false,
            "0": {
                //交由具体渲染组件处理
                description: "<p></p>",
                link_description: "",
                link: "",
                statusIcon: "warning",
            },
            "1": {},
        };

        this.addressRightCheck = {
            //ACCOUNT-147-反洗钱-交易常用IP或手机号归属地与常住地址不一致
            title: "常住地址存疑",
            remarks: "",
            renderWhenPass: false,
            "0": {
                //交由具体渲染组件处理
                description: "<p></p>",
                link_description: "",
                link: "",
                statusIcon: "warning",
            },
            "1": {},
        };

        this.archiveIntegrity = {
            title: "账户档案资料齐全",
            remarks: "",
            renderWhenPass: false,
            "0": {
                //账户档案资料不全仅为身份图片信息???
                description:
                    "<p>系统检测到您的相关身份信息图片有瑕疵，您可以至就近营业部或QQ咨询在线客服完善相关身份图片信息。办理完成并审核通过后，下一个交易日即可为您提供线上业务办理哦。</p>",
                link_description: "在线客服 ",
                //点击后跳转至“在线客服”页面。
                link: "onlineService",
            },
            "1": {},
            "97": {
                description: {
                    default:
                        "<p>当前是清算时间，不支持查询，请稍后或交易时间再来办理吧！</p>",
                    //科创板
                    "30050": MSG_DICT.ALLTIME_97,
                    "30054": MSG_DICT.ALLTIME_97,
                    //创业板
                    "30082": MSG_DICT.ALLTIME_97,
                    "30083": MSG_DICT.ALLTIME_97,
                    "30084": MSG_DICT.ALLTIME_97,
                    "30100": MSG_DICT.ALLTIME_97,
                },
                link_description: "",
                link: "",
                statusIcon: "warning",
            },
        };
        //资料有效性、完整性相关：可通过线上办理完善。end

        //账户异常或不允许线上办理
        this.ageValid = {
            title: "年龄不超过70周岁",
            remarks: "融资融券开户",
            renderWhenPass: false,
            "0": {
                description: {
                    default:
                        "<p>很抱歉，宝宝发现您已经70岁了，融资融券交易不同于普通交易，存在较大投资风险，宝宝建议您亲临现场营业部咨询开户事项。</p>",
                    //科创板
                    "30050":
                        "<p>因科创板股票交易存在较大投资风险，建议您亲临营业部现场办理。详询客服热线95310。</p>",
                    "30054":
                        "<p>因科创板股票交易存在较大投资风险，建议您亲临营业部现场办理。详询客服热线95310。</p>",
                    "30110":
                        "<p>因信用北交所交易存在较大投资风险，建议您亲临营业部现场办理。详询客服热线95310。</p>",
                },
                link_description: "查找营业部",
                //点击后跳转至查找营业部页面，暂定是一个url？？？
                link: "findDepartment",
            },
            "1": {},
        };
        this.ageLessThanEighty = {
            title: {
                default: "年龄符合线上办理要求",
                "30050": "年龄不超过80周岁", //科创板
                "30054": "年龄不超过80周岁", //科创板
                "30100": "年龄符合线上办理要求",
            },
            remarks: "",
            renderWhenPass: false,
            "0": {
                description: {
                    default:
                        "<p>您已经80岁或80岁以上了，新三板交易不同于普通交易，存在较大投资风险，根据适当性管理规定，建议您亲临现场营业部咨询办理。</p>",
                    "30050":
                        "<p>因科创板股票交易存在较大投资风险，建议您亲临营业部现场办理。详询客服热线95310。</p>", //科创板
                    "30054":
                        "<p>因科创板股票交易存在较大投资风险，建议您亲临营业部现场办理。详询客服热线95310。</p>", //科创板
                    "30100":
                        "<p>您已经80岁或80岁以上了，北交所股票交易不同于普通交易，存在较大投资风险，建议您亲临现场营业部咨询办理。</p>", // 北交所
                },
                link_description: "查找营业部",
                //点击后跳转至查找营业部页面，暂定是一个url？？？
                link: "findDepartment",
            },
            "1": {},
        };

        this.idCard = {
            title: "证件类型符合网上办理要求", //仅身份证
            remarks: "",
            renderWhenPass: false,
            "0": {
                description:
                    "<p>本业务目前仅支持使用身份证开户的客户办理。其他证件类型的客户，请前往就近营业部临柜办理。</p>",
                link_description: "查找营业部",
                //点击后跳转至查找营业部页面，暂定是一个url？？？
                link: "findDepartment",
            },
            "1": {},
            "97": {
                description: {
                    default:
                        "<p>当前是清算时间，不支持查询，请稍后或交易时间再来办理吧！</p>",
                    //科创板
                    "30050": MSG_DICT.ALLTIME_97,
                    "30054": MSG_DICT.ALLTIME_97,
                    //创业板
                    "30082": MSG_DICT.ALLTIME_97,
                    "30083": MSG_DICT.ALLTIME_97,
                    "30084": MSG_DICT.ALLTIME_97,
                    "30100": MSG_DICT.ALLTIME_97,
                },
                link_description: "",
                link: "",
                statusIcon: "warning",
            },
        };

        this.inspectionIdKind = {
            title: "证件类型符合网上办理要求", //身份证&港澳台
            remarks: "",
            renderWhenPass: false,
            "0": {
                description:
                    "<p>本业务目前仅支持使用身份证、港澳居民来往内地通行证等证件开户的客户办理。其他证件类型的客户，请前往就近营业部临柜办理。" +
                    "详询客服热线<span class='condition-callphone'>&nbsp;95310&nbsp;</span>。</p>",
                link_description: "查找营业部",
                link: "findDepartment",
            },
            "1": {},
            "97": {
                description: {
                    default:
                        "<p>当前是清算时间，不支持查询，请稍后或交易时间再来办理吧！</p>",
                    //科创板
                    "30050": MSG_DICT.ALLTIME_97,
                    "30054": MSG_DICT.ALLTIME_97,
                    //创业板
                    "30082": MSG_DICT.ALLTIME_97,
                    "30083": MSG_DICT.ALLTIME_97,
                    "30084": MSG_DICT.ALLTIME_97,
                    "30100": MSG_DICT.ALLTIME_97,
                    
                },
                link_description: "",
                link: "",
                statusIcon: "warning",
            },
        };

        this.custStatusNormal = {
            title: "账户状态正常",
            remarks: "",
            renderWhenPass: false,
            "0": {
                description:
                    "<p>您当前的账户状态异常，不能在线办理本业务。请前往就近营业部临柜办理。</p>",
                link_description: "查找营业部",
                //点击后跳转至查找营业部页面，暂定是一个url？？？
                link: "findDepartment",
            },
            "1": {},
            "97": {
                description: {
                    default:
                        "<p>当前是清算时间，不支持查询，请稍后或交易时间再来办理吧！</p>",
                    //科创板
                    "30050": MSG_DICT.ALLTIME_97,
                    "30054": MSG_DICT.ALLTIME_97,
                    //创业板
                    "30082": MSG_DICT.ALLTIME_97,
                    "30083": MSG_DICT.ALLTIME_97,
                    "30084": MSG_DICT.ALLTIME_97,
                    "30100": MSG_DICT.ALLTIME_97,

                },
                link_description: "",
                link: "",
                statusIcon: "warning",
            },
        };
        this.fundStatusNormal = {
            //合并至账户状态
            title: "资金账号状态（正常）",
            remarks: "资金账号状态（正常）",
            renderWhenPass: false,
            "0": {
                description: "<p>不满足</p>" + "<p></p>",
                link_description: "",
                link: "",
            },
            "1": {},
        };
        this.custRiskInfoOK = {
            //合并至账户状态
            title: "账户风险要素（合格账户）",
            remarks: "账户风险要素（合格账户）",
            renderWhenPass: false,
            "0": {
                description: "<p></p>" + "<p></p>",
                link_description: "",
                link: "",
            },
            "1": {},
        };

        // this.specialListCredit = {
        //     title: "通过融资融券业务资格审查",
        //     remarks: "融资融券开户",
        //     renderWhenPass: false,
        //     "0": {
        //         description:{
        //             "default":"<p>很抱歉，您的账户没有通过融资融券业务资格审查，宝宝暂时不能为您开通融资融券账户。</p>",
        //             "30018":"<p>很抱歉，您的账户没有通过融资融券业务资格审查，宝宝暂时不能为您开通融资融券账户。</p>",
        //             "30010":"<p>很抱歉，您的账户没有通过融资融券业务资格审查，宝宝暂时不能为您调整额度。</p>"
        //         },
        //         link_description: "",
        //         link: ""
        //     },
        //     "1": {}
        // }
        // this.specialListHKStock = {
        //     title: "通过港股通业务资格审查",
        //     remarks: "开通港股通权限",
        //     renderWhenPass: false,
        //     "0": {
        //         description: "<p>很抱歉，您的账户没有通过港股通业务资格审查，宝宝暂时不能为您开通港股通权限。</p>",
        //         link_description: "",
        //         link: ""
        //     },
        //     "1": {}
        // }
        this.specialListCheck = {
            title: "业务资格审查",
            remarks: "通用业务资格审查(黑名单)",
            renderWhenPass: false,
            "0": {
                description:
                    "<p>很抱歉，您的账户没有通过当前业务的资格审查，暂时不能为您开通本权限。</p>",
                link_description: "",
                link: "",
            },
            "1": {},
            "97": {
                description: {
                    default:
                        "<p>当前是清算时间，不支持查询，请稍后或交易时间再来办理吧！</p>",
                    //科创板
                    "30050": MSG_DICT.ALLTIME_97,
                    "30054": MSG_DICT.ALLTIME_97,
                    //创业板
                    "30082": MSG_DICT.ALLTIME_97,
                    "30083": MSG_DICT.ALLTIME_97,
                    "30084": MSG_DICT.ALLTIME_97,
                    "30100": MSG_DICT.ALLTIME_97,

                },
                link_description: "",
                link: "",
                statusIcon: "warning",
            },
        };

        this.publicSecurityCheck = {
            title: "通过公安校验",
            remarks: "",
            renderWhenPass: false,
            "0": {
                description:
                    "<p>宝宝翻箱倒柜也找不到您的公安身份信息。</p>" +
                    "<p>如果您近期变更过姓名等身份信息，请带好相关证明前往就近营业部临柜办理。</p>",
                link_description: "查找营业部",
                //点击后跳转至查找营业部页面，暂定是一个url？？？
                link: "findDepartment",
            },
            "1": {},
            "2": {
                description:
                    "<p>宝宝翻箱倒柜也找不到您的公安身份信息。您可以休息一会儿再来试试，或咨询客服。</p>",
                link_description: "",
                //title前的图标显示“？”
                link: "",
                statusIcon: "warning",
            },
            "3": {
                //description: "<p>您在我司留存的身份信息是15位身份证，不能在线办理业务。请先更新您的身份证信息后再来办理。</p>",
                description:
                    "<p>您在我司留存的身份信息是15位身份证，不能在线办理业务</p>" +
                    "<p>请先更新您的身份证信息后再来办理。</p>",
                link_description: "更新身份证",
                link: "idupdate",
            },
            "4": {
                description:
                    "<p>您在我司留存的身份信息是一代身份证，不能在线办理业务</p>" +
                    "<p>请先更新您的身份证信息后再来办理。</p>",
                link_description: "更新身份证",
                link: "idupdate",
            },
            "5": {
                //服务不支持
                description:
                    "<p>当前时间不支持该项查询，请稍后或交易时间再来办理吧！</p>",
                link_description: "",
                link: "",
                statusIcon: "warning",
            },

            "6": {
                //超出中登请求次数
                description: "<p>当前不支持该项查询，请明天再来申请。</p>",
                link_description: "",
                link: "",
                statusIcon: "warning",
            },
            "97": {
                description: {
                    default:
                        "<p>当前是清算时间，不支持查询，请稍后或交易时间再来办理吧！</p>",
                    //科创板
                    "30050": MSG_DICT.ALLTIME_97,
                    "30054": MSG_DICT.ALLTIME_97,
                    //创业板
                    "30082": MSG_DICT.ALLTIME_97,
                    "30083": MSG_DICT.ALLTIME_97,
                    "30084": MSG_DICT.ALLTIME_97,
                    "30100": MSG_DICT.ALLTIME_97,

                },
                link_description: "",
                link: "",
                statusIcon: "warning",
            },
            /*"99": {
                description: "<p>亲，宝宝翻箱倒柜也找不到您的公安身份信息。您可以休息一会儿再来试试。</p>",
                link_description: "查找营业部",
                //点击后跳转至查找营业部页面，暂定是一个url？？？
                //title前的图标显示“？”
                link: "findDepartment",
                statusIcon: "warning"
            },*/
        };

        //mob-1978
        this.moneyLaunderingSurveillance = {
            title: "风险管理资格审查",
            //remarks: "反洗钱检测",
            renderWhenPass: false,
            "0": {
                description:
                    "<p>很抱歉，根据我司风险管理需要，不能为您在线办理本业务。请联系您的服务经理或致电95310了解详情。</p>",
                link_description: "",
                link: "",
            },
            "1": {},
        };
        //mob-1979
        this.integrityRecordPassed = {
            title: "诚信记录审查",
            //remarks: "诚信记录",
            renderWhenPass: false,
            "0": {
                description:
                    "<p>很抱歉，您自主申报了不良诚信记录，根据我司业务管理需要，不能为您在线开通本权限。</p>",
                link_description: "",
                link: "",
            },
            "1": {},
            "97": {
                description: {
                    default:
                        "<p>当前是清算时间，不支持查询，请稍后或交易时间再来办理吧！</p>",
                    //科创板
                    "30050": MSG_DICT.ALLTIME_97,
                    "30054": MSG_DICT.ALLTIME_97,
                    //创业板
                    "30082": MSG_DICT.ALLTIME_97,
                    "30083": MSG_DICT.ALLTIME_97,
                    "30084": MSG_DICT.ALLTIME_97,
                    "30100": MSG_DICT.ALLTIME_97,

                },
                link_description: "",
                link: "",
                statusIcon: "warning",
            },
        };

        //账户异常或不允许线上办理end
        this.frequencyControl = {
            title: "频率控制",
            remarks: "频率控制",
            renderWhenPass: false,
            "0": {
                description: "<p></p>" + "<p></p>",
                link_description: "",
                link: "",
            },
            "1": {},
        };
        //科创板

        //开通普通科创板
        this.creditStibOpenAfterNormal = {
            title: "需先开通普通科创板权限",
            remarks: "开通退市整理权限",
            renderWhenPass: false,
            "0": {
                description:
                    "<p>在申请信用账户科创板权限前，您需要先开通普通账户科创板权限。</p>",
                link_description: "开通普通账户科创板",
                link: "stibQuery",
            },
            "1": {},
        };
        //补充条件检查-科创板资产
        this.stibAssetDetection = {
            title: "",
            remarks: "科创板资产补充条件检查",
            renderWhenPass: false,
            "0": {
                description:
                    "<p>尊敬的客户，您在我司的资产状况未通过综合评估，请在补充资产证明后重新申请开通科创板权限。</p>",
                link_description: "",
                link: "",
            },
            "1": {},
            "95": {
                description: "",
            },
            "97": {
                description: "",
            },
            "98": {
                description: "",
            },
            "99": {
                description: "",
            },
        };
        //补充条件检查-科创板风险等级
        this.stibRiskLevelMatch = {
            title: "",
            remarks: "科创板风险等级补充条件检查",
            renderWhenPass: false,
            "0": {
                description:
                    "<p>您当前的风险承受能力为<span class='condition-red'>{{clientRiskbearValue}}</span>，与科创板的风险评级<span class='condition-red'>{{stibRiskValue}}</span>不匹配，不适合参与该产品投资。如仍需开通科创板权限，可通过临柜或非现场双录的方式申请。</p>",
                link_description: "",
                link: "",
            },
            "1": {},
            "95": {
                description: "",
            },
            "97": {
                description: "",
            },
            "98": {
                description: "",
            },
            "99": {
                description: "",
            },
        };
        //合格投资者-私募资产
        this.privateAssetCheck = {
            title: "金融资产不低于300万元或最近三年个人年均收入不低于50万元",
            remarks: "",
            renderWhenPass: true,
            "0": {
                description:
                    "<p>您在我司账户上的资产未满足私募合格投资者的认证要求。如果您在其他金融机构托管的金融总资产或您的近三年年均收入满足要求，您可以上传相关证明材料申请认证。</p>",
                link_description: "上传材料",
                link: "goNext",
            },
            "1": {},
        };
        //合格投资者-资管资产
        this.managementAssetCheck = {
            title:
                "家庭金融净资产不低于300万元或家庭金融资产不低于500万元或近3年个人年均收入不低于40万元",
            remarks: "",
            renderWhenPass: true,
            "0": {
                description:
                    "<p>您在我司账户上的资产未满足资管/信托合格投资者的认证要求。如果您在其他金融机构托管的金融总资产或净资产或近三年年均收入满足要求，您可以上传相关证明材料申请认证。</p>",
                link_description: "上传材料",
                link: "goNext",
            },
            "1": {},
        };
        //合格投资者-资管经验
        this.eligibleInvestorsFirstTradeYearTwo = {
            title: "具有2年以上投资经验",
            remarks: "",
            renderWhenPass: true,
            "2": {
                description:
                    "<p>您的股票交易经验不足2年，如果您在2年前有成功认购银行理财产品、信托产品、基金（包括公募和私募，不包括货币基金及保本型收益凭证）、纸黄金白银等交易经验，可以上传相关证明材料申请认证。</p>",
                link_description: "上传材料",
                link: "goNext",
            },
            "0": {
                description:
                    "<p>您的股票交易经验不足2年，如果您在2年前有成功认购银行理财产品、信托产品、基金（包括公募和私募，不包括货币基金及保本型收益凭证）、纸黄金白银等交易经验，可以上传相关证明材料申请认证。</p>",
                link_description: "上传材料",
                link: "goNext",
            },
            "95": {
                //非中登时间，不允许查询
                description: {
                    default:
                        "<p>当前无法查询您的全市场交易经验。如果您在2年前有成功认购银行理财产品、信托产品、基金（包括公募和私募，不包括货币基金及保本型收益凭证）、纸黄金白银等交易经验，可以上传相关证明材料申请认证。</p>" +
                        "<p>如果您的股票交易经验超过2年，您也可以下一交易日前来申请认证。</p>",
                },
                link_description: "上传材料",
                link: "goNext",
                statusIcon: "warning",
            },
            "97": {
                description: {
                    default:
                        "<p>当前无法查询您的全市场交易经验。如果您在2年前有成功认购银行理财产品、信托产品、基金（包括公募和私募，不包括货币基金及保本型收益凭证）、纸黄金白银等交易经验，可以上传相关证明材料申请认证。</p>" +
                        "<p>如果您的股票交易经验超过2年，您也可以下一交易日前来申请认证。</p>",
                },
                link_description: "上传材料",
                link: "goNext",
                statusIcon: "warning",
            },

            "1": {},
        };

        //新债权限开通
        this.existCreditConversionStock = {
            title: "需注销上海/深圳信用A股账户公司债/企业债权限",
            remarks: "",
            renderWhenPass: false,
            "0": {
                description:
                    "<p>信用A股账户权限未注销</p>",
                link_description: "立即办理",
                link: "creditDebentureQuery",
            },
            "1": {},
            "99": {
                //查询出错
                description:
                    "<p>查询出错了，请稍后再试。</p>",
                link_description: "",
                link: "",
                statusIcon: "warning",
            },
        };

        //新债权限开通
        this.existOrdinaryConversionStock = {
            title: "上海/深圳A股账户公司债、企业债权限开通",
            remarks: "",
            renderWhenPass: false,
            "0": {
                description:
                    "<p>A股账户公司债、企业债权限开通未通过</p>",
                link_description: "立即开通",
                link: "debentureQuery",
            },
            "1": {},
            "99": {
                //查询出错
                description:
                    "<p>查询出错了，请稍后再试。</p>",
                link_description: "",
                link: "",
                statusIcon: "warning",
            },
        };

        this.existNormalHsAccount = {
            title: "已在我司开立沪市衍生品合约账户",
            remarks: "",
            renderWhenPass: true,
            "0": {
                description:
                    "<p>根据深交所的要求，网上申请开立深市衍生品合约账户，须已在我司开立沪市衍生品合约账户。请您本人携带证件亲临国金证券营业部，现场申请开立上海市场衍生品合约账户</p>",
                link_description: "查看开通条件",
                link: "optionAccountSHHint",
            },
            "1": {},
        };

        this.riskEvaluationGatherThanOneYear = {
            title: {
                default: "风险等级符合业务要求",
                "30075": "风险等级符合股票期权交易业务要求",
            },
            remarks: "风险测评（有效期内&新试卷&非C1（最低）&匹配柜台设置）",
            renderWhenPass: true,
            "0": {
                description: "<p></p>" + "<p></p>",
                link_description: "重新评测",
                link: "risk",
            },
            "1": {},
            "2": {
                description:
                    "<p>根据交易所的要求，开通衍生品合约账户，须满足风险承受能力为C3及以上，且有效期不低于1年。您的风险测评有效期至{{evaluEndDateValue}}，距今不满一年，请重新测评。</p>",
                link_description: "重新评测",
                link: "risk",
            },
            "3": {
                description:
                    "<p>您当前的风险测评结果不是最新版本，或结果已过期，请重新参与测评。</p>",
                link_description: "重新评测",
                link: "risk",
            },
            // "4": {
            //     description: "根据交易所的要求，开通衍生品合约账户，须满足风险承受能力为C3及以上，且有效期不低于1年。",
            //     link_description: "重新评测",
            //     link: "risk"
            // },
            "5": {
                description:
                    "根据交易所的要求，开通衍生品合约账户，须满足风险承受能力为C3及以上，且有效期不低于1年。",
                link_description: "重新评测",
                link: "risk",
            },
            "9": {
                description:
                    "<p>由于风险测评题目有更新，您当前的风险测评结果不是最新版本，请重新参与测评。</p>",
                link_description: "重新评测",
                link: "risk",
            },
            "97": {
                description:
                    "<p>当前是清算时间，不支持查询，请稍后或交易时间再来办理吧！</p>",
                link_description: "",
                link: "",
                statusIcon: "warning",
            },
        };
        //关联账户
        this.csdcRelationConfirm = {
            title: "一码通下不存在关联关系未确认的证券账户",
            remarks: "",
            renderWhenPass: false,
            "0": {
                description:
                    "<p>由于您在中登公司存在未确认关联关系的证券账户，不能在网上办理开户。您可前往证券公司营业部办理。</p>",
                link_description: "查找营业部",
                link: "findDepartment",
            },
            "1": {},
            "95": {
                //非中登
                description:
                    "<p>当前未查询到您一码通下证券账户的关联关系情况，您可以再试一次，或者在交易日的9点-16点前来申请。</p>",
                link_description: "",
                link: "",
                statusIcon: "warning",
            },
            "97": {
                //清算
                description:
                    "<p>当前未查询到您一码通下证券账户的关联关系情况，您可以再试一次，或者在交易日的9点-16点前来申请。</p>",
                link_description: "",
                link: "",
                statusIcon: "warning",
            },
            "98": {
                //判断出错
                description:
                    "<p>当前未查询到您一码通下证券账户的关联关系情况，您可以再试一次，或者在交易日的9点-16点前来申请。</p>",
                link_description: "",
                link: "",
                statusIcon: "warning",
            },
            "99": {
                //查询出错
                description:
                    "<p>当前未查询到您一码通下证券账户的关联关系情况，您可以再试一次，或者在交易日的9点-16点前来申请。</p>",
                link_description: "",
                link: "",
                statusIcon: "warning",
            },
        };

        //开户回访
        this.finishCallout = {
            title: "完成证券账户开户回访",
            remarks: "",
            renderWhenPass: false,
            "0": {
                description:
                    "<p>根据监管要求，需要本人完成电话回访后才能为您开立证券账户。请注意接听95310来电。</p>",
                link_description: "",
                link: "",
            },
            "1": {},
            "2": {
                description:
                    "<p>根据监管要求，需要本人完成电话回访后才能为您开立证券账户。</p>",
                link_description: "立即预约回访", //finishCallout组件处理
                link: "",
            },
            "3": {
                //finishCallout组件处理
                description: "<p></p>",
                link_description: "",
                link: "",
            },
            "4": {
                description:
                    "<p>根据监管要求，需要本人完成电话回访后才能为您开立证券账户。请注意接听95310来电。</p>",
                link_description: "",
                link: "",
            },
        };
        //信用北交所
        this.creditBjsAssertCheck = {
            title: "证券账户及资金账户近20个交易日日均资产不低于50万元",
            remarks: "",
            renderWhenPass: true,
            "0": {
                description:
                    "<p>您的账户近20个交易日日均资产为：{{assets}}元</p>" +
                    "<p>温馨提示：银证转账后次日再来试试！</p>",
                link_description: "",
                link: "",
            },
            "1": {},
            "97": {
                description: {
                    default:  "<p>当前是清算时间，不支持查询，请稍后或交易时间再来办理吧！</p>",
                },
                link_description: "",
                link: "",
                statusIcon: "warning",
            },
        };
        {
            //根据系统版本设定银证转账参数。
            if (Controller.getAppId() == "yjb3.0") {
                this.creditBjsAssertCheck["0"].link_description =
                    "银证转账";
                this.creditBjsAssertCheck["0"].link = "bankTransferV3";
            } else if (Controller.getAppId() == "yjbjyd") {
                this.creditBjsAssertCheck["0"].link_description =
                    "银证转账";
                this.creditBjsAssertCheck["0"].link = "bankTransferV2";
            } else {
            }
        }
        //信用北交所-特转A
        this.havNormalTzaBjsRules = {
            title: "未开通普通账户北交所权限",
            remarks: "",
            renderWhenPass: true,
            "0": {
                description: "在申请信用账户北交所权限前，您需要先开通普通账户北交所权限。",
                link_description: "开通普通账户北交所权限",
                link: "stockBeiQuery",
            },
            "1": {},
            "97": {
                description: {
                    default:  "<p>当前是清算时间，不支持查询，请稍后或交易时间再来办理吧！</p>",
                },
                link_description: "",
                link: "",
                statusIcon: "warning",
            },
        };

        // 产品购买双录 黑名单
        this.productDoubleRecordBlack = {
            title: "业务资格审查",
            remarks: "",
            renderWhenPass: false,
            "0": {
                description:
                    "<p>很抱歉，您的账户没有通过产品购买双录业务资格审查，暂时不能为您办理。</p>",
                link_description: "",
                link: "",
                statusIcon: "error",
            },
            "1": {},
        };

        // this.testCheck = {
        //     title: "测试用",
        //     remarks: "",
        //     renderWhenPass: false,
        //     "0": {
        //         description: "<p>11111</p>"
        //         + "<p></p>",
        //         link_description: "",
        //         link: ""
        //     },
        //     "1": {}
        // }

        this.name = {
            title: "",
            remarks: "",
            renderWhenPass: false,
            "0": {
                description: "<p></p>" + "<p></p>",
                link_description: "",
                link: "",
            },
            "1": {},
        };
        //需要动态合并的条件检查项
        this._needCombineValue = [
            {
                key: "businessTime",
                value: [
                    {
                        item_name: "businessTimeValue",
                        value_name: "item_value",
                        target_name: "acceptable_time",
                    },
                ],
            },
            {
                key: "riskEvaluationNormal",
                value: [
                    {
                        item_name: "riskLevelNameValue",
                        value_name: "item_value",
                        target_name: "riskLevelNameValue",
                    },
                ],
            },
            {
                key: "riskEvaluationProper",
                value: [
                    {
                        item_name: "riskLevelNameValue",
                        value_name: "item_value",
                        target_name: "riskLevelNameValue",
                    },
                ],
            },
            {
                key: "creditScoreAgain",
                value: [
                    {
                        item_name: "creditScoreAgainValue",
                        value_name: "item_value",
                        target_name: "credit_score",
                    },
                ],
            },
            {
                key: "firstTradeMonthSix",
                value: [
                    {
                        item_name: "firstTradeDateValue",
                        value_name: "item_value",
                        target_name: "firstTradeTime",
                    },
                    {
                        item_name: "firstTradeMonthSixValue",
                        value_name: "item_value",
                        target_name: "firstTradeMonthSixTime",
                    },
                ],
            },
            {
                key: "firstTradeYearTwo",
                value: [
                    {
                        item_name: "firstTradeDateValue",
                        value_name: "item_value",
                        target_name: "firstTradeTime",
                    },
                    {
                        item_name: "firstTradeYearTwoValue",
                        value_name: "item_value",
                        target_name: "firstTradeYearTwoTime",
                    },
                ],
            },
            {
                key: "averageAssetsShHKStock",
                value: [
                    {
                        item_name: "dailyAssetOfShHKValue",
                        value_name: "item_value",
                        target_name: "assets",
                    },
                ],
            },
            {
                key: "averageAssetsSzHKStock",
                value: [
                    {
                        item_name: "dailyAssetOfSzHKValue",
                        value_name: "item_value",
                        target_name: "assets",
                    },
                ],
            },
            {
                key: "averageAssetsDelist",
                value: [
                    {
                        item_name: "dailyAssetOfDelist",
                        value_name: "item_value",
                        target_name: "assets",
                    },
                ],
            },
            {
                key: "averageAssetsCredit",
                value: [
                    {
                        item_name: "dailyAssetOfCreditValue",
                        value_name: "item_value",
                        target_name: "assets",
                    },
                ],
            },
            {
                key: "averageAssetsLondonCdr",
                value: [
                    {
                        item_name: "averageAssetsLondonCdrValue",
                        value_name: "item_value",
                        target_name: "assets",
                    },
                ],
            },
            {
                key: "averageAssetsZXTC",
                value: [
                    {
                        item_name: "averageAssetsZXTCValue",
                        value_name: "item_value",
                        target_name: "assets",
                    },
                ],
            },
            {
                key: "averageAssetsSTIB",
                value: [
                    {
                        item_name: "averageAssetsSTIBValue",
                        value_name: "item_value",
                        target_name: "assets",
                    },
                ],
            },
            {
                key: "averageAssetsStockRotation",
                value: [
                    {
                        item_name: "averageAssetsStockRotationValue",
                        value_name: "item_value",
                        target_name: "assets",
                    },
                ],
            },
            {
                key: "averageAssetsBJXStockRotation",
                value: [
                    {
                        item_name: "averageAssetsStockBJSRotationValue",
                        value_name: "item_value",
                        target_name: "assets",
                    },
                ],
            },

            {
                key: "averageAssetKzztsStock",
                value: [
                    {
                        item_name: "averageAssetKzztsStockValue",
                        value_name: "item_value",
                        target_name: "assets",
                    },
                ],
            },
            {
                key: "averageAssetsCybStock",
                value: [
                    {
                        item_name: "averageAssetsCybStockValue",
                        value_name: "item_value",
                        target_name: "assets",
                    },
                ],
            },
            {
                key: "stibRiskLevelMatch",
                value: [
                    {
                        item_name: "stibRiskValue",
                        value_name: "item_value",
                        target_name: "stibRiskValue",
                    },
                    {
                        item_name: "clientRiskbearValue",
                        value_name: "item_value",
                        target_name: "clientRiskbearValue",
                    },
                ],
            },
            {
                key: "riskEvaluationGatherThanOneYear",
                value: [
                    {
                        item_name: "riskEvaluCorpEndDateValue",
                        value_name: "item_value",
                        target_name: "evaluEndDateValue",
                    },
                ],
            },
            {
                key: "certificateValid",
                value: [
                    {
                        item_name: "certificateValidValue",
                        value_name: "item_value",
                        target_name: "certificateValidValue",
                    },
                    {
                        item_name: "idKindValue",
                        value_name: "item_value",
                        target_name: "idKindValue",
                    },
                ],
            },
            {
                key: "certificateValidIntact",
                value: [
                    {
                        item_name: "certificateValidIntactValue",
                        value_name: "item_value",
                        target_name: "certificateValidValue",
                    },
                ],
            },
            {
                key: "checkPersonalDocument",
                value: [
                    {
                        item_name: "checkPersonalDocumentValue",
                        value_name: "item_value",
                        target_name: "checkPersonalDocumentValue",
                    },
                ],
            },
            {
                key: "occupationNotMatchAge",
                value: [
                    {
                        item_name: "professionCodeValue",
                        value_name: "item_value",
                        target_name: "professionCode",
                    },
                    {
                        item_name: "professionNameValue",
                        value_name: "item_value",
                        target_name: "professionName",
                    },
                    {
                        item_name: "ageValue",
                        value_name: "item_value",
                        target_name: "age",
                    },
                ],
            },
            {
                key: "addressRightCheck",
                value: [
                    {
                        item_name: "addressValue",
                        value_name: "item_value",
                        target_name: "address",
                    },
                ],
            },
            {
                key: "creditBjsAssertCheck",
                value: [
                    {
                        item_name: "creditBjsAssertCheckValue", //todo
                        value_name: "item_value",
                        target_name: "assets",
                    },
                ],
            },
            {
                key: "productDoubleRecordRiskEvaluationValid",
                value: [
                    {
                        item_name: "productDoubleRecordRiskLevelValue",
                        value_name: "item_value",
                        target_name: "productDoubleRecordRiskLevelValue",
                    },
                    {
                        item_name: "riskLevelNameValue",
                        value_name: "item_value",
                        target_name: "riskLevelValue",
                    },
                ],
            },
            {
                key: "riskEvaluationValidMatchBusiness",
                value: [
                    {
                        item_name: "riskEvaluCorpEndDateValue",
                        value_name: "item_value",
                        target_name: "evaluEndDateValue",
                    },
                    {
                        item_name: "businessRiskValidValue",
                        value_name: "item_value",
                        target_name: "businessRiskValidValue",
                    },
                    {
                        item_name: "clientRiskbearValue",
                        value_name: "item_value",
                        target_name: "clientRiskbearValue",
                    },
                ],
            },

        ];
        //展示顺序
        this._sort_order = [
            //业务相关
            "creditStibOpenAfterNormal",
            "custTypePersonal",
            "custTypeOrgan",
            "averageAssetsDelist",
            "averageAssetsCredit",
            "averageAssetsHKStock",
            "averageAssetsShHKStock",
            "averageAssetsSzHKStock",
            "averageAssetsLondonCdr",
            "averageAssetsSTIB",
            "averageAssetsStockRotation",
            "averageAssetsBJXStockRotation",
            "averageAssetsCybStock",
            "averageAssetKzztsStock",
            "riskEvaluationValid",
            "riskEvaluationNormal",
            "riskEvaluationProper",
            "riskEvaluationProperPassNotShow",
            "firstTradeMonthSix",
            "firstTradeYearTwo",

            "csdcCreditAStockholder",
            "creditScoreFirst",
            "creditScoreAgain",
            "csdcGemInfo",
            "accordCybInfo",
            "fundBananceCheck",
            "creditFundBananceCheck",
            "fundJourCheck",
            "securityEntrustCheck",
            "businessUnfinishCheck",
            "cybXkCsdcRisk",
            "cybZqCsdcRisk",
            "ordinarySaStockHolderHadW",
            //时间、资料有效性、完整性相关
            "businessTime",
            //"certificateValid",
            "hasKzzRight",
            "secondContactsDataIntegrity",
            "controlBenefitDataIntegrity",
            "creditRecord",
            "certificateValid",
            "certificateValidIntact",
            "checkPersonalDocument",
            "occupationNotMatchAge",
            "addressRightCheck",
            "archiveIntegrity",
            //账户异常或不允许线上办理
            "ageValid",
            "ageLessThanEighty",
            "idCard",
            "inspectionIdKind",
            "custStatusNormal",
            "fundStatusNormal",
            "custRiskInfoOK",
            // "specialListCredit",
            // "specialListHKStock",
            "specialListCheck",
            "publicSecurityCheck",
            "moneyLaunderingSurveillance",
            "integrityRecordPassed",
            //其他/补充条件检查
            "frequencyControl",
            "privateAssetCheck",
            "managementAssetCheck",
            "eligibleInvestorsFirstTradeYearTwo",
            "existNormalHsAccount",
            "finishCallout",
            "creditBjsAssertCheck",
            "havNormalTzaBjsRules",
            "productDoubleRecordRiskEvaluationValid",
            "riskEvaluationValidMatchBusiness",
            "productDoubleRecordBlack",
        ];
        this._sort_weight = {};
        for (let i = 0; i < this._sort_order.length; i++) {
            this._sort_weight[this._sort_order[i]] = i;
        }

        this._renderType = {
            certificateValid: "certificateValid",
            certificateValidIntact: "certificateValidIntact",
            //'certificateValidTc':'certificateValid',
            checkPersonalDocument: "checkPersonalDocument",
            occupationNotMatchAge: "occupationNotMatchAge",
            addressRightCheck: "addressRightCheck",
            finishCallout: "finishCallout",
            // 'testCheck':'testCheck',
        };
    }
    //向提示语中，插入变量
    renderDescription(conditionCheckValue) {
        var tokenReg = /\{\{([^\{\}]+)\}\}/g;
        return this[conditionCheckValue.item_name][
            conditionCheckValue.item_status
        ]["description"].replace(tokenReg, function(word, token) {
            //console.log(word)
            if (conditionCheckValue[token] != undefined) {
                return conditionCheckValue[token];
            } else {
                return null;
            }
        });
    }

    getTitle(conditionCheckValue) {
        if (this[conditionCheckValue.item_name] == undefined) {
            console.log(
                conditionCheckValue.item_name +
                    " undefined in ConditionCheckDict"
            );
            return conditionCheckValue.item_name;
        } else if (this[conditionCheckValue.item_name].title == undefined) {
            console.log(
                conditionCheckValue.item_name +
                    " title undefined in ConditionCheckDict"
            );
            return conditionCheckValue.item_name;
        } else if (
            this[conditionCheckValue.item_name].title instanceof Object
        ) {
            if (
                this[conditionCheckValue.item_name].title[
                    conditionCheckValue.business_type
                ] != undefined
            ) {
                return this[conditionCheckValue.item_name].title[
                    conditionCheckValue.business_type
                ];
            } else {
                return this[conditionCheckValue.item_name].title["default"];
            }
        } else {
            return this[conditionCheckValue.item_name].title;
        }
    }

    getDescription(conditionCheckValue) {
        //if(conditionCheckValue.item_status==97){
        //
        //}

        if (
            this[conditionCheckValue.item_name] == undefined ||
            this[conditionCheckValue.item_name][
                conditionCheckValue.item_status
            ] == undefined
        ) {
            console.log(
                conditionCheckValue.item_name +
                    "\t" +
                    conditionCheckValue.item_status +
                    "\t status undefined in ConditionCheckDict"
            );
            if (conditionCheckValue.item_status == 97) {
                return "<p>当前是清算时间，不支持查询，请稍后或交易时间再来办理吧！</p>";
            } else if (conditionCheckValue.item_status == 98) {
                return "<p>查询过程中摔倒了，请返回重试。</p>";
            } else if (conditionCheckValue.item_status == 99) {
                return "<p>查询过程中摔倒了，请返回重试。</p>";
            } else if (conditionCheckValue.item_status == 95) {
                return "<p>查询过程中摔倒了，请返回重试。</p>";
            } else if (conditionCheckValue.item_status == 94) {
                return "<p>当前非业务办理时间，暂不支持办理本业务。</p>";
            }
            return null;
        } else if (
            this[conditionCheckValue.item_name][conditionCheckValue.item_status]
                .description == undefined
        ) {
            console.log(
                conditionCheckValue.item_name +
                    "\t" +
                    conditionCheckValue.item_status +
                    "\t description undefined in ConditionCheckDict"
            );
            if (conditionCheckValue.item_status == 97) {
                return "<p>当前是清算时间，不支持查询，请稍后或交易时间再来办理吧！</p>";
            } else if (conditionCheckValue.item_status == 98) {
                return "<p>查询过程中摔倒了，请返回重试。</p>";
            } else if (conditionCheckValue.item_status == 99) {
                return "<p>查询过程中摔倒了，请返回重试。</p>";
            } else if (conditionCheckValue.item_status == 95) {
                return "<p>查询过程中摔倒了，请返回重试。</p>";
            } else if (conditionCheckValue.item_status == 94) {
                return "<p>当前非业务办理时间，暂不支持办理本业务。</p>";
            }
            return null;
        } else if (
            this[conditionCheckValue.item_name][conditionCheckValue.item_status]
                .description instanceof Object
        ) {
            var tokenReg = /\{\{([^\{\}]+)\}\}/g;
            if (
                this[conditionCheckValue.item_name][
                    conditionCheckValue.item_status
                ]["description"][conditionCheckValue.business_type] == undefined
            ) {
                return this[conditionCheckValue.item_name][
                    conditionCheckValue.item_status
                ]["description"]["default"].replace(tokenReg, function(
                    word,
                    token
                ) {
                    //console.log(word)
                    if (conditionCheckValue[token] != undefined) {
                        return conditionCheckValue[token];
                    } else {
                        return null;
                    }
                });
            } else {
                return this[conditionCheckValue.item_name][
                    conditionCheckValue.item_status
                ]["description"][conditionCheckValue.business_type].replace(
                    tokenReg,
                    function(word, token) {
                        //console.log(word)
                        if (conditionCheckValue[token]) {
                            return conditionCheckValue[token];
                        } else {
                            return null;
                        }
                    }
                );
            }
        } else {
            return this.renderDescription(conditionCheckValue);
        }
    }

    getLinkDescription(conditionCheckValue) {
        if (
            this[conditionCheckValue.item_name] == undefined ||
            this[conditionCheckValue.item_name][
                conditionCheckValue.item_status
            ] == undefined
        ) {
            console.log(
                conditionCheckValue.item_name +
                    "\t" +
                    conditionCheckValue.item_status +
                    "\t status undefined in ConditionCheckDict"
            );
            return null;
        } else if (
            this[conditionCheckValue.item_name][conditionCheckValue.item_status]
                .link_description == undefined
        ) {
            console.log(
                conditionCheckValue.item_name +
                    "\t" +
                    conditionCheckValue.item_status +
                    "\t link_description undefined in ConditionCheckDict"
            );
            return null;
        } else {
            return this[conditionCheckValue.item_name][
                conditionCheckValue.item_status
            ].link_description;
        }
    }

    needRender(conditionCheckValue) {
        if (
            conditionCheckValue.item_status != "1" &&
            this[conditionCheckValue.item_name] != undefined
        ) {
            //未通过
            return true;
        } else if (this[conditionCheckValue.item_name] == undefined) {
            //通过，但对应检查项无定义
            //console.log(conditionCheckValue.item_name+" undefined in ConditionCheckDict")
            return false;
        } else if (this[conditionCheckValue.item_name].renderWhenPass == true) {
            //通过，且对应检查项检查结果需要显示
            //console.log(conditionCheckValue.item_name+" "+this[conditionCheckValue.item_name].renderWhenPass)

            // 新三板业务当条件检查项riskEvaluationProper通过是不展示
            if (conditionCheckValue.business_type == '30077' && conditionCheckValue.item_name == 'riskEvaluationProper') {
                return false
            }
            return true;
        } else {
            //通过，但对应检查项结果不需要显示
            return false;
        }
    }

    renderCount(conditionCheckValueList) {
        let count = 0;
        for (let i = 0; i < conditionCheckValueList.length; i++) {
            if (this.needRender(conditionCheckValueList[i])) {
                count++;
            }
        }
        return count;
    }

    getStatusIcon(conditionCheckValue) {
        if (
            this[conditionCheckValue.item_name] != undefined &&
            this[conditionCheckValue.item_name][
                conditionCheckValue.item_status
            ] != undefined &&
            this[conditionCheckValue.item_name][conditionCheckValue.item_status]
                .statusIcon != undefined
        ) {
            return this[conditionCheckValue.item_name][
                conditionCheckValue.item_status
            ].statusIcon;
        } else if (conditionCheckValue.item_status == 1) {
            return "check";
        } else if (
            conditionCheckValue.item_status == 95 ||
            conditionCheckValue.item_status == 97 ||
            conditionCheckValue.item_status == 98 ||
            conditionCheckValue.item_status == 99 ||
            conditionCheckValue.item_status == 94
        ) {
            return "warning";
        } else {
            return "error";
        }
    }
    getSortWeight(item_name) {
        if (this._sort_weight[item_name] != undefined) {
            return this._sort_weight[item_name];
        } else {
            return 9999;
        }
    }
    renderWhenPass(business_type) {
        let hideWhenPass = [
            "30047",
            "30048",
            "30049",
            "30062",
            "30064",
            "30072",
            "30106",
            "30108",
            "30110",
        ];
        if (
            hideWhenPass.filter((ele) => {
                return ele == business_type;
            }).length > 0
        ) {
            return false;
        } else {
            return true;
        }
    }
    getConditionCheckTitle(business_type) {
        let title = {
            default: "办理条件检查",
            //'30047':'你需要先完善下列资料：',
            "30048": "你需要先完善下列资料：",
        };
        if (business_type) {
            if (title[business_type]) {
                return title[business_type];
            } else {
                return title["default"];
            }
        } else {
            return title["default"];
        }
    }
}
module.exports = new ConditionCheckDict();
