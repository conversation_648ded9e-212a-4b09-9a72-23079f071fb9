import React, { Component } from 'react';
import { render } from 'react-dom';
import Events from 'Events';
import Storage from 'Storage';
import Tool from 'Tool';
import StockAOpenRet from '../components/openRet/StockAOpenRet.jsx';
import Loading from '../components/loading/Loading.jsx';
import BaseComponent from  '../../common/vendors/BaseComponent.js'
import '../stockARetView/StockARetView.css';

class StockARetView extends BaseComponent {
	constructor(props) {
		super(props);
		this.state = {
			isLoading: true,
			openRet: {
				retDesc: []
			}
		};
		this.queryLimitNum = settings.queryStockHolderLimitNum || 10;
		this.queryNum = 0;
		this.queryTimer = null;
	}

	componentWillMount() {
		Tool.weblog({
			logId: '202212090900',
			action: 'stockARet',
			url: window.location.href
		});
	}

	componentDidMount() {
		this.appInit(data => {
			this.initPage(data);
		});

		var me = this;
		//window.GoBackOnLoad=function(){
		//    me.appInit((data)=>{
		//        me.initPage(data);
		//    })
		//}
		/*Controller.onAppear(function(){
            me.appInit((data)=>{
                me.initPage(data);
            })
        });*/
	}
	componentDidUpdate() {
		var me = this;
		window.GoBackOnLoad = function() {
			me.appInit(data => {
				me.initPage(data);
			});
		};
	}
	initPage(data) {
		let stockAOpenAsyncRet = Storage.getItem('stockAOpenAsyncRet');
		let stockAOpenType = Storage.getItem('stockAOpenType');
		if (stockAOpenAsyncRet.result && stockAOpenAsyncRet.result.request_no) {
			console.log(stockAOpenAsyncRet, stockAOpenType);
			let request_no = stockAOpenAsyncRet.result.request_no;
			this.setState(
				{
					request_no,
					stockAOpenType
				},
				() => {
					this.onQueryStockHolder();
					this.keepOnQueryStockHolder();
				}
			);
		} else {
			this.setState({ stockAOpenType, isLoading: false }, () => {
				this.openFail(stockAOpenAsyncRet['message']);
			});
		}
	}
	onQuery() {
		this.onQueryStockHolder();
	}
	keepOnQueryStockHolder() {
		let queryLimitNum = this.queryLimitNum;
		this.queryTimer = setInterval(() => {
			let queryNum = this.queryNum;
			if (queryNum < queryLimitNum) {
				queryNum++;
				this.queryNum = queryNum;
				this.onQueryStockHolder();
			}
		}, 5000);
	}
	onQueryStockHolder() {
		let request_no = this.state.request_no;
		let param = {
			UniqueKey: 'STOCKHOLDER_ASYNC_QUERY',
			op_station: '($op_station)',
			request_no: request_no
		};
		Tool.fetchTo(param, {}, 'all')
			.then(oData => {
				Tool.waiting.hide();
				console.log(oData);
				let stockAOpenRet = oData;
				this.setState({ isLoading: false });

				let openRet;

				if (+stockAOpenRet['code'] == 0) {
					let retDesc = [];
					let status = stockAOpenRet['result']['status'];
					//两个都要开通的场景
					if (stockAOpenRet['result']['sh_open'] == 1 && stockAOpenRet['result']['sz_open'] == 1) {
						if (
							stockAOpenRet['result']['sh_stock_account'] &&
							stockAOpenRet['result']['sz_stock_account']
						) {
							retDesc.push('您的上海A股账户：' + stockAOpenRet['result']['sh_stock_account']);
							retDesc.push('深圳A股账户：' + stockAOpenRet['result']['sz_stock_account']);
						} else if (
							stockAOpenRet['result']['sh_stock_account'] &&
							stockAOpenRet['result']['sz_stock_account'] == null
						) {
							retDesc.push('您的上海A股账户：' + stockAOpenRet['result']['sh_stock_account']);
							retDesc.push('深圳A股账户:' + (status == 0 ? '受理中' : '开通失败'));
						} else if (
							stockAOpenRet['result']['sh_stock_account'] == null &&
							stockAOpenRet['result']['sz_stock_account']
						) {
							retDesc.push('您的上海A股账户：' + (status == 0 ? '受理中' : '开通失败'));
							retDesc.push('深圳A股账户：' + stockAOpenRet['result']['sz_stock_account']);
						} else {
							//受理中状态

							retDesc.push('您的上海A股账户：' + (status == 0 ? '受理中' : '开通失败'));
							retDesc.push('深圳A股账户：' + (status == 0 ? '受理中' : '开通失败'));
						}
					}
					// 沪A开通，深A不开通
					if (stockAOpenRet['result']['sh_open'] == 1 && stockAOpenRet['result']['sz_open'] == 0) {
						if (stockAOpenRet['result']['sh_stock_account']) {
							state = 'success';
							retDesc.push('您的上海A股账户：' + stockAOpenRet['result']['sh_stock_account']);
						} else {
							retDesc.push('您的上海A股账户：' + (status == 0 ? '受理中' : '开通失败'));
						}
					}
					//沪A不开通，深A开通
					//{"code":0,"message":"SUCCESS","result":{"sh_open":"0","sh_stock_account":null,"status":"0","sz_open":"1","sz_stock_account":null}}
					if (stockAOpenRet['result']['sh_open'] == 0 && stockAOpenRet['result']['sz_open'] == 1) {
						if (stockAOpenRet['result']['sz_stock_account']) {
							retDesc.push('您的深圳A股账户：' + stockAOpenRet['result']['sz_stock_account']);
						} else {
							state = 'fail';
							retDesc.push('您的深圳A股账户：' + (status == 0 ? '受理中' : '开通失败'));
						}
					}

					let state = 'success';
					let failDesc = [
						'温馨提醒：新开账户下一交易日生效可交易，下挂账户需重新登录后方能生效。交易权限以交易界面提示为准，如有疑问，请咨询在线客服。'
					];
					if (status == 0) {
						//展示轮询页面
						failDesc = [];
						state = 'accept';
					}
					openRet = {
						retType: state,
						mainClass: state,
						retDesc: retDesc,
						retWord: '',
						failDesc: failDesc,
						btnName: '返回',
						queryFunc: () => {
							this.onQuery();
						},
						btnFunc: () => {
							Controller.goBackPage();
						}
					};
					this.setState(
						{
							openRet: openRet
						},
						() => {
							//清理定时器
							if (status == 1 && this.queryTimer) {
								clearInterval(this.queryTimer);
							}
						}
					);

					Tool.supportStockAuthNotify().then(support => {
						Tool.weblog({
							support: support,
							url: window.location.href
						});
						if (support) {
							// 1，普通
							// 2，信用
							let param = {
								type: 1
							};
							Tool.weblog({
								type: 1,
								url: window.location.href
							});
							jssdk.stockAuthNotify(param, function(data) {
								Tool.weblog(data);
							});
						}
					});
				} else {
					this.openFail(stockAOpenRet['message']);
				}
			})
			.catch(error => {
				Tool.waiting.hide();
				console.log(error);
			});
	}

	openFail(retMsg) {
        let openRet;
		let stockAOpenType = this.state.stockAOpenType;

		let shType = stockAOpenType['sh'];
		let szType = stockAOpenType['sz'];

		let retDesc = [];

		if (shType !== '' && szType !== '') {
			retDesc.push('您的上海A股账户：开通失败');
			retDesc.push('深圳A股账户：开通失败');
		} else {
			if (shType !== '') {
				retDesc.push('您的上海A股账户：开通失败');
			}
			if (szType !== '') {
				retDesc.push('您的深圳A股账户：开通失败');
			}
		}

		openRet = {
			retType: 'fail',
			mainClass: 'fail',
			retDesc: retDesc,
			retWord: '',
			failDesc: ['失败信息：' + retMsg, '您可以重试一次，如您需要帮助，请联系在线客服。'],
			btnName: '返回',
			queryFunc: () => {
				this.onQuery();
			},
			btnFunc: () => {
				Controller.goBackPage();
			}
		};
		this.setState(
			{
				openRet: openRet
			},
			() => {
				//清理定时器
				if (this.queryTimer) {
					clearInterval(this.queryTimer);
				}
			}
		);
	}

	render() {
		const { isLoading } = this.state;
		console.log('openRet', this.state.openRet);
		return isLoading ? (
			<Loading />
		) : (
			<div>
				<StockAOpenRet openRet={this.state.openRet} />
			</div>
		);
	}
}

render(<StockARetView />, document.querySelector('#content'));
