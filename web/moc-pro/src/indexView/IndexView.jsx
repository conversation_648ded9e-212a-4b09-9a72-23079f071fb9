import React, { Component } from 'react';
import { render } from 'react-dom';

import './IndexView.css';
import BaseComponent from  '../../common/vendors/BaseComponent.js'
import SearchView from './components/SearchView.jsx'
import Tool from '../../common/vendors/Tool';
import initCollect from '../../common/vendors/initCollect';
import { trackEvent,trackUuid } from '@sinolink/collect';

import NavOverView from './components/NavOverView.jsx';
import pageConfig from './components/pageConfig';

const {sections} = pageConfig
class IndexView extends BaseComponent {
    constructor(props) {
        console.log('首页');
        super(props);
        this.pageInit=this.pageInit.bind(this);
        this.state = {
            mode:'index', // 首页模式，'search': 为搜索模式
            isLogin:false,
            menusData:{},
        };
        if(localStorage.getItem('isSdTest') == null || localStorage.getItem('isSdTest') == '0') {
			localStorage.setItem('isSdTest', '1');
			localStorage.setItem('isSdUatTest', '1');
        }
    }
    
    componentWillMount() {
        console.log("调试cdn12");
        if(Controller.getAppId()=='yjb3.0'){
            jssdk.ready(function () {
                jssdk.setTitle({
                    enableSwipeLeftGoBack:1,
                });
            });
        }
        this.pageInit()
    }

    pageInit(){
        //token 初始化
        var instant_token = comm.TOOLS.GetQueryString("instant_token") || comm.TOOLS.GetQueryString("token");
        var old_instant_token = comm.TOOLS.getCookie('instant');
        
        if (instant_token == '' || instant_token == '(null)' || instant_token == 'no_token') {
            /**
             * 无token的情况-清空cookie，防止遗留上次session信息
             */
            comm.TOOLS.clearCookie('MOCPHPSESSID');
            comm.TOOLS.clearCookie('cgjzq_instant');
            comm.TOOLS.clearCookie('cgjzq_app_init_error');
        } else if (instant_token != old_instant_token && instant_token != null) {
            /**
             * 传入新token的情况-清空cookie，防止遗留上次session信息
             */
            comm.TOOLS.clearCookie('MOCPHPSESSID');
            comm.TOOLS.clearCookie('cgjzq_instant');
            comm.TOOLS.clearCookie('cgjzq_app_init_error');
        }

        //cookie信息初始化
        //初始化op_station和app_id信息
        let op_station = comm.TOOLS.GetQueryString("op_station");
        if (op_station != null) {
            comm.TOOLS.setCookie('op_station', op_station);
        }
        //将app_id 存储至cookie中
        let app_id = comm.TOOLS.GetQueryString("app_id");
        if (app_id != null) {
            var ua = navigator.userAgent.toLocaleUpperCase();
            if(ua.indexOf('HARMONY') > -1 && ua.indexOf('ARKWEB') > -1 && ua.indexOf('YJB') > -1) {
                app_id = "yjbweb";
            }
            comm.TOOLS.setCookie('app_id', app_id);
        }
        if(app_id=='yjb3.0'){
            //3.0适配逻辑
            Controller.closeAllPage= this.closeAllPage.bind(Controller);
            window.setTimeout(function(){
                if(instant_token == 'notoken'){
                    //已经是交易登陆状态，但是token生成失败
                    jssdk.ready(()=>{
                        jssdk.getLoginStatus((status)=>{
                            let loginStatus = status == 1 || status == 3;
                            if(loginStatus){
                                console.warn('已登陆，但生成token异常');
                                Tool.weblog({
                                    logId: '202302011448',
                                    action: 'app_init_error',
                                    appId: Controller.getAppId() || '',
                                    url: window.location.href,
                                    loginStatus: status
                                })
                                let errorInfo = {code:'-1',result:'获取用户信息异常，请重新登录！'}
                                comm.TOOLS.setCookie('app_init_error',JSON.stringify(errorInfo));
                            }
                            //console.log("this.loginStatus  ===> ",this.loginStatus)
                        });
                    });
                }
            },50)
        }
    }

    closeAllPage(loginUri, closeType, closeArgs) {
        //重置closeAllPage方法,首页不主动登出登陆状态 MOB-1018
        //除3.0外，其余方法与Controll.closeAllPage相同
        var self = this;
        if (this.getAppId() == 'yjbjyd') {
            if (comm.TOOLS.isYjbIos()) {
                var exp = new Date();
                exp.setTime(exp.getTime() + 2000);  //仅存储2秒 以防杀进程
                document.cookie = 'cgjzq_' + 'androidback' + "=" + encodeURI('1') + ";expires=" + exp.toGMTString();
                self.changeURL("http://action:3413/?");
            }
            else if (comm.TOOLS.isAndroid()) {
                var exp = new Date();
                exp.setTime(exp.getTime() + 2000);  //仅存储2秒 以防杀进程
                document.cookie = 'cgjzq_' + 'androidback' + "=" + encodeURI('1') + ";expires=" + exp.toGMTString();
                self.changeURL("http://action:3413/?");
            }
            else {
                self.changeURL("http://action:10402/?");
            }
        } else if (this.getAppId() == 'yjb3.0' || this.getAppId() == 'yjbsa') {
            //通过closeType获取登录需要的nativeLoginType
            var nativeLoginType = 1;   //默认退出---普通登录
            var closeType = closeType || 3; //默认退出到---普通交易首页
            if (+closeType == 3) {
                var nativeLoginType = 1;
            } else if (+closeType == 4) {
                var nativeLoginType = 2;
            } else if (+closeType == 5) {
                var nativeLoginType = 3;
                closeType = 3;
                jssdk.ready(function() {
                    jssdk.logout(
                        nativeLoginType,
                        function() {
                            jssdk.goBack(closeType, closeArgs);
                        },
                        function() {
                            jssdk.goBack(closeType, closeArgs);
                        }
                    );
                });
                return;
            }
            jssdk.ready(function () {
                //首页不主动登出登陆状态 MOB-1018
                jssdk.goBackPrev();//回到上个页面，而不是交易首页
            });
        } else {  //getAppId()=='yjbwx'或其他
            try {
                //适配微信
                setTimeout(function () {
                    WeixinJSBridge.call('closeWindow');
                }, 500);
            }
            catch (e) {
            }
            window.close();
        }
    }

    componentDidMount() {
        // 筛选 菜单
        this.filterMenu();
        // 等校验完登录态，再进行跳转
        let promiseLogin = new Promise((res,rej)=>{
            this.appInit((data)=>{
                if(data.initMessage==undefined || data.initMessage.clientId == undefined){
                    // 未登录
                    this.setState({isLogin:false})
                    console.log("data.initMessage====",data.initMessage)
                    // 对用户信息类报错，只弹框不跳转也不返回。因为这是首页。
                    if(data.initMessage.code!=-882 && data.initMessage.code!=-996){
                        //不是token失效，而是账户本身异常的状态
                        if(data.initMessage===false){
                            Dialog.notice("亲，网络连接断开啦！请检查网络设置。");
                        }else if(!data.initMessage.result){
                            Dialog.notice("登录超时请返回重新登录！");
                        }else if(data.initMessage.code == -990){
                            // 特殊处理 code -990 的情况
                            Dialog.showAlert(
                                "请使用主资金账户登录业务办理，如有遗忘，您可以尝试找回资金账号并重新登录。",
                                "重新登录",
                                () => {
                                    Controller.closeAllPage('',5);
                                }
                            );
                        }else if(data.initMessage.code == -991){
                            // 特殊处理 code -991 的情况
                            Dialog.showAlert(
                                "您的资金账户缺少网上交易委托权限，暂无法登录业务办理，详询服务人员或95310。",
                                "我知道了",
                                () => {
                                    jssdk.ready(function () {
                                        jssdk.goBackPrev();//回到上个页面，而不是交易首页
                                    });
                                }
                            );
                        }else{
                            Dialog.notice(data.initMessage.result);
                        }
                        comm.TOOLS.setCookie('app_init_error',JSON.stringify(data.initMessage));
                        rej(1)
                        return false;
                    }
                }else{
                    res(1)
                    this.setState({isLogin:true})
                    if(data.initMessage && data.initMessage.clientId) trackUuid(data.initMessage.clientId) // 设置uuid

                }
            },{ignoreError:true});
        })

        promiseLogin.then(res=>{
            let login_back_target = comm.TOOLS.GetQueryString("login_back");
            if (login_back_target) {
                window.setTimeout(function () {
                    // 根据sectionButton 里面的class 定位，并且手动点击
                    let selector = '.login_back_target_' + login_back_target;
                    if ($(selector).length > 0) {
                        $(selector)[0].click();
                    }
                }, 10);
            }
        })

        setTimeout(async()=> {
            await initCollect();
            await trackEvent({
                event_name: 'ywbl_view',
                page_name: "ywbl"
            });
            this.logPv();
        }, 100);
    }

    filterMenu = ()=>{
        let app_id = Controller.getAppId() || "yjbweb"; // 增加默认配置
        var ua = navigator.userAgent.toLocaleUpperCase();
        if(ua.indexOf('HARMONY') > -1 && ua.indexOf('ARKWEB') > -1 && ua.indexOf('YJB') > -1) {
            app_id = 'yjb3.0';
        }
        const menus ={};
        Object.keys(sections).forEach((key,index)=>{
           const temp = sections[key].filter(item=>item.channel_code.includes(app_id)) 
           if( temp&& temp.length>0) menus[key] = temp
        })
        this.setState({menusData:menus})
    }
    // 记录访问量
    logPv = ()=>{
        try {
            Tool.weblog({
                logId: '202212011800',
                action: 'index',
                appId: Controller.getAppId() || '',
                url: window.location.href
            })
            console.log(`Controller.changeURL('http://1.15.227.141:10015/wt/#/menu')`)
        } catch (error) {
            
        }
    }

    componentDidUpdate() {}

    // 模式切换，搜索和首页两种
    onSwitchMode = val=>{
        this.setState({
            mode:val
        })
    }

    /**
     * 查询菜单数据，目前已废弃，
     */
    queryMenusList = ()=>{
        const param = {
            UniqueKey: 'YCWBMQ',
            channel_code: Controller.getAppId(),
        }
       return Tool.queryTo(param,{},"all").then((data) => {
            console.log(data)
            if(data.code==0){
                this.setState({
                    menusData:data.result,
                })
            }else{
                // 请求失败
                console.log("获取接口失败，启动默认配置")
                this.setState({
                    menusData: sections,
                })
            }
        })
    }
    render() {
        const {mode,menusData,isLogin} = this.state;
        return (<div>
                <NavOverView menusData={menusData} onSwitchMode= {this.onSwitchMode} isLogin={isLogin} mode={mode}/>
                <SearchView onSwitchMode= {this.onSwitchMode} menusData={menusData} mode={mode}/>
            </div>);
    }
}
render(<IndexView/>, document.querySelector("#content"))