var navConfig = {

    sections: [
        // {
        //    desc: '测试业务入口',
        //    buttonList: [
        //    ]
        // },
        {
            desc: '热门业务办理',
            buttonList: [
                // 'crhInfrastructureFund',
                // 'crhCheckAccount',
                // 'professionalInvestor',
                // 'crhFundOpenTpye',
                // 'crhTaRelationInfo',
                'openStock',
                'stockBei',
                'stib',
                'startupBoardV2',
                'personal',
                'idupdate',
                //'thirdpart',
                'findDepartment',
                'holderRights',
            ]
        },
        {
            desc: '密码服务',
            buttonList: [
                'findAccount',
                'clearPwd',
                'resetTradePwd',
                'resetFundPwd',
            ]
        },
        {
            desc: '业务开通',
            buttonList: [
                'openStock',
                'openCreditAccount',
                'openfund',
                'stockBei',
                'stockA',
                'closedEndFund',
                'creditA',
                'startupboard',
                'creditStartupBoard',
                'startupBoardV2Sign',
                'openHK',
                'riskwarn',
                'delist',
                'reverseRepos',
                'londonCdr',
                'londonCdrComingSoon',
                'stib',
                'creditStib',
                'investorOpen',
                'optionAccount',
                'stockRotation',
                'cdr',
                //'creditCdr',
                 'innovation',
                // 'creditInnovation',
                'holderRights',
                'crhTaRelationInfo',
                'crhInfrastructureFund',
                'specialSecurities',//专项头寸权限
                'debentureOpen',
                'bondProfessionalInvestors_sd',
                'creditDebentureOpen',
                'specialRights',
                'delistRights',
                'delistHolderRights',
                'fundPrefrozen',
            ]
        },
        {
            desc: '账户管理',
            buttonList: [
                'risk',
                'personal',
                'idupdate',
                'specified',
                'professionalInvestor',
                'doubleRecord',
                'thirdpart',
                'creditthird',
                'crhCheckAccount',
                'creditlimit',
                'crhStockOptions',
                'wjdc',
                //'accountCancellation',
                'contractQuery',
                'reserveList',
                'mallFundTransferYJB',//场外基金转场内
                'mallFundTransferWX',//场外基金转场内
                'associationConfirm', // 关联关系确认
                //'iadviserBasic',//暂时隐藏
                'other',            
                'searchProgress',
            ]
        },

    ],
    buttons: {
        'openStock': {
            desc: '网上开户',//按钮描述文字
            className: 'openStock',//todo 按钮样式类名，用于指定图标图片
            renderLogic: {
                app_id: [
                    'yjb3.0',
                    'yjbsa',
                    'yjbwx',
                    'yjbjyd'
                ]
            },
            transferLogic: {//页面跳转逻辑
                needLogin: false,//是否需要登陆
                defaultParam: {//todo 默认跳转行为
                    groupName: 'openStock',
                    logType: 'openStock',
                }
            }
        },
        'startupboard': {
            desc: '同步创业板权限',//按钮描述文字
            className: 'startupboard-syn',//按钮样式类名，用于指定图标图片
            renderLogic: {
                app_id: [
                    'yjb3.0',
                    'yjbsa',
                    'yjbwx',
                    'yjbjyd',
                    'yjbweb',
                ]
            },
            transferLogic: {//页面跳转逻辑
                defaultParam: {//默认跳转行为
                    groupName: 'startUpBoardQuery',
                    logType: 'startupboard',
                }
            }
        },
        'personal': {
            desc: '修改个人资料',//按钮描述文字
            className: 'personal',//todo 按钮样式类名，用于指定图标图片
            renderLogic: {
                app_id: [
                    'yjb3.0',
                    'yjbsa',
                    'yjbwx',
                    'yjbjyd',
                    'yjbweb',
                ]
            },
            transferLogic: {//页面跳转逻辑
                defaultParam: {//默认跳转行为
                    groupName: 'personal',
                    logType: 'personal',
                }
            }
        },
        'idupdate': {
            desc: '身份证更新',//按钮描述文字
            className: 'idupdate',//按钮样式类名，用于指定图标图片
            renderLogic: {
                app_id: [
                    'yjb3.0',
                    'yjbsa',
                    'yjbwx',
                    'yjbjyd',
                    'yjbweb',
                ]
            },
            transferLogic: {//页面跳转逻辑
                defaultParam: {//默认跳转行为
                    groupName: 'idupdate',
                    logType: 'idupdate',
                }
            }
        },
        'thirdpart': {
            desc: '变更普通三方存管',//按钮描述文字
            className: 'thirdpart',//按钮样式类名，用于指定图标图片
            renderLogic: {
                app_id: [
                    'yjb3.0',
                    'yjbsa',
                    'yjbwx',
                    'yjbjyd'
                ]
            },
            transferLogic: {//页面跳转逻辑
                defaultParam: {//默认跳转行为
                    groupName: 'thirdpart',
                    logType: 'thirdpart',
                }
            }
        },
        'specified': {
            desc: '指定交易',//按钮描述文字
            className: 'specified',//按钮样式类名，用于指定图标图片
            renderLogic: {
                app_id: [
                    'yjb3.0',
                    'yjbsa',
                    'yjbwx',
                    'yjbjyd',
                    'yjbweb',
                ]
            },
            transferLogic: {//页面跳转逻辑
                defaultParam: {//默认跳转行为
                    groupName: 'specified',
                    logType: 'specified',
                }
            }
        },
        'findDepartment': {
            desc: '查找营业部',//按钮描述文字
            className: 'findDepartment',//todo 按钮样式类名，用于指定图标图片
            renderLogic: {
                app_id: [
                    'yjb3.0',
                    'yjbsa',
                    'yjbwx',
                    'yjbweb',
                    'yjbjyd'
                ]
            },
            transferLogic: {//页面跳转逻辑
                needLogin: false,//是否需要登陆
                defaultParam: {//默认跳转行为
                    groupName: 'findDepartment',
                    logType: 'findDepartment',
                }
            }
        },

        'findAccount': {
            desc: '找回资金账号',//按钮描述文字
            className: 'findAccount',//todo 按钮样式类名，用于指定图标图片
            renderLogic: {
                app_id: [
                    'yjb3.0',
                    'yjbsa',
                    'yjbwx',
                    'yjbjyd',
                    'yjbweb',
                ]
            },
            transferLogic: {//页面跳转逻辑
                needLogin: false,//是否需要登陆
                defaultParam: {//todo 默认跳转行为
                    groupName: 'goFindAccount',
                    logType: 'goFindAccount',
                }
            }
        },
        'clearPwd': {
            desc: '忘记密码',//按钮描述文字
            className: 'clearPwd',//todo 按钮样式类名，用于指定图标图片
            renderLogic: {//渲染逻辑，不在列表中则不渲染，如果为设置，则默认所有端都渲染
                app_id: [
                    'yjb3.0',
                    'yjbwx',
                ]
            },
            transferLogic: {//页面跳转逻辑
                needLogin: false,//是否需要登陆
                defaultParam: {//todo 默认跳转行为
                    groupName: 'goClearPwd',
                    logType: 'goClearPwd',
                }
            }
        },
        'resetTradePwd': {//todo
            desc: '修改交易密码',//按钮描述文字
            className: 'resetTradePwd',//按钮样式类名，用于指定图标图片
            renderLogic: {//渲染逻辑，不在列表中则不渲染，如果为设置，则默认所有端都渲染
                app_id: [
                    'yjb3.0',
                ]
            },
            transferLogic: {//页面跳转逻辑
                needPersonal: false,//是否需要是个人账号，默认必须是个人账号
                defaultParam: {//默认跳转行为
                    groupName: 'resetTradePwd',
                    logType: 'resetTradePwd',
                }
            }
        },
        'resetFundPwd': {//todo
            desc: '修改资金密码',//按钮描述文字
            className: 'resetFundPwd',//按钮样式类名，用于指定图标图片
            renderLogic: {//渲染逻辑，不在列表中则不渲染，如果为设置，则默认所有端都渲染
                app_id: [
                    'yjb3.0',
                ]
            },
            transferLogic: {//页面跳转逻辑
                needPersonal: false,//是否需要是个人账号，默认必须是个人账号
                defaultParam: {//默认跳转行为
                    groupName: 'resetFundPwd',
                    logType: 'resetFundPwd',
                }
            }
        },

        'openCreditAccount': {
            desc: '开通两融账户',//按钮描述文字
            className: 'openCreditAccount',//todo 按钮样式类名，用于指定图标图片
            renderLogic: {
                app_id: [
                    'yjb3.0',
                    'yjbsa',
                    'yjbwx',
                    'yjbjyd',
                    'yjbweb',
                ]
            },
            transferLogic: {//页面跳转逻辑
                needCredit: true,//是否需要有信用账号，默认不需要信用
                defaultParam: {//默认跳转行为
                    groupName: 'creditA',
                    logType: 'creditA',//todo
                }
            }
        },
        'openfund': {
            desc: '开通开放式基金',//按钮描述文字
            className: 'openfund',//按钮样式类名，用于指定图标图片
            renderLogic: {
                app_id: [
                    'yjb3.0',
                    'yjbsa',
                    'yjbwx',
                    'yjbjyd'
                ]
            },
            transferLogic: {//页面跳转逻辑
                defaultParam: {//默认跳转行为
                    groupName: 'openfund',
                    logType: 'openfund',
                }
            }
        },
        'stockA': {
            desc: '补开股东户',//按钮描述文字
            className: 'stockA',//todo 按钮样式类名，用于指定图标图片
            renderLogic: {
                app_id: [
                    'yjb3.0',
                    'yjbsa',
                    'yjbwx',
                    'yjbjyd',
                    'yjbweb',
                ]
            },
            transferLogic: {//页面跳转逻辑
                defaultParam: {//默认跳转行为
                    groupName: 'stockA',
                    logType: 'stockA',
                }
            }
        },
        'creditA': {
            desc: '补开信用股东户',//按钮描述文字
            className: 'creditA',//todo 按钮样式类名，用于指定图标图片
            renderLogic: {
                app_id: [
                    'yjb3.0',
                    'yjbsa',
                    'yjbwx',
                    'yjbjyd',
                    'yjbweb',
                ]
            },
            transferLogic: {//页面跳转逻辑
                needCredit: true,//是否需要有信用账号，默认不需要信用
                defaultParam: {//默认跳转行为
                    groupName: 'creditA',
                    logType: 'creditA',
                }
            }
        },
        'openStartupboard': {
            desc: '开通创业板',//按钮描述文字
            className: 'startupboard',//todo 按钮样式类名，用于指定图标图片
            renderLogic: {
                app_id: [
                    'yjb3.0',
                    'yjbsa',
                    'yjbwx',
                    'yjbjyd'
                ]
            },
            transferLogic: {//页面跳转逻辑
                defaultParam: {//默认跳转行为
                    groupName: 'startUpBoardQuery',
                    logType: 'startupboard',
                }
            }
        },
        'startupBoardV2': {//开通创业板
            desc: '开通创业板',//按钮描述文字
            className: 'startupboard',//todo 按钮样式类名，用于指定图标图片
            renderLogic: {
                app_id: [
                    'yjb3.0',
                    'yjbsa',
                    'yjbwx',
                    'yjbjyd',
                    'yjbweb',
                ]
            },
            transferLogic: {//页面跳转逻辑
                defaultParam: {//默认跳转行为
                    groupName: 'startUpBoardV2Query',
                    logType: 'startupboardV2',
                }
            }
        },
        'startupBoardV2Sign': {//签署新版创业板风险揭示书 MOB-1982
            desc: '签署新版创业板风险揭示书',//按钮描述文字
            className: 'startupboardSign',//todo 按钮样式类名，用于指定图标图片
            renderLogic: {
                app_id: [
                    'yjb3.0',
                    'yjbsa',
                    'yjbwx',
                    'yjbjyd'
                ]
            },
            transferLogic: {//页面跳转逻辑
                defaultParam: {//默认跳转行为
                    groupName: 'startUpBoardV2SignQuery',
                    logType: 'startupBoardV2Sign',
                }
            }
        },
        'creditStartupBoard': {//开通创业板
            desc: '开通信用创业板',//按钮描述文字  
            className: 'credit_startupboard',//todo 按钮样式类名，用于指定图标图片
            renderLogic: {
                app_id: [
                    'yjb3.0',
                    'yjbsa',
                    'yjbwx',
                    'yjbjyd',
                    'yjbweb',
                ]
            },
            transferLogic: {//页面跳转逻辑
                pageTitle:'信用创业板',
                needCredit: true,//是否需要有信用账号，默认不需要信用
                noCreditParam: {
                    groupName: 'openCreditHint',
                    logType: 'openCreditHint',
                },
                defaultParam: {//默认跳转行为
                    groupName: 'creditStartUpBoardQuery',
                    logType: 'creditStartupboard',
                }
            }
        },

        'openHK': {
            desc: '开通港股通',//按钮描述文字
            className: 'hkstock',//按钮样式类名，用于指定图标图片
            renderLogic: {
                app_id: [
                    'yjb3.0',
                    'yjbsa',
                    'yjbwx',
                    'yjbjyd',
                    'yjbweb',
                ]
            },
            transferLogic: {//页面跳转逻辑
                defaultParam: {//默认跳转行为
                    groupName: 'ggtQuery',
                    logType: 'ggt',
                }
            }
        },
        'riskwarn': {
            desc: '开通风险警示',//按钮描述文字
            className: 'riskwarn',//按钮样式类名，用于指定图标图片
            renderLogic: {
                app_id: [
                    'yjb3.0',
                    'yjbsa',
                    'yjbwx',
                    'yjbjyd'
                ]
            },
            transferLogic: {//页面跳转逻辑
                defaultParam: {//默认跳转行为
                    groupName: 'riskwarn',
                    logType: 'riskwarn',
                }
            }
        },
        'delist': {
            desc: '开通退市整理',//按钮描述文字
            className: 'delist',//按钮样式类名，用于指定图标图片
            renderLogic: {
                app_id: [
                    'yjb3.0',
                    'yjbsa',
                    'yjbwx',
                    'yjbjyd'
                ]
            },
            transferLogic: {//页面跳转逻辑
                defaultParam: {//默认跳转行为
                    groupName: 'deListQuery',
                    logType: 'delist',
                }
            }
        },
        'reverseRepos': {
            desc: '签署债券回购协议',//按钮描述文字
            className: 'reverseRepos',//todo 按钮样式类名，用于指定图标图片
            renderLogic: {
                app_id: [
                    'yjb3.0',
                    'yjbsa',
                    'yjbwx',
                    'yjbjyd'
                ]
            },
            transferLogic: {//页面跳转逻辑
                defaultParam: {//默认跳转行为
                    groupName: 'reverseReposQuery',
                    logType: 'reverseReposQuery',
                }
            }
        },

        'risk': {
            desc: '风险测评',//按钮描述文字
            className: 'risk',//按钮样式类名，用于指定图标图片
            renderLogic: {
                app_id: [
                    'yjb3.0',
                    'yjbsa',
                    'yjbwx',
                    'yjbjyd',
                    'yjbweb',
                ]
            },
            transferLogic: {//页面跳转逻辑
                needPersonal: false,//是否需要是个人账号，默认必须是个人账号
                defaultParam: {//默认跳转行为
                    groupName: 'riskResult',
                    logType: 'risk',
                }
            }
        },
        'ggtOpenAccount': {
            desc: '开立港股通账户',//按钮描述文字
            className: 'ggtOpenAccount',//按钮样式类名，用于指定图标图片
            renderLogic: {
                app_id: [
                    'yjb3.0',
                    'yjbsa',
                    'yjbwx',
                    'yjbjyd',
                    'yjbweb',
                ]
            },
            transferLogic: {//页面跳转逻辑
                needPersonal: false,//是否需要是个人账号，默认必须是个人账号
                defaultParam: {//默认跳转行为
                    groupName: 'ggtOpenAccount',
                    logType: 'ggtOpenAccount',
                }
            }
        },

        'creditthird': {
            desc: '变更信用三方存管',//按钮描述文字
            className: 'creditthird',//按钮样式类名，用于指定图标图片
            renderLogic: {
                app_id: [
                    'yjb3.0',
                    'yjbsa',
                    'yjbwx',
                    'yjbjyd'
                ]
            },
            transferLogic: {//页面跳转逻辑
                needCredit: true,//是否需要有信用账号，默认不需要信用
                defaultParam: {//默认跳转行为
                    groupName: 'creditthird',
                    logType: 'creditthird',
                }
            }
        },

        'creditlimit': {
            desc: '管理信用额度',//按钮描述文字
            className: 'creditlimit',//按钮样式类名，用于指定图标图片
            renderLogic: {
                app_id: [
                    'yjb3.0',
                    'yjbsa',
                    'yjbwx',
                    'yjbjyd'
                ]
            },
            transferLogic: {//页面跳转逻辑
                needCredit: true,//是否需要有信用账号，默认不需要信用
                defaultParam: {//默认跳转行为
                    groupName: 'creditLimit',
                    logType: 'creditlimit',
                }
            }
        },

        'wjdc': {
            desc: '问卷调查',//按钮描述文字
            className: 'wjdc',//按钮样式类名，用于指定图标图片
            renderLogic: {
                app_id: [
                    'yjb3.0',
                    'yjbsa',
                    'yjbwx',
                    'yjbjyd'
                ]
            },
            transferLogic: {//页面跳转逻辑
                needPersonal: false,//是否需要是个人账号，默认必须是个人账号
                defaultParam: {//默认跳转行为
                    groupName: 'wjdcList',
                    logType: 'wjdcList',
                }
            }
        },
        'onlineservice': {
            desc: 'onlineservice',//按钮描述文字
            className: 'onlineservice',//按钮样式类名，用于指定图标图片
            renderLogic: {
                app_id: [
                    'yjb3.0',
                    'yjbsa',
                    'yjbwx',
                    'yjbjyd'
                ]
            },
            transferLogic: {//页面跳转逻辑
                needLogin: false,//是否需要有信用账号，默认不需要信用
                defaultParam: {//默认跳转行为
                    groupName: 'onlineService',
                    logType: 'onlineservice',
                }
            }
        },
        'londonCdr': {
            desc: '开通沪伦通',//按钮描述文字
            className: 'londonCdr',//todo 按钮样式类名，用于指定图标图片
            renderLogic: {
                app_id: [
                    'yjb3.0',
                    'yjbsa',
                    'yjbwx',
                    'yjbjyd'
                ]
            },
            transferLogic: {//页面跳转逻辑
                defaultParam: {//默认跳转行为
                    groupName: 'londonCdrQuery',
                    logType: 'london_cdr',
                }
            }
        },
        'londonCdrComingSoon': {
            desc: '开通沪伦通',//按钮描述文字
            className: 'londonCdr',//todo 按钮样式类名，用于指定图标图片
            renderLogic: {
                app_id: [
                    'yjb3.0',
                    'yjbsa',
                    'yjbwx',
                    'yjbjyd'
                ]
            },
            transferLogic: {//页面跳转逻辑
                defaultParam: {//默认跳转行为
                    groupName: 'londonCdrComingSoon',
                    logType: 'london_cdr',
                }
            }
        },

        'reservation': {
            desc: '预约业务',//按钮描述文字
            className: 'reservation',//todo 按钮样式类名，用于指定图标图片
            renderLogic: {//渲染逻辑，不在列表中则不渲染，如果为设置，则默认所有端都渲染
                app_id: [
                    'yjb3.0',
                ]
            },
            transferLogic: {//页面跳转逻辑
                defaultParam: {//默认跳转行为
                    groupName: 'reservationList',
                    logType: 'reservationList',
                }
            }
        },
        'other': {
            desc: '其他业务',//按钮描述文字
            className: 'other',//todo 按钮样式类名，用于指定图标图片
                renderLogic: {//渲染逻辑，不在列表中则不渲染，如果为设置，则默认所有端都渲染
                    app_id: [
                        'yjb3.0',
                    ]
                },
            transferLogic: {//页面跳转逻辑
                defaultParam: {//默认跳转行为
                    // groupName: 'reservationList',
                    // logType: 'reservationList',
                    groupName: 'accountCancellation_sd',
                    logType: 'accountCancellation_sd',  
                }
            }
        },

        'stib': {
            desc: '开通科创板',//按钮描述文字
            className: 'stib',//todo 按钮样式类名，用于指定图标图片
            renderLogic: {
                app_id: [
                    'yjb3.0',
                    'yjbsa',
                    'yjbwx',
                    'yjbjyd',
                    'yjbweb',
                ]
            },
            transferLogic: {//页面跳转逻辑
                defaultParam: {//默认跳转行为
                    groupName: 'stibQuery',
                    logType: 'stib',
                }
            }
        },
        'creditStib': {
            desc: '开通信用科创板',//按钮描述文字
            className: 'creditStib',//todo 按钮样式类名，用于指定图标图片
            renderLogic: {
                app_id: [
                    'yjb3.0',
                    'yjbsa',
                    'yjbwx',
                    'yjbjyd',
                    'yjbweb',
                ]
            },
            transferLogic: {//页面跳转逻辑
                pageTitle:'信用科创板',
                needCredit: true,//是否需要有信用账号，默认不需要信用
                noCreditParam: {
                    groupName: 'openCreditHint',
                    logType: 'openCreditHint',
                },
                defaultParam: {//默认跳转行为
                    groupName: 'creditStibQuery',
                    logType: 'creditStib',
                }
            }
        },
        'stockBei': {
            desc: '开通北交所权限',//按钮描述文字
            className: 'stockBei',//todo 按钮样式类名，用于指定图标图片
            renderLogic: {
                app_id: [
                    'yjb3.0',
                    'yjbsa',
                    'yjbwx',
                    'yjbjyd',
                    'yjbweb',
                ]
            },
            transferLogic: {//页面跳转逻辑
                defaultParam: {//默认跳转行为
                    groupName: 'stockBeiQuery',
                    logType: 'stockBei',
                }
            }
        },
        'debentureOpen': {
            desc: '开通公司债/企业债权限',//按钮描述文字
            className: 'ptxz',//todo 按钮样式类名，用于指定图标图片
            renderLogic: {
                app_id: [
                    'yjb3.0',
                    'yjbsa',
                    'yjbjyd',
                    'yjbweb',
                ]
            },
            transferLogic: {//页面跳转逻辑
                defaultParam: {//默认跳转行为
                    groupName: 'debentureQuery',
                    logType: 'debentureQuery',
                }
            }
        },
        'creditDebentureOpen': {
            desc: '开通信用公司债/企业债权限',//按钮描述文字
            className: 'xyxz',//todo 按钮样式类名，用于指定图标图片
            renderLogic: {
                app_id: [
                    'yjb3.0',
                    'yjbsa',
                    'yjbjyd',
                    'yjbweb',
                ]
            },
            transferLogic: {//页面跳转逻辑
                pageTitle:'信用公司债/企业债权限',
                needCredit: true,//是否需要有信用账号，默认不需要信用
                noCreditParam: {
                    groupName: 'openCreditHint',
                    logType: 'openCreditHint',
                },
                defaultParam: {//默认跳转行为
                    groupName: 'creditDebentureQuery',
                    logType: 'creditDebentureQuery',
                }
            }
        },
        'investorOpen':{
            desc: '合格投资者认证',//按钮描述文字
            className: 'investorOpen',//todo 按钮样式类名，用于指定图标图片
            renderLogic: {
                app_id: [
                    'yjb3.0',
                ]
            },
            transferLogic: {//页面跳转逻辑  
                defaultParam: {//默认跳转行为
                    groupName: 'investorList',
                    logType: 'investorList',
                }
            }
        },
        'closedEndFund': {
            desc: '下挂封闭式基金',//按钮描述文字
            className: 'closedEndFund',//
            renderLogic: {
                app_id: [
                    'yjb3.0',
                    'yjbsa',
                    'yjbwx',
                    'yjbjyd'
                ]
            },
            transferLogic: {//页面跳转逻辑
                defaultParam: {//默认跳转行为
                    groupName: 'closedEndFund',
                    logType: 'closedEndFund',
                }
            }
        },
        'accountCancellation': {//已隐藏
            desc: '销户业务',//按钮描述文字
            className: 'accountCancellation',//todo 按钮样式类名，用于指定图标图片
            renderLogic: {//渲染逻辑，不在列表中则不渲染，如果为设置，则默认所有端都渲染
                app_id: [
                    'yjb3.0'
                ]
            },
            transferLogic: {//页面跳转逻辑
                defaultParam: {//默认跳转行为
                    groupName: 'accountCancellation',
                    logType: 'ac',
                }
            }
        },
        'contractQuery': {
            desc: '电子协议查询',//按钮描述文字
            className: 'contractQuery',//todo 按钮样式类名，用于指定图标图片
            renderLogic: {
                app_id: [
                    'yjb3.0',
                    'yjbsa',
                    'yjbwx',
                    'yjbjyd'
                ]
            },
            transferLogic: {//页面跳转逻辑
                needLogin: true,//是否需要登陆，默认需要登陆
                needCredit: false,//是否需要有信用账号，默认不需要信用
                orgParam: {
                    groupName: 'orgHint',
                    logType: 'orgHint',
                },
                defaultParam: {//默认跳转行为
                    groupName: 'contractQuery',
                    logType: 'contractQuery',
                }
            }
        },
        'reserveList': {
            desc: '预约业务',//按钮描述文字
            className: 'reserveList',//todo 按钮样式类名，用于指定图标图片
            renderLogic: {//渲染逻辑，不在列表中则不渲染，如果为设置，则默认所有端都渲染
                app_id: [
                    'yjb3.0',
                ]
            },
            transferLogic: {//页面跳转逻辑          
                defaultParam: {//默认跳转行为
                    groupName: 'reserveListModule',
                    logType: 'reserveList',
                }
            }
        },
        'doubleRecord': {
            desc: '产品购买双录',//按钮描述文字
            className: 'doubleRecord',//todo 按钮样式类名，用于指定图标图片
            renderLogic: {//渲染逻辑，不在列表中则不渲染，如果为设置，则默认所有端都渲染
                app_id: [
                    'yjb3.0',
                ]
            },
            transferLogic: {//页面跳转逻辑          
                defaultParam: {//默认跳转行为
                    groupName: 'doubleRecordList',
                    logType: 'doubleRecordList',
                }
            }
        },
        'optionAccount': {
            desc: '开通股票期权',//按钮描述文字
            className: 'optionAccount',//todo 按钮样式类名，用于指定图标图片
            renderLogic: {//渲染逻辑，不在列表中则不渲染，如果为设置，则默认所有端都渲染
                app_id: [
                    'yjb3.0',
                ]
            },
            transferLogic: {//页面跳转逻辑          
                defaultParam: {//默认跳转行为
                    groupName: 'optionAccount',
                    logType: 'optionAccount',
                }
            }
        },
        'stockRotation': {
            desc: '开通新三板',//按钮描述文字
            className: 'stockRotation',//todo 按钮样式类名，用于指定图标图片
            renderLogic: {
                app_id: [
                    'yjb3.0',
                    'yjbsa',
                    'yjbwx',
                    'yjbjyd'
                ]
            },
            transferLogic: {//页面跳转逻辑
                defaultParam: {//默认跳转行为
                    groupName: 'stockRotation',
                    logType: 'stockRotation',
                }
            }
        },
        'iadviserBasic': {
            desc: '签约标准咨询服务',//按钮描述文字
            className: 'iadviserBasic',//按钮样式类名，用于指定图标图片
            renderLogic: {
                app_id: [
                    'yjb3.0',
                    'yjbsa',
                    'yjbwx',
                    'yjbjyd'
                ]
            },
            transferLogic: {//页面跳转逻辑
                defaultParam: {//默认跳转行为
                    groupName: 'iadviserBasic',
                    logType: 'iadviserBasic',
                }
            }
        },

        'cdr': {
            desc: 'CDR',//按钮描述文字
            className: 'innovation_cdr',//按钮样式类名，用于指定图标图片
            renderLogic: {
                app_id: [
                    'yjb3.0',
                    'yjbsa',
                    'yjbwx',
                    'yjbjyd'
                ]
            },
            transferLogic: {//页面跳转逻辑
                defaultParam: {//默认跳转行为
                    groupName: 'cdrQuery',
                    logType: 'cdr',
                }
            }
        },
        'creditCdr': {
            desc: '信用CDR',//按钮描述文字
            className: 'credit_innovation_cdr',//按钮样式类名，用于指定图标图片
            renderLogic: {
                app_id: [
                    'yjb3.0',
                    'yjbsa',
                    'yjbwx',
                    'yjbjyd'
                ]
            },
            transferLogic: {//页面跳转逻辑
                pageTitle:'信用CDR',
                needCredit: true,//是否需要有信用账号，默认不需要信用
                noCreditParam: {
                    groupName: 'openCreditHint',
                    logType: 'openCreditHint',
                },

                defaultParam: {//默认跳转行为
                    groupName: 'creditCdrQuery',
                    logType: 'creditCdr',
                }
            }
        },
        'innovation': {
            desc: '创新企业股票',//按钮描述文字
            className: 'innovation_stock',//按钮样式类名，用于指定图标图片
            renderLogic: {
                app_id: [
                    'yjb3.0',
                    'yjbsa',
                    'yjbwx',
                    'yjbjyd'
                ]
            },
            transferLogic: {//页面跳转逻辑
                defaultParam: {//默认跳转行为
                    groupName: 'innovationStockQuery',
                    logType: 'innovationStock',
                }
            }
        },
        'creditInnovation': {
            desc: '信用创新企业股票',//按钮描述文字
            className: 'credit_innovation_stock',//按钮样式类名，用于指定图标图片
            renderLogic: {
                app_id: [
                    'yjb3.0',
                    'yjbsa',
                    'yjbwx',
                    'yjbjyd'
                ]
            },
            transferLogic: {//页面跳转逻辑
                pageTitle:'信用创新企业股票',
                needCredit: true,//是否需要有信用账号，默认不需要信用
                noCreditParam: {
                    groupName: 'openCreditHint',
                    logType: 'openCreditHint',
                },

                defaultParam: {//默认跳转行为
                    groupName: 'creditInnovationStockQuery',
                    logType: 'creditInnovationStock',
                }
            }
        },

        'professionalInvestor': {
            desc: '专业投资者认定',//按钮描述文字
            className: 'professionalInvestor',//按钮样式类名，用于指定图标图片
            renderLogic: {
                app_id: [
                    'yjb3.0',
                    'yjbsa',
                    'yjbwx',
                    'yjbjyd'
                ]
            },
            transferLogic: {//页面跳转逻辑
                defaultParam: {//默认跳转行为
                    groupName: 'professionalInvestor',
                    logType: 'professionalInvestor',
                }
            }
        },
        'bondProfessionalInvestor': {
            desc: '开通债券专业投资者权限',//按钮描述文字
            className: 'professionalInvestor',//按钮样式类名，用于指定图标图片
            renderLogic: {
                app_id: [
                    'yjb3.0'
                ]
            },
            transferLogic: {//页面跳转逻辑
                needPersonal: false,
                defaultParam: {//默认跳转行为
                    groupName: 'bondProfessionalInvestor',
                    logType: 'bondProfessionalInvestor',
                }
            }
        },
        'specialSecurities': {
            desc: '开通专项头寸权限',//按钮描述文字
            className: 'special_securities',//按钮样式类名，用于指定图标图片
            renderLogic: {
                app_id: [
                    'yjb3.0',
                    'yjbsa',
                    'yjbwx',
                    'yjbjyd',
                    // 'yjbweb',
                ]
            },
            transferLogic: {//页面跳转逻辑
                pageTitle:'专项头寸权限',
                needCredit: true,//是否需要有信用账号，默认不需要信用
                noCreditParam: {
                    groupName: 'openCreditHint',
                    logType: 'openCreditHint',
                },
                defaultParam: {//默认跳转行为
                    groupName: 'specialSecuritiesQuery',
                    logType: 'specialSecurities',
                }
            }
        },
        'fundPrefrozen': {
            desc: '开通中签资金预冻结权限',//按钮描述文字
            className: 'fundPrefrozen',//按钮样式类名，用于指定图标图片
            renderLogic: {
                app_id: [
                    'yjb3.0',
                    'yjbweb',
                    // 'yjbsa',
                    'yjbwx',
                    // 'yjbjyd'
                ]
            },
            transferLogic: {//页面跳转逻辑
                defaultParam: {//默认跳转行为
                    groupName: 'fundPrefrozen',
                    logType: 'fundPrefrozen',
                }
            }
        },
        'holderRights': {
            desc: '开通可转债',//按钮描述文字
            className: 'holderRights',//按钮样式类名，用于指定图标图片
            renderLogic: {
                app_id: [
                    'yjb3.0',
                    'yjbweb',
                    // 'yjbsa',
                    'yjbwx',
                    // 'yjbjyd'
                ]
            },
            transferLogic: {//页面跳转逻辑
                defaultParam: {//默认跳转行为
                    groupName: 'holderRights',
                    logType: 'holderRights',
                }
            }
        },
        'delistHolderRights': {
            desc: '开通可转债退市整理权限',//按钮描述文字
            className: 'delistHolderRights',//按钮样式类名，用于指定图标图片
            renderLogic: {
                app_id: [
                    'yjb3.0',
                ]
            },
            transferLogic: {//页面跳转逻辑
                defaultParam: {//默认跳转行为
                    groupName: 'delistHolderRights',
                    logType: 'delistHolderRights',
                }
            }
        },
        'delistRights': {
            desc: '开通两网及退市股票权限',//按钮描述文字
            className: 'delistRights',//按钮样式类名，用于指定图标图片
            renderLogic: {
                app_id: [
                    'yjb3.0',
                    'yjbweb',
                    // 'yjbsa',
                    'yjbwx',
                    // 'yjbjyd'
                ]
            },
            transferLogic: {//页面跳转逻辑
                defaultParam: {//默认跳转行为
                    groupName: 'delistRights',
                    logType: 'delistRights',
                }
            }
        },
        'specialRights': {
            desc: '开通特转A账户',//按钮描述文字
            className: 'specialRights',//按钮样式类名，用于指定图标图片
            renderLogic: {
                app_id: [
                    'yjb3.0',
                    'yjbweb',
                    // 'yjbsa',
                    'yjbwx',
                    // 'yjbjyd'
                ]
            },
            transferLogic: {//页面跳转逻辑
                defaultParam: {//默认跳转行为
                    groupName: 'specialRights',
                    logType: 'specialRights',
                }
            }
        },
        'crhInfrastructureFund': {
            desc: '开通基础设施基金',//按钮描述文字
            className: 'crhInfrastructureFund',//按钮样式类名，用于指定图标图片
            renderLogic: {
                app_id: [
                    'yjb3.0',
                    //'yjbwx',
                    //'yjbweb',
                ]
            },
            transferLogic: {//页面跳转逻辑
                defaultParam: {//默认跳转行为
                    groupName: 'crhInfrastructureFund',
                    logType: 'crhInfrastructureFund',
                }
            }
        },
        'crhStockOptions': {
            desc: '期权买入额度调整',//按钮描述文字
            className: 'crhStockOptions',//按钮样式类名，用于指定图标图片
            renderLogic: {
                app_id: [
                    'yjb3.0',
                    'yjbwx',//todo
                    //'yjbweb',
                ]
            },
            transferLogic: {//页面跳转逻辑
                defaultParam: {//默认跳转行为
                    groupName: 'crhStockOptions',
                    logType: 'crhStockOptions',
                }
            }
        },
        'crhCheckAccount': {
            desc: '电子对账单',//按钮描述文字
            className: 'crhCheckAccount',//按钮样式类名，用于指定图标图片
            renderLogic: {
                app_id: [
                    'yjb3.0',
                    'yjbwx',//todo
                    //'yjbweb',
                ]
            },
            transferLogic: {//页面跳转逻辑
                defaultParam: {//默认跳转行为
                    groupName: 'crhCheckAccount',
                    logType: 'crhCheckAccount',
                }
            }
        },
        'openPensionAccount': {
            desc: '开通养老金账户',//按钮描述文字
            className: 'openPensionAccount',//按钮样式类名，用于指定图标图片
            renderLogic: {
                app_id: [
                    'yjb3.0',
                ]
            },
            transferLogic: {//页面跳转逻辑
                defaultParam: {//默认跳转行为
                    groupName: 'openPensionAccount',
                    logType: 'openPensionAccount',
                }
            }
        },
         'openkcbczc': {
            desc: '开通科创板成长层',//按钮描述文字
            className: 'openkcbczc',//todo 按钮样式类名，用于指定图标图片
            renderLogic: {
                app_id: [
                    'yjb3.0'
                ]
            },
            transferLogic: {//页面跳转逻辑
                defaultParam: {//默认跳转行为
                    groupName: 'openkcbczc',
                    logType: 'openkcbczc',
                }
            }
        },
        'openxykcbczc': {
            desc: '开通信用科创板成长层',//按钮描述文字
            className: 'openxykcbczc',//todo 按钮样式类名，用于指定图标图片
            renderLogic: {
                app_id: [
                    'yjb3.0'
                ]
            },
            transferLogic: {//页面跳转逻辑
                pageTitle:'开通信用科创板成长层',
                needCredit: true,//是否需要有信用账号，默认不需要信用
                noCreditParam: {
                    groupName: 'openCreditHint',
                    logType: 'openCreditHint',
                },
                defaultParam: {//默认跳转行为
                    groupName: 'openxykcbczc',
                    logType: 'openxykcbczc',
                }
            }
        },
        // 'crhFundOpenTpye': {
        //     desc: 'test-开放式基金开户',//按钮描述文字
        //     className: 'riskwarn',//按钮样式类名，用于指定图标图片
        //     renderLogic: {
        //         app_id: [
        //             'yjb3.0',
        //             'yjbwx',//todo
        //             //'yjbweb',
        //         ]
        //     },
        //     transferLogic: {//页面跳转逻辑
        //         defaultParam: {//默认跳转行为
        //             groupName: 'crhFundOpenTpye',
        //             logType: 'crhFundOpenTpye',
        //         }
        //     }
        // },
        'crhTaRelationInfo': {
            desc: '证券基金账户对应关系维护',//按钮描述文字
            className: 'crhTaRelationInfo',//按钮样式类名，用于指定图标图片
            renderLogic: {
                app_id: [
                    'yjb3.0',
                    'yjbwx',
                    //'yjbweb',
                ]
            },
            transferLogic: {//页面跳转逻辑
                defaultParam: {//默认跳转行为
                    groupName: 'crhTaRelationInfo',
                    logType: 'crhTaRelationInfo',
                }
            }
        },
        'mallFundTransferWX': {//要求交易登陆
            desc: '场外基金转场内',//按钮描述文字
            className: 'mallFundTransfer',//按钮样式类名，用于指定图标图片
            renderLogic: {
                app_id: [
                    'yjbwx',
                ]
            },
            transferLogic: {//页面跳转逻辑
                needLogin: true,//需要登陆
                needPersonal: false,//个人号/机构号/产品号
                defaultParam: {//默认跳转行为
                    groupName: 'mallFundTransfer',
                    logType: 'mallFundTransfer',
                }
            }
        },
        'mallFundTransferYJB': {//不要求交易登录
            desc: '场外基金转场内',//按钮描述文字
            className: 'mallFundTransfer',//按钮样式类名，用于指定图标图片
            renderLogic: {
                app_id: [
                    'yjb3.0',
                ]
            },
            transferLogic: {//页面跳转逻辑
                needLogin: false,//不需要登陆-由bridge.html处理弱登录逻辑
                needPersonal: false,//个人号/机构号/产品号
                defaultParam: {//默认跳转行为
                    groupName: 'mallFundTransfer',
                    logType: 'mallFundTransfer',
                }
            }
        },
        'associationConfirm': {//不要求交易登录
            desc: '关联关系确认',//按钮描述文字
            className: 'associationConfirm',//按钮样式类名，用于指定图标图片
            renderLogic: {
                app_id: [
                    'yjb3.0',
                ]
            },
            transferLogic: {//页面跳转逻辑
                needLogin: true,//不需要登陆-由bridge.html处理弱登录逻辑
                defaultParam: {//默认跳转行为
                    groupName: 'associationConfirm',
                    logType: 'associationConfirm',
                }
            }
        },
        'searchProgress': {//不要求交易登录
            desc: '业务办理进度查询',//按钮描述文字
            className: 'associationConfirm',//按钮样式类名，用于指定图标图片
            renderLogic: {
                app_id: [
                    'yjb3.0',
                    'yjbweb', // TODO开发展示，后续删掉
                ]
            },
            transferLogic: {//页面跳转逻辑
                needLogin: true,
                needPersonal: false,//个人号/机构号/产品号
                defaultParam: {//默认跳转行为
                    groupName: 'searchProgress',
                    logType: 'searchProgress',
                }
            }
        },

        'exerciseFinancing': {
            desc: '开通行权融资权限',//按钮描述文字
            className: 'exerciseFinancing',//按钮样式类名，用于指定图标图片
            renderLogic: {
                app_id: [
                    'yjb3.0',
                    // 'yjbweb',
                    // 'yjbsa',
                    // 'yjbwx',
                    // 'yjbjyd'
                ]
            },
            transferLogic: {//页面跳转逻辑
                defaultParam: {//默认跳转行为
                    groupName: 'exerciseFinancing',
                    logType: 'exerciseFinancing',
                }
            }
        },
        'equityIncentiveSign': {
            desc: '股权激励授权签署',//按钮描述文字
            className: 'equityIncentiveSign',//按钮样式类名，用于指定图标图片
            renderLogic: {
                app_id: [
                    'yjb3.0'
                ]
            },
            transferLogic: {//页面跳转逻辑
                defaultParam: {//默认跳转行为
                    groupName: 'equityIncentiveSign',
                    logType: 'equityIncentiveSign',
                }
            }
        },
        
        'pTradeQMT': {
            desc: '股权激励授权签署',//按钮描述文字
            className: 'pTradeQMT',//按钮样式类名，用于指定图标图片
            renderLogic: {
                app_id: [
                    'yjb3.0'
                ]
            },
            transferLogic: {//页面跳转逻辑
                defaultParam: {//默认跳转行为
                    groupName: 'pTradeQMT',
                    logType: 'pTradeQMT',
                }
            }
        },

        'marginRenewal': {
            desc: '开通两融合约展期自动申请权限',//按钮描述文字
            className: 'marginRenewal',//按钮样式类名，用于指定图标图片
            renderLogic: {
                app_id: [
                    'yjb3.0',
                    'yjbweb',
                    // 'yjbsa',
                    'yjbwx',
                    // 'yjbjyd'
                ]
            },
            transferLogic: {//页面跳转逻辑
                defaultParam: {//默认跳转行为
                    groupName: 'marginRenewal',
                    logType: 'marginRenewal',
                }
            }
        },
        'onLineOpenStock': {
            desc: '全线上开户',//按钮描述文字
            className: 'onLineOpenStock',//按钮样式类名，用于指定图标图片
            renderLogic: {
                app_id: [
                    'yjb3.0',
                    'yjbweb',
                    // 'yjbsa',
                    'yjbwx',
                    // 'yjbjyd'
                ]
            },
            transferLogic: {//页面跳转逻辑
                defaultParam: {//默认跳转行为
                    groupName: 'onLineOpenStock',
                    logType: 'onLineOpenStock',
                }
            }
        },
        'quoteRepurchase': {
            desc: '开通报价回购权限',//按钮描述文字
            className: 'quoteRepurchase',//按钮样式类名，用于指定图标图片
            renderLogic: {
                app_id: [
                    'yjb3.0',
                    'yjbweb',
                    // 'yjbsa',
                    'yjbwx',
                    // 'yjbjyd'
                ]
            },
            transferLogic: {//页面跳转逻辑
                defaultParam: {//默认跳转行为
                    groupName: 'quoteRepurchase',
                    logType: 'quoteRepurchase',
                }
            }
        },
        'marginTradeReserve': {
            desc: '两融预约开户',//按钮描述文字
            className: 'marginTradeReserve',//按钮样式类名，用于指定图标图片
            renderLogic: {
                app_id: [
                    'yjb3.0',
                    // 'yjbweb',
                    // 'yjbsa',
                    // 'yjbwx',
                    // 'yjbjyd'
                ]
            },
            transferLogic: {//页面跳转逻辑
                defaultParam: {//默认跳转行为
                    groupName: 'marginTradeReserve',
                    logType: 'marginTradeReserve',
                }
            }
        },
        'creditStockBei': {
            desc: '开通信用北交所权限',//按钮描述文字
            className: 'creditStockBei',//todo 按钮样式类名，用于指定图标图片
            renderLogic: {
                app_id: [
                    'yjb3.0',
                    'yjbsa',
                    'yjbwx',
                    'yjbjyd',
                    'yjbweb',
                ]
            },
            transferLogic: {//页面跳转逻辑
                pageTitle:'信用北交所权限',
                needCredit: true,//是否需要有信用账号，默认不需要信用
                noCreditParam: {
                    groupName: 'openCreditHint',
                    logType: 'openCreditHint',
                },
                defaultParam: {//默认跳转行为
                    groupName: 'creditStockBeiQuery',
                    logType: 'creditStockBei',
                }
            }
        },
        'qptionSimulated': {
            desc: '开通股票期权全真模拟交易账户',//按钮描述文字
            className: 'qptionSimulated',//todo 按钮样式类名，用于指定图标图片
            renderLogic: {
                app_id: [
                    'yjb3.0',
                    'yjbwx',
                ]
            },
            transferLogic: {//页面跳转逻辑
                pageTitle:'开通股票期权全真模拟交易账户',
                needLogin: true,
                needPersonal: false,
                defaultParam: {//默认跳转行为
                    groupName: 'qptionSimulated',
                    logType: 'qptionSimulated',
                }
            }
        },



        //'test': {
        //    desc: 'test',//按钮描述文字
        //    className: 'test',//按钮样式类名，用于指定图标图片
        //    renderLogic: {//渲染逻辑，不在列表中则不渲染，如果为设置，则默认所有端都渲染
        //        app_id: [
        //            'yjb3.0',
        //            'yjbsa',
        //            'yjbwx',
        //            'yjbjyd'
        //        ]
        //    },
        //    transferLogic: {//页面跳转逻辑
        //        needLogin: true,//是否需要登陆
        //        needPersonal: true,//是否需要是个人账号，默认必须是个人账号
        //        needCredit: false,//是否需要有信用账号，默认不需要信用
        //        defaultParam: {//默认跳转行为
        //            groupName: 'orgTip',
        //            logType: 'orgTip',
        //        }
        //    }
        //},
        //'tmp': {
        //    desc: '',//按钮描述文字
        //    className: '',//按钮样式类名，用于指定图标图片
        //    renderLogic: {//渲染逻辑，不在列表中则不渲染，如果为设置，则默认所有端都渲染
        //        app_id: [
        //            'yjb3.0',
        //            'yjbsa',
        //            'yjbwx',
        //            'yjbjyd'
        //        ]
        //    },
        //    transferLogic: {//页面跳转逻辑
        //        needLogin: true,//是否需要登陆
        //        needPersonal: true,//是否需要是个人账号，默认必须是个人账号
        //        needCredit: false,//是否需要有信用账号，默认不需要信用
        //        defaultParam: {//默认跳转行为
        //            groupName: '',
        //            logType: '',
        //        }
        //    }
        //},


    },
    defaultTransferLogic: {
        //pageTitle:'',//机构户/信用账号提示页标题，默认不设置使用desc字段
        needLogin: true,//是否需要登陆，默认需要登陆
        //unLoginParam:{
        //    logType:'',
        //    groupName:'',
        //},
        needPersonal: true,//是否需要是个人账号，默认必须是个人账号
        orgParam: {
            groupName: 'orgTip',
            logType: 'orgTip',
        },
        needCredit: false,//是否需要有信用账号，默认不需要信用
        noCreditParam: {
            groupName: 'openCredit',
            logType: 'openCredit',
        },
        defaultParam: {//默认跳转行为
        },
    },
    // defaultRenderLogic: {
    //     app_id: [
    //         'yjb3.0',
    //         'yjbsa',
    //         'yjbwx',
    //         'yjbjyd'
    //     ]
    // },


}

/**
 *
 */
// var testButtons=[
//     'test1',
//     'test2',
//     'test3',
//     'test4',
//     'test5',
//     'test6',
//     // 'test7',
//     // 'test8',
//     // 'test9',
//     // 'test10',
// ];
// navConfig.sections[0].buttonList=testButtons.concat(navConfig.sections[0].buttonList)
// var testButtonsConfig={
//     'test1': {
//         desc: '测试-预约列表',//按钮描述文字
//         className: 'riskwarn',//todo 按钮样式类名，用于指定图标图片
//         transferLogic: {//页面跳转逻辑
//             defaultParam: {//默认跳转行为
//                 groupName: 'reserveListModule',
//                 logType: 'reserveListModule',
//             }
//         }
//     },
//     'test2': {
//         desc: '测试-预约-身份证',//按钮描述文字
//         className: 'riskwarn',//todo 按钮样式类名，用于指定图标图片
//         transferLogic: {//页面跳转逻辑
//             defaultParam: {//默认跳转行为
//                 groupName: 'test2',
//                 logType: 'reservationList',
//             }
//         }
//     },
//     'test3': {
//         desc: '测试-预约-辅证',//按钮描述文字
//         className: 'riskwarn',//todo 按钮样式类名，用于指定图标图片
//         transferLogic: {//页面跳转逻辑
//             defaultParam: {//默认跳转行为
//                 groupName: 'test3',
//                 logType: 'test3',
//             }
//         }
//     },
//     'test4': {
//         desc: '测试-预约-头像',//按钮描述文字
//         className: 'riskwarn',//todo 按钮样式类名，用于指定图标图片
//         transferLogic: {//页面跳转逻辑
//             defaultParam: {//默认跳转行为
//                 groupName: 'test4',
//                 logType: 'test4',
//             }
//         }
//     },
//     'test5': {
//         desc: '测试-预约-结果',//按钮描述文字
//         className: 'riskwarn',//todo 按钮样式类名，用于指定图标图片
//         transferLogic: {//页面跳转逻辑
//             defaultParam: {//默认跳转行为
//                 groupName: 'test5',
//                 logType: 'test5',
//             }
//         }
//     },
//     'test6': {
//         desc: '测试-预约-视频见证',//按钮描述文字
//         className: 'riskwarn',//todo 按钮样式类名，用于指定图标图片
//         transferLogic: {//页面跳转逻辑
//             defaultParam: {//默认跳转行为
//                 groupName: 'test6',
//                 logType: 'test6',
//             }
//         }
//     },
//     'test7': {
//         desc: '身份证',//按钮描述文字
//         className: 'riskwarn',//todo 按钮样式类名，用于指定图标图片
//         transferLogic: {//页面跳转逻辑
//             defaultParam: {//默认跳转行为
//                 groupName: 'test7',
//                 logType: 'test7',
//             }
//         }
//     },
//     'test8': {
//         desc: '辅证',//按钮描述文字
//         className: 'riskwarn',//todo 按钮样式类名，用于指定图标图片
//         transferLogic: {//页面跳转逻辑
//             defaultParam: {//默认跳转行为
//                 groupName: 'test8',
//                 logType: 'test8',
//             }
//         }
//     },
//     'test9': {
//         desc: '视频见证',//按钮描述文字
//         className: 'riskwarn',//todo 按钮样式类名，用于指定图标图片
//         transferLogic: {//页面跳转逻辑
//             defaultParam: {//默认跳转行为
//                 groupName: 'test9',
//                 logType: 'test9',
//             }
//         }
//     },
//     'test10': {
//         desc: '销户信息确认',//按钮描述文字
//         className: 'riskwarn',//todo 按钮样式类名，用于指定图标图片
//         transferLogic: {//页面跳转逻辑
//             defaultParam: {//默认跳转行为
//                 groupName: 'test10',
//                 logType: 'test10',
//             }
//         }
//     },



// }
// navConfig.buttons=Object.assign({},navConfig.buttons, testButtonsConfig);
/**/

export default navConfig;