import  beijiaosuo from "../img/beijiaosuo.png"
import  chanpinshuanglu from "../img/chanpinshuanglu.png"
import  chuangyeban from "../img/chuangyeban.png"
import  gerenziliao from "../img/gerenziliao.png"
import  kaihu from "../img/kaihu.png"
import  kechuangban from "../img/kechuangban.png"
import  kezhuanzhai from "../img/kezhuanzhai.png"
import  sanfang from "../img/sanfang.png"


import  gantan from "../img/gantan.png"
import  gupiaoqiquan from "../img/gupiaoqiquan.png"
import  hot from "../img/hot.png"
import  jiantou from "../img/jiantou.png"
import  kefu from "../img/kefu.png"
import  mima from "../img/mima.png"
import  point from "../img/point.png"
import  qita from "../img/qita.png"
import  quanxian from "../img/quanxian.png"
import  rongzirongquan from "../img/rongzirongquan.png"
import  shalou from "../img/shalou.png"
import  zhanghufuwu from "../img/zhanghufuwu.png"
import  newIcon from "../img/new.png"

// 热门业务映射和固定分类的和通用的一些icon映射
export function logoIconMap(key){
    const keyMap ={
        "beijiaosuo":beijiaosuo,
        "chanpinshuanglu":chanpinshuanglu,
        "chuangyeban":chuangyeban,
        "gerenziliao":gerenziliao,
        "kaihu":kaihu,
        "kechuangban":kechuangban,
        "kezhuanzhai":kezhuanzhai,
        "sanfang":sanfang,

        "gantan":gantan,
        "股票期权":gupiaoqiquan,
        "hot":hot,
        "jiantou":jiantou,
        "kefu":kefu,
        "密码服务":mima,
        "point":point,
        "其他业务":qita,
        "权限开通":quanxian,
        "融资融券":rongzirongquan,
        "shalou":shalou,
        "账户服务":zhanghufuwu,
    }
    return keyMap[key]
}

// 普通菜单的icon映射
export function subMenuIconMap(key){
    const keyMap ={
        '找回资金账号':hot,
        '开通信用北交所权限':newIcon,
        '开通科创板成长层':newIcon,
        '开通信用科创板成长层':newIcon
    }
    return keyMap[key]
}

// 埋点的简写映射
export function collectMenuMap(key){
    const keyMap ={
        '热门业务':"rm",
        '密码服务':"mm",
        '账户服务':"zh",
        '权限开通':"qx",
        '融资融券':"rzrq",
        '股票期权':"gpqq",
        '其他业务':"qt",
    }
    return keyMap[key]
}


export default {
    logoIconMap,
    subMenuIconMap,
    collectMenuMap
}