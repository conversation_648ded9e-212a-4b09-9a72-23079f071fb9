import React, { Component } from 'react';
import { List, Switch } from 'antd-mobile';

export default class SdTest extends Component {
	constructor(props) {
		super(props);
		this.state = {
			isSdTest: localStorage.getItem('isSdTest') === '1',
			isSdUatTest: localStorage.getItem('isSdUatTest') === '1',
			isSdSitEnv: localStorage.getItem('isSdSitEnv') === '1'
		};
	}

	render() {
		return (
			<List>
				<List.Item
					extra={
						<Switch
							checked={this.state.isSdTest}
							onChange={() => {
								this.setState({
									isSdTest: !this.state.isSdTest
								});
								localStorage.setItem('isSdTest', this.state.isSdTest ? '0' : '1');
							}}
						/>
					}>
					思迪开关
				</List.Item>
				{/* <List.Item
					extra={
						<Switch
							checked={this.state.isSdUatTest}
							onChange={() => {
								this.setState({
									isSdUatTest: !this.state.isSdUatTest
								});
								localStorage.setItem('isSdUatTest', this.state.isSdUatTest ? '0' : '1');
							}}
						/>
					}>
					uat开关
				</List.Item> */}
				<List.Item
					extra={
						<Switch
							checked={this.state.isSdSitEnv}
							onChange={() => {
								this.setState({
									isSdSitEnv: !this.state.isSdSitEnv
								});
								localStorage.setItem('isSdSitEnv', this.state.isSdSitEnv ? '0' : '1');
							}}
						/>
					}>
					sit开关
				</List.Item>
				<List.Item>
					<a
						onClick={() => {
							Controller.toThinkive('https://fzsdbusiness.yjbtest.com/views/home');
						}}>
						思迪sit首页
					</a>
					<div onClick={() => {
						jssdk.getUserUKeysByChannelId(function (res) {
							console.log(res);
						});
					}}>
						调试
					</div>
				</List.Item>
				<List.Item>
					<a
						onClick={() => {
							let param = {
								userName:"gh_72c8f90dfeae", // 淘号基金
							}
							jssdk.openMiniProgram(param,(res)=>{
								console.log(res);
							});
						}}>
						打开建行小程序
					</a>
				</List.Item>
			</List>
		);
	}
}
