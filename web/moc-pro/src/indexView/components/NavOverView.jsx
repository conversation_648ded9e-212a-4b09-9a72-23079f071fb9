import React, { Component } from 'react';
import { render } from 'react-dom';
import { Tabs,Grid,Modal} from 'antd-mobile';
import classnames from 'classnames'
import { throttle } from 'lodash'
import { trackEvent } from '@sinolink/collect';

import OnlineService from './OnlineService.jsx'
import SectionButtons from '../../components/IconItem/SectionButtons.jsx';
import AnomalousView from './AnomalousView.jsx'
import TitleBar from './TitleBar.jsx'
import {logoIconMap,subMenuIconMap,collectMenuMap} from './utils.js'
import SdTest from './SdTest.jsx'

import shalouPng from '../img/shalou.png'
import jiantouPng from '../img/jiantou.png'
import iconDefaultPng from '../img/icon_default.png'
import hotTitlePng from '../img/hot-title.png'
import searchIcon from '../img/searchIcon.png'

import  '../scss/NavOverView.scss'


const tabHeight = 44 ;// tab 栏高度

const androidHeight = 45;// 安卓安全区固定高度
const searchBarHeight = 54; // 搜索栏高度
const paddingHieght = 35; // padding 高度
const marginHieght = -60; // padding 高度

export default class NavOverView extends Component {
    constructor(props) {
        super(props);
        this.state = {
            currentKey: 0,
            commonMenu:[],// 普通业务
            hotMenu:[],// 热门业务
            isLogin:props.isLogin, // 是否登录
            // processList:[],// 业务办理查询进度
            isFreshErrorMsg:1, // 更新异常信息
        };
        this.isHarmony = false;
        var ua = navigator.userAgent.toLocaleUpperCase();
        if(ua.indexOf('HARMONY') > -1 && ua.indexOf('ARKWEB') > -1 && ua.indexOf('YJB') > -1) {
            this.isHarmony = true;
        }
        if (Controller.getAppId() == 'yjb3.0') {
            this.isYJBAPP = true;
            this.TitleBarHeight = 44; // 
        } else {
            this.isYJBAPP = false;
            this.TitleBarHeight = 0;
        }
        this.bodyRef = null;  
    }

    componentWillReceiveProps(nextProps) {
        this.updateProps(nextProps)
        const {isLogin} = nextProps;
        if(this.props.isLogin!=isLogin){
           this.initData(isLogin);
        }
    }

    initData = (isLogin)=>{
        if(isLogin && this.isYJBAPP){
            // 如果从未登录，变成了登录，并且是佣金宝app
            // this.queryProcess();
        }
    }

    // 查询业务办理查询进度
    // queryProcess=()=>{
    //     const param = {
    //         UniqueKey: 'BTPS',
    //         client_id: '($usercode)',
    //         op_station: "($op_station)"
    //     }
    //     Tool.fetchTo(param).then(data=>{
    //         if(data.code==0){
    //             this.setState({processList:data.result||[]})
    //         }
    //     })
    // }

    // 更新数据
    updateProps =(props)=>{
        if(props&&props.menusData){
            const sections =  props.menusData
            const commonSection =  []
            Object.keys(sections).forEach((key,index)=>{
               if(key!="热门业务"){
                   commonSection.push({title:key,button:sections[key]})
               }
           })
    
            this.setState({
                commonMenu: commonSection,
                hotMenu:sections["热门业务"]
            })
        }
       
    }

    componentDidMount(){
        this.initData(this.props.isLogin);
        document.body.style.overflow="hidden"
        
        this.bodyRef.addEventListener('scroll', this.handleScroll)
        this.updateProps(this.props);
        this.settingBanner();

        // webview 刷新
        Controller.onAppear(()=>{
            this.initData(this.props.isLogin);// 返回页面时候，重新渲染
            this.shouldUpdateError();// 更新异常提醒的模块
        });

    }
    // 埋点
    colloet = ()=>{
        trackEvent({
            event_name: 'ywbl_view',
            page_name: "ywbl"
        });
    }

    // 刷新顶部异常场景-个人资料
    shouldUpdateError=()=>{
        const {isFreshErrorMsg} = this.state
        this.setState({
            isFreshErrorMsg:isFreshErrorMsg+1
        })
    }

    componentWillUnmount() {
        this.bodyRef.removeEventListener('scroll', this.handleScroll)
    }

    // 设置广告位
    settingBanner = ()=>{

        window.Ad && window.Ad.init({
            fetchParams: {
            "adLocations":window.settings.adLink,
        },
        swiperConfig: {
            loop: false,
            autoplay: true,
          },
        context: ['.swiper-container1'],
    });
    }


    // 滑动事件监听
    handleScroll = throttle(() => {
        const {currentKey,commonMenu} = this.state;
        const safeHeight = comm.TOOLS.isAndroid()&&this.isYJBAPP ? androidHeight : Number(String(getComputedStyle(document.documentElement).getPropertyValue("--sat")).split('px')[0])
        const tabs = commonMenu.map(item=>{return {title:item.title,key:item.title}})
        let newKey = currentKey;
        const fixedHeight = tabHeight + this.TitleBarHeight + searchBarHeight+safeHeight
        for (const [index, item] of tabs.entries()) {
            const element = document.getElementById(`anchor-${item.key}`)
            if (!element) continue
            const rect = element.getBoundingClientRect()
            if (rect.top <= fixedHeight + marginHieght) {
                newKey = index+1
            } else if(index==0 && rect.top >= fixedHeight ){
                newKey = 0 
            }else{
                break
            }
        }
        // for (const [index, item] of tabs.entries()) {
        //     const element = document.getElementById(`anchor-${item.key}`)
        //     if (!element) continue
        //     const rect = element.getBoundingClientRect()
        //     if (rect.top <= tabHeight +  this.TitleBarHeight + searchBarHeight + paddingHieght +safeHeight) {
        //         newKey = index
        //     } else {
        //         break
        //     }
        // }
        
        this.setState({
            currentKey: newKey,
        })
    }, 100, { leading: true, trailing: true })

    // tab 点击事件
    tabChange = async (tab, index)=>{
        console.log("点击了tab切换事件index",index)
        console.log("tab==",tab)

        const safeHeight = comm.TOOLS.isAndroid()&&this.isYJBAPP ? androidHeight : Number(String(getComputedStyle(document.documentElement).getPropertyValue("--sat")).split('px')[0])

        this.bodyRef.removeEventListener('scroll', this.handleScroll)
        // 优化滚动，scrollIntoView 会压住上方
        this.bodyRef.scrollTo({
            top: this.bodyRef.scrollTop + document.getElementById(`anchor-${tab.key}`).getBoundingClientRect().top 
            - tabHeight - safeHeight - searchBarHeight - this.TitleBarHeight - paddingHieght,
        })

        // document.getElementById(`anchor-${tab.key}`).scrollIntoView(true)
        // this.bodyRef.scrollTo({
        //     top: this.bodyRef.scrollTop - tabHeight ,
        // })
        this.setState({ currentKey: index },()=>{
            window.setTimeout(() => this.bodyRef.addEventListener('scroll', this.handleScroll), 100)
        })
    }
    
    // 跳转到搜索页面
    goToSearch = ()=>{
        this.props.onSwitchMode("search")
        // 搜索埋点
        trackEvent({
            event_name: 'ywbl_click',
            page_name: "ywbl",
            module_name:"top",
            element_name:"search",
            location:""
        });
    }

    // 埋点
    collectPoint = (module_name,element_name)=>{
        trackEvent({
            event_name: 'ywbl_click',
            page_name: "ywbl",
            module_name:collectMenuMap(module_name),
            element_name,
            location:""
        });
    }

    // 同花顺调试
    thsClick = () => {
        console.log('同花顺渠道')
        Controller.thsCameraNative()
    }

    // 退出登录
    logoutTest = () => {        
		jssdk.ready(function () {
            jssdk.logout(
                19,
                function() {
                    jssdk.goBack(3, '');
                },
                function() {
                    jssdk.goBack(3, '');
                }
            );
		});
    }

    render() {
        const {commonMenu,hotMenu,isLogin,processList,currentKey,isFreshErrorMsg} = this.state;
        // const numberProcess = processList.filter(item=>item.status!=0 && item.status != 1 );// 统计业务办理条数,除了成功/失败，其他状态都是审核中
        const hasOnline = settings.onlineChannelType?settings.onlineChannelType.includes(sessionStorage.getItem('channel_type')||"") :false;
        return (
            <div className={this.isYJBAPP?"section-containers":classnames('section-containers','bodys')} style={{display:this.props.mode=="index"?"":"none"}}> 
                <div className={this.isYJBAPP?"section-top":classnames('section-top','bodys')}>
                    {
                        this.isYJBAPP?<TitleBar />:""
                    }
                    <div className='nav-searchs'>
                        <div className='nav-input'  onClick={this.goToSearch}>
                            <div className='nav-input-search'>
                                <img src={searchIcon}/>
                            </div>
                            请输入需要办理的业务名称
                        </div>
                        {
                            this.isYJBAPP || hasOnline?"":(
                                <div className='nav-search-kefu'>
                                    <OnlineService />
                                </div>
                            )
                        }
                    </div>
                </div>
                <div className='bodys-content' ref={ref => this.bodyRef = ref}>
                    <AnomalousView isYJBAPP={this.isYJBAPP} isLogin={this.props.isLogin} isFreshErrorMsg={isFreshErrorMsg}/>
                    {
                        this.isYJBAPP || this.isHarmony ? <div className='nav-business'>
                            <div  className='nav-bus-content'>
                                <div className='nav-bus-title'>
                                    <img className='nav-bus-img' src={shalouPng}/>
                                    业务办理进度查询
                                </div>
                                <div className='nav-bus-desc'>
                                    <SectionButtons value="searchProgress" >
                                        {/* {
                                            isLogin || numberProcess.length<=0?<span>点击查看业务办理进度</span>:
                                            <span><span className='nav-bus-number'>{numberProcess.length}</span> 条业务正在办理中</span>
                                        }*/}
                                        <span>点击查看业务办理进度</span>
                                        <img className="nav-bus-gointo" src={jiantouPng}/>
                                    </SectionButtons>   
                                </div>
                                </div>
                            </div>:""
                    }
                    <div style={{width: '100%', height: '50px', lineHeight: '50px'}} onClick={()=>{Modal.alert('调试开关', <SdTest />)}}>调试开关</div>
                    {sessionStorage.getItem('channel_type') === '2005000000000' && <div style={{width: '100%', height: '50px', lineHeight: '50px'}} onClick={this.thsClick}>同花顺照片上传调试</div>}
                    {this.isYJBAPP && <div style={{width: '100%', height: '50px', lineHeight: '50px'}} onClick={this.logoutTest}>退出登录</div>}
                    <div className='nav-body'>
                        {
                            hotMenu && hotMenu.length>0?(
                                <div>
                                     <div className='nav-hot'><div className='nav-hot-logo'><img className='nav-hot-img' src={hotTitlePng}/></div>热门业务</div>
                                    <div className='nav-grid'>
                                        <Grid 
                                            hasLine={false} 
                                            square={false}
                                            data={hotMenu.map(item=>{
                                                return {
                                                    icon:logoIconMap(item.icon) || iconDefaultPng,
                                                    text: item.business_name,
                                                    value:item.link,
                                                    shortDesc:item.shortDesc,
                                                }
                                            })} 
                                            renderItem={dataItem => (
                                                <div style={{ padding: '2' }} onClick={()=>this.collectPoint("热门业务",dataItem.shortDesc)}>
                                                    <SectionButtons value={dataItem.value} >
                                                        <img src={dataItem.icon} style={{ width: '30px', height: '30px' }} alt="" />
                                                        <div style={{ color: '#333333', fontSize: '14px', marginTop: '2px' ,    padding: '0 5px'}}>
                                                            <span> {dataItem.text}</span>
                                                        </div>
                                                    </SectionButtons>
                                                </div>
                                            )}
                                        />
                                    </div>
                                </div>
                            ):""
                        }
                    <div className='nav-carousel'>
                        <div className = 'swiper-container1'></div>  
                    </div>
                    <div className={this.isYJBAPP?'nav-tab':classnames('nav-tab','nav-tabs')} >
                        <Tabs tabs={commonMenu.map(item=>{return {title:item.title,key:item.title}})}
                            onChange={this.tabChange}
                            animated={true} 
                            useOnPan={true}
                            usePaged={false}
                            page={currentKey}
                            tabBarUnderlineStyle={{width:'20px',marginLeft:'12.5%', transform:'translateX(-10px)',height:0,background:'#F0392F', border:'2px #F0392F solid'}}
                            renderTabBar={props => <Tabs.DefaultTabBar {...props} page={4} />}>
                        </Tabs>
                    </div>
                    <div className='nav-menu'>
                        {
                          commonMenu.map((item,index)=>{
                              return (
                                <div id={`anchor-${item.title}`} key={item.title}  className="nav-menu-body">
                                    <div className='nav-menu-title'>
                                        <div className='nav-menu-title-logo'><img className='nav-img' src={logoIconMap(item.title) }/></div>
                                         {item.title}
                                        </div>
                                    <div className='nav-menu-item'>
                                        {
                                        item.button.map(items=>{
                                                return (
                                                    <div className='nav-menu-button'>
                                                        <div className='nav-menu-title-logo'>
                                                            <img src={logoIconMap("point")} className='nav-img-small' />
                                                        </div>
                                                        <span className='nav-menu-button-span' onClick={()=>this.collectPoint(item.title,items.shortDesc)}>
                                                            <SectionButtons value={items.link} >
                                                                {items.business_name}
                                                                {
                                                                    items.icon && subMenuIconMap(items.business_name)?(
                                                                            <img src={subMenuIconMap(items.business_name)} className='nav-img-mid' />
                                                                    ):""
                                                                }
                                                            </SectionButtons>  
                                                        </span>
                                                    </div>
                                                )
                                            })
                                        }
                                    </div>  
                                </div>
                              )
                          })
                        }
                    </div>
                </div>
                </div>
            </div>
        );
    }
}
