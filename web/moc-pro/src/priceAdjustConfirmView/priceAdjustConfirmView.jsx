import React from 'react';
import { render } from 'react-dom';
import BaseComponent from '../../common/vendors/BaseComponent.js';
import Button from '../components/button/Button.jsx';
import ApplyResult from './ApplyResult/ApplyResult.jsx';
import ContractAlert from './ContractAlert/ContractAlert.jsx';
import {
	ApplyStatusMap,
	ResultSceneFromMap,
	AdjustTypeMap,
	ConfirmItemMap,
	ApplyResultToResultMap
} from './utils/config';
import { priceConfirmFetch, getContract, signContract } from './utils/tool';

import './priceAdjustConfirmView.css';

// 融资利率补充协议号
const financingContractId = '5080';
const delaySecond = 20;

class PriceAdjustConfirmView extends BaseComponent {
	constructor(props) {
		super(props);
		this.state = {
			showContent: false,
			showStatus: false,
			showContract: false,
			showHint: false
		};
		this.commissionInfo = {};
		this.adjustConfig = {};
		this.codeFrom = ResultSceneFromMap.APPEAR;
		this.alreadyReadContract = false;
	}

	initPage = appInfo => {
		const { initMessage, moduleData, groupData } = appInfo;
		const commissionInfo = moduleData.result;
		const search = groupData && groupData.param && groupData.param.search;
		const applyId = comm.TOOLS.parseSearchString(search, 'applyId');
		const pageType = comm.TOOLS.parseSearchString(search, 'pageType');

		if (!search || !applyId) {
			Dialog.notice('缺少applyId参数');
			setTimeout(function() {
				Controller.goBackPage();
			}, 3000);
			return;
		}
		if (initMessage.clientId && commissionInfo.clientId && initMessage.clientId !== commissionInfo.clientId) {
			if (commissionInfo.adjustType === AdjustTypeMap.financing_rate.name) {
				Dialog.notice('当前客户号与调利率申请不一致');
			} else {
				Dialog.notice('当前客户号与调佣申请不一致');
			}

			setTimeout(function() {
				Controller.goBackPage();
			}, 3000);
			return;
		}
		if (commissionInfo.applyId && commissionInfo.applyId !== applyId) {
			Dialog.notice('调拥信息返回错误');
			setTimeout(function() {
				Controller.goBackPage();
			}, 3000);
			return;
		}

		// 期权账户数据合并处理
		if (commissionInfo.adjustType == 'commission_rate' && commissionInfo.adjustProject.length) {
			this.setState({
				showHint: commissionInfo.serviceProductList && commissionInfo.serviceProductList.length > 0
			});
			let adjustProject = [...commissionInfo.adjustProject];
			// ETFapplyType6、7合并
			if (adjustProject.filter(v => v.applyType == '6').length > 0) {
				let adjustProject7 = adjustProject.filter(v => v.applyType == '7')[0];
				adjustProject = adjustProject.filter(item => {
					if (item.applyType == '6') {
						item.applyTypeName = '期权账户（ETF）';
						item.afterValueDesc = item.afterValueDesc + '<br/>' + adjustProject7.afterValueDesc;
						item.realFeeRateDesc = item.realFeeRateDesc + '<br/>' + adjustProject7.realFeeRateDesc;
					}
					return item.applyType != '7' ? item : false;
				});
			}
			// 股票applyType8、9合并
			if (adjustProject.filter(v => v.applyType == '8').length > 0) {
				let adjustProject9 = adjustProject.filter(v => v.applyType == '9')[0];
				adjustProject = adjustProject.filter(item => {
					if (item.applyType == '8') {
						item.applyTypeName = '期权账户（股票）';
						item.afterValueDesc = item.afterValueDesc + '<br/>' + adjustProject9.afterValueDesc;
						item.realFeeRateDesc = item.realFeeRateDesc + '<br/>' + adjustProject9.realFeeRateDesc;
					}
					return item.applyType != '9' ? item : false;
				});
			}
			// 其他品种佣金调整,处于最末
			if (adjustProject.filter(v => v.applyType == '5').length > 0) {
				let adjustProject5 = adjustProject.filter(v => v.applyType == '5')[0];
				adjustProject = adjustProject.filter(v => v.applyType != '5');
				adjustProject.push(adjustProject5);
			}
			commissionInfo.adjustProject = adjustProject;
		}

		this.commissionInfo = commissionInfo;

		// 先使用订单查询结果中的status
		this.status = commissionInfo.status;
		this.shouldSignContract =
			commissionInfo.adjustType === AdjustTypeMap.financing_rate.name &&
			+commissionInfo.isShow === 1 &&
			+commissionInfo.status === ApplyStatusMap.CONFIRM;
		this.adjustTypeConfig = AdjustTypeMap[commissionInfo.adjustType];
		this.applyId = applyId;

		if (pageType === 'result') {
			// 审核通过的推送跳转进入本页面
			switch (+commissionInfo.status) {
				case ApplyStatusMap.CONFIRM:
					// 展示待用户确认
					Controller.setTitle({ title: '确认单' });
					this.showApplyContentWithConfirm();
					break;
				case ApplyStatusMap.AUDIT_SUCCESS:
					// 展示审核通过内容
					Controller.setTitle({ title: '服务提醒' });
					this.showApplyContentWithSuccess();
					break;
				case ApplyStatusMap.EXPIRED:
				case ApplyStatusMap.REVOKE:
				case ApplyStatusMap.APPLY:
				case ApplyStatusMap.AUDIT:
				case ApplyStatusMap.AUDIT_FAIL:
				case ApplyStatusMap.AUDIT_PROCESS:
				case ApplyStatusMap.AUDIT_PASS:
				case ApplyStatusMap.AUDIT_NOTPASS:
				case ApplyStatusMap.DELETE:
				default:
					Controller.setTitle({ title: '确认单' });
					this.showApplyResult();
					break;
			}
		} else {
			// 确认单推送、IM卡片的跳转进入本页面
			Controller.setTitle({ title: '确认单' });
			switch (+commissionInfo.status) {
				case ApplyStatusMap.CONFIRM:
					this.showApplyContentWithConfirm();
					break;
				case ApplyStatusMap.EXPIRED:
				case ApplyStatusMap.REVOKE:
				case ApplyStatusMap.APPLY:
				case ApplyStatusMap.AUDIT:
				case ApplyStatusMap.AUDIT_SUCCESS:
				case ApplyStatusMap.AUDIT_FAIL:
				case ApplyStatusMap.AUDIT_PROCESS:
				case ApplyStatusMap.AUDIT_PASS:
				case ApplyStatusMap.AUDIT_NOTPASS:
				case ApplyStatusMap.DELETE:
				default:
					this.showApplyResult();
					break;
			}
		}
	};

	// delay 是否需要20秒强制阅读
	showContractView = async (isDelay = false) => {
		const setStateAndDelay = isDelay => {
			this.setContractDelay = isDelay;
			this.setState({
				showContent: true,
				showContract: true
			});
		};
		if (!this.financingContract) {
			getContract(financingContractId)
				.then(contractResp => {
					const contractResult = contractResp.result;
					this.financingContract = {
						name: contractResult.econtract_name,
						content: contractResult.econtract_content
					};
					setStateAndDelay(isDelay);
				})
				.catch(err => {
					console.log('协议获取失败', err);
				});
		} else {
			setStateAndDelay(isDelay);
		}
	};

	showApplyContentWithConfirm = () => {
		if (this.shouldSignContract && !this.alreadyReadContract) {
			this.showContractView(true);
		}

		this.adjustConfig = {
			title: this.adjustTypeConfig.confirmTitle,
			tip: this.adjustTypeConfig.confirmTip,
			name: this.adjustTypeConfig.name
		};
		this.setState({
			showContent: true,
			showStatus: false
		});
	};

	showApplyContentWithSuccess = () => {
		this.adjustConfig = {
			title: this.adjustTypeConfig.successTitle,
			tip: this.adjustTypeConfig.successTip,
			name: this.adjustTypeConfig.name
		};
		this.isAuditSuccess = true;
		this.setState({
			showContent: true,
			showStatus: false
		});
	};

	showApplyResult = () => {
		this.setState({
			showContent: true,
			showStatus: true
		});
	};

	confirmFetchAndNext = () => {
		priceConfirmFetch(this.applyId)
			.then(confirmResp => {
				this.status = confirmResp.code;
				this.codeFrom = ResultSceneFromMap.CONFIRM;
				this.showApplyResult();
			})
			.catch(err => {
				if (ApplyResultToResultMap.hasOwnProperty(err.code)) {
					this.status = err.code;
					this.codeFrom = ResultSceneFromMap.CONFIRM;
					this.showApplyResult();
				} else {
					this.shouldBlockTapApply = false;
					console.log('调佣单确认接口调用失败', err);
				}
			});
	};

	onTapCloseContractAlert = () => {
		this.alreadyReadContract = true;
		this.setState({ showContract: false });
	};

	onTapContractName = () => {
		// 弹框展示协议
		this.showContractView();
	};

	onTapApply = async () => {
		if (this.shouldBlockTapApply) return;
		if (this.shouldSignContract) {
			if (!this.refs.checkboxContract.checked) {
				Dialog.notice('请先确认阅读协议后再提交哦！', '', 2);
				return;
			} else {
				this.shouldBlockTapApply = true;
				signContract(financingContractId)
					.then(signResp => {
						this.confirmFetchAndNext();
					})
					.catch(err => {
						this.shouldBlockTapApply = false;
						console.log('协议签署失败', err);
					});
			}
		} else {
			this.shouldBlockTapApply = true;
			this.confirmFetchAndNext();
		}
	};

	componentDidMount() {
		const this_ = this;
		this.appInit(appInfo => {
			this_.initPage(appInfo);
		});

		Controller.onAppear(function() {
			this_.appInit(appInfo => {
				this_.initPage(appInfo);
			});
		});

		// test code
		// this.initPage({
		// 	initMessage: {
		// 		fundAccount: '********'
		// 	},
		// 	groupData: {
		// 		param: {
		// 			search: '?applyId=10104'
		// 		}
		// 	},
		// 	moduleData: {
		// 		code: 0,
		// 		message: 'success',
		// 		result: {
		// 			applyId: '10104',
		// 			clientName: '佣金宝',
		// 			fundAccount: '********',
		// 			adjustType: 'commission_rate',
		// 			status: 1,
		// 			financingRate: 5.1,
		// 			createTime: '2020-09-10 00:00:00',
		// 			isShow: 0,
		// 			serviceProductList: [
		// 				{
		// 					prodName: '影子账户1',
		// 					signTime: '2020-09-10',
		// 					instructions: 'https://baidu'
		// 				},
		// 				{
		// 					prodName: '影子账户2',
		// 					signTime: '2020-09-10',
		// 					instructions: 'https://baidu'
		// 				}
		// 			],
		// 			adjustProject: [
		// 				{
		// 					applyType: 1,
		// 					applyTypeName: '融资利率',
		// 					afterValue: '0.01',
		// 					otherCategoryDesc: '',
		// 					afterValueDesc: '万1', // 基础佣金调整后万分位描述。样例：万3
		// 					serviceProductFeeRate: '0.05', // 增值服务费率（千分位）
		// 					serviceProductFeeRateDesc: '万5', // 增值服务服务产品万分位描述。样例：万3
		// 					realFeeRate: '0.05', // 实际佣金（千分位）
		// 					realFeeRateDesc: '万5' // 实际佣金万分位描述。样例：万6
		// 				},
		// 				{
		// 					applyType: 2,
		// 					applyTypeName: '普通账户',
		// 					afterValue: '万6',
		// 					otherCategoryDesc: '', // 调整后基础佣金（‰）
		// 					afterValueDesc: '', // 基础佣金调整后万分位描述。样例：万3
		// 					serviceProductFeeRate: '', // 增值服务费率（千分位）
		// 					serviceProductFeeRateDesc: '', // 增值服务服务产品万分位描述。样例：万3
		// 					realFeeRate: '', // 实际佣金（千分位）
		// 					realFeeRateDesc: '' // 实际佣金万分位描述。样例：万6
		// 				},
		// 				{
		// 					applyType: 3,
		// 					applyTypeName: '信用交易',
		// 					afterValue: '万7',
		// 					otherCategoryDesc: '',
		// 					afterValueDesc: '', // 基础佣金调整后万分位描述。样例：万3
		// 					serviceProductFeeRate: '', // 增值服务费率（千分位）
		// 					serviceProductFeeRateDesc: '', // 增值服务服务产品万分位描述。样例：万3
		// 					realFeeRate: '', // 实际佣金（千分位）
		// 					realFeeRateDesc: '' // 实际佣金万分位描述。样例：万6
		// 				},
		// 				{
		// 					applyType: 4,
		// 					applyTypeName: '融资融券、平仓',
		// 					afterValue: '万8',
		// 					otherCategoryDesc: '',
		// 					afterValueDesc: '', // 基础佣金调整后万分位描述。样例：万3
		// 					serviceProductFeeRate: '', // 增值服务费率（千分位）
		// 					serviceProductFeeRateDesc: '', // 增值服务服务产品万分位描述。样例：万3
		// 					realFeeRate: '', // 实际佣金（千分位）
		// 					realFeeRateDesc: '' // 实际佣金万分位描述。样例：万6
		// 				},
		// 				{
		// 					applyType: '6',
		// 					applyTypeName: 'ETF',
		// 					afterValue: 6.16,
		// 					otherCategoryDesc: null,
		// 					afterValueDesc: '交易:6.16元/张',
		// 					serviceProductFeeRate: null,
		// 					serviceProductFeeRateDesc: null,
		// 					realFeeRate: '6.16',
		// 					realFeeRateDesc: '交易:6.16元/张'
		// 				},
		// 				{
		// 					applyType: '7',
		// 					applyTypeName: 'ETF',
		// 					afterValue: 6.16,
		// 					otherCategoryDesc: null,
		// 					afterValueDesc: '行权:6.16元/张',
		// 					serviceProductFeeRate: null,
		// 					serviceProductFeeRateDesc: null,
		// 					realFeeRate: '6.16',
		// 					realFeeRateDesc: '行权:6.16元/张'
		// 				},
		// 				{
		// 					applyType: '8',
		// 					applyTypeName: '股票',
		// 					afterValue: 6.16,
		// 					otherCategoryDesc: null,
		// 					afterValueDesc: '交易:6.16元/张',
		// 					serviceProductFeeRate: null,
		// 					serviceProductFeeRateDesc: null,
		// 					realFeeRate: '6.16',
		// 					realFeeRateDesc: '交易:6.16元/张'
		// 				},
		// 				{
		// 					applyType: '9',
		// 					applyTypeName: '股票',
		// 					afterValue: 6.16,
		// 					otherCategoryDesc: null,
		// 					afterValueDesc: '行权:6.16元/张',
		// 					serviceProductFeeRate: null,
		// 					serviceProductFeeRateDesc: null,
		// 					realFeeRate: '6.16',
		// 					realFeeRateDesc: '行权:6.16元/张'
		// 				},
		// 				{
		// 					applyType: 5,
		// 					applyTypeName: '其它品种',
		// 					afterValue: '',
		// 					otherCategoryDesc: '',
		// 					afterValueDesc: '', // 基础佣金调整后万分位描述。样例：万3
		// 					serviceProductFeeRate: '', // 增值服务费率（千分位）
		// 					serviceProductFeeRateDesc: '', // 增值服务服务产品万分位描述。样例：万3
		// 					realFeeRate: '', // 实际佣金（千分位）
		// 					realFeeRateDesc: '' // 实际佣金万分位描述。样例：万6
		// 				}
		// 			]
		// 		}
		// 	}
		// });
	}

	getHasValidVal(val) {
		return !isNaN(parseFloat(val)) && !isNaN(+val);
	}

	toQueryProof(url) {
		Controller.baseNewPageInit(url);
	}

	render() {
		const { showContent, showStatus, showContract, showHint } = this.state;
		const { title, tip, name } = this.adjustConfig;
		// 确认单审核通过不需要展示确认按钮
		return (
			showContent && (
				<div className="price-adjust">
					{showStatus ? (
						<ApplyResult status={this.status} from={this.codeFrom} />
					) : (
						<div>
							<p className="adjust-title">{title}</p>
							<p className="adjust-tip">{tip}</p>
							<div className="adjust-items">
								{this.adjustTypeConfig.confirmItems.map((itemName, index) => (
									<div className="items-line" key={`line-${index}`}>
										{itemName == 'serviceProductList' ? (
											this.commissionInfo[itemName].length > 0 && (
												<div>
													<div className="items-name">{ConfirmItemMap[itemName].name}：</div>
													<div>
														{this.commissionInfo[itemName].map((item, index) => {
															return (
																<div key={`project-${index}`}>
																	{item.prodName}（{item.signTime}，
																	<span
																		className="project-proof"
																		onClick={() =>
																			this.toQueryProof(item.instructions)
																		}>
																		产品收费说明
																	</span>
																	）
																</div>
															);
														})}
													</div>
												</div>
											)
										) : itemName == 'adjustProject' ? (
											<div className="items-project">
												<div className="items-flex-top-wrapper">
													<div className="items-flex-td">{ConfirmItemMap[itemName].name}</div>
													<div className="items-flex-td">调整后基础佣金率</div>
													<div className="items-flex-td">增值服务产品佣金率</div>
													<div className="items-flex-td">实际佣金率</div>
												</div>
												{this.commissionInfo[itemName].map((adjustProjectItem, index) => {
													let value = '';
													let serviceProductFee = '';
													let realFee = '';
													if (adjustProjectItem.applyType == 5) {
														value = adjustProjectItem.otherCategoryDesc || '-';
														return (
															<div
																className="items-flex-wrapper"
																key={`project-${index}`}>
																<div className="items-flex-td">
																	{adjustProjectItem.applyTypeName}
																</div>
																<div className="items-flex-td" style={{ width: '75%' }}>
																	{value}
																</div>
															</div>
														);
													} else {
														value = this.getHasValidVal(adjustProjectItem.afterValue)
															? `${adjustProjectItem.afterValueDesc}`
															: '未调整';
														serviceProductFee = this.getHasValidVal(
															adjustProjectItem.serviceProductFeeRate
														)
															? `${adjustProjectItem.serviceProductFeeRateDesc}`
															: '-';
														realFee = this.getHasValidVal(adjustProjectItem.realFeeRate)
															? `${adjustProjectItem.realFeeRateDesc}`
															: '-';
														return (
															<div
																className="items-flex-wrapper"
																key={`project-${index}`}>
																<div className="items-flex-td">
																	{adjustProjectItem.applyTypeName}
																</div>
																<div
																	className="items-flex-td"
																	dangerouslySetInnerHTML={{ __html: value }}></div>
																<div
																	className="items-flex-td"
																	dangerouslySetInnerHTML={{
																		__html: serviceProductFee
																	}}></div>
																<div
																	className="items-flex-td"
																	dangerouslySetInnerHTML={{ __html: realFee }}></div>
															</div>
														);
													}
												})}
											</div>
										) : (
											<div>
												<span className="items-name">{ConfirmItemMap[itemName].name}：</span>
												<span>
													{ConfirmItemMap[itemName].pre} {this.commissionInfo[itemName]}{' '}
													{ConfirmItemMap[itemName].unit}
												</span>
											</div>
										)}
									</div>
								))}
								{name == 'commission_rate' && showHint && (
									<div className="bottom-hint">
										注：您已签约增值服务产品，确认单仅展示当前实际佣金率情况。如果后续您签约新的增值产品或已签约增值产品发生调整，佣金率将随之变化，详见产品收费说明。如有疑问，请及时联系服务人员。
									</div>
								)}
								{// 调整融资率且低于5.3需要签署协议
								this.shouldSignContract ? (
									<div className="adjust-contract">
										<input type="checkbox" id="checkboxContract" ref="checkboxContract" />
										<div className="contract-box">
											<label htmlFor="checkboxContract">我已仔细阅读并同意签署</label>
											<p className="contract-name">
												《
												<span
													className="name-name"
													onClick={this.onTapContractName}>{`融资利率补充协议`}</span>
												》
											</p>
										</div>
									</div>
								) : null}
							</div>
							{!this.isAuditSuccess && (
								<Button buttonName="确认" class={'act adjust-apply'} onClick={this.onTapApply} />
							)}
							{showContract && (
								<ContractAlert
									contract={this.financingContract}
									isDelay={this.setContractDelay}
									delaySecond={delaySecond}
									onClose={this.onTapCloseContractAlert}
								/>
							)}
						</div>
					)}
				</div>
			)
		);
	}
}

render(<PriceAdjustConfirmView />, document.getElementById('content'));
