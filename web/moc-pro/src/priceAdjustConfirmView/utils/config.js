// ApplyResult组件使用
export const StatusTypeMap = {
    0: {
        title: '提交成功',
        subTitle: '我们将尽快为您调整',
        icon: 0,
    },
    1: {
        title: '确认单已失效',
        subTitle: '请联系服务人员重新申请',
        icon: 1,
    },
    2: {
        title: '确认单已过期',
        subTitle: '请联系服务人员重新申请',
        icon: 2,
    },
    3: {
        title: '确认单已删除',
        subTitle: '请联系服务人员重新申请',
        icon: 2,
    },
    4: {
        title: '确认单已审核通过',
        subTitle: '',
        icon: 0,
    },
    default: {
        title: '未知状态',
        subTitle: '请联系服务人员重新申请',
        icon: 2,
    }
}

export const ApplyStatusMap = {
    CONFIRM: 1,        // 待客户确认
    EXPIRED: 2,        // 已过期
    REVOKE: 3,         // 已撤回
    APPLY: 4,          // 申请中
    AUDIT: 5,          // 审核中
    AUDIT_SUCCESS: 6,  // 审核通过
    AUDIT_FAIL: 7,     // 审核不通过
    AUDIT_PROCESS: 9,     // 柜台审批中
    AUDIT_PASS: 10,     // 柜台审批通过
    AUDIT_NOTPASS: 11,     // 柜台审批未通过
    DELETE: 0,         // 删除
}

export const ResultSceneFromMap = {
    APPEAR: 'appear',  // 页面进入时
    CONFIRM: 'confirm' // 确认单点击时
}

// 页面进入时使用的确认结果
export const ApplyStatusToResultMap = {
    1: -1, // -1 表示不使用ApplyResult组件
    2: 2,  // 对应StatusTypeMap
    3: 1,
    4: 0,
    5: 0,
    6: 0,
    7: 0,
    9: 0,
    10: 0,
    11: 0,
    0: 3,
    default: 'default',
}

// 确认单点击确认按钮的确认结果
export const ApplyResultToResultMap = {
    0: 0,
    100012: 2,
    100013: 1,
    100014: 0,
    100015: 0,
    100016: 0,
    100017: 0,
    100010: 3,
    default: 'default'
}

export const ConfirmItemMap = {
    clientName: {
        name: '客户姓名'
    },
    fundAccount: {
        name: '资金账号'
    },
    adjustProject: {
        name: '调整项目'
    },
    createTime: {
        name: '受理时间'
    },
    financingRate: {
        name: '融资利率调整为',
        pre: '年化',
        unit: '%'
    },
    serviceProductList: {
        name: '生效中增值产品'
    }
}

export const AdjustTypeMap = {
    commission_rate: {  // 佣金率
        name: 'commission_rate',
        confirmTitle: '【佣金调整申请】确认单',
        confirmTip: '您的佣金调整申请已受理，请核对信息并确认，我们将尽快为您提交申请，申请是否通过请以最终审批结果为准，谢谢！',
        successTitle: '【佣金调整申请】受理结果通知',
        successTip: '您的佣金调整申请已审批通过，具体信息如下，很高兴为您服务，谢谢！',
        confirmItems: ['clientName', 'fundAccount', 'adjustProject', 'createTime', 'serviceProductList'],
    },
    financing_rate: {   // 融资率
        name: 'financing_rate',
        confirmTitle: '【融资利率调整申请】确认单',
        confirmTip: '您的融资利率调整申请已受理，请核对信息并确认，我们将尽快为您提交申请，申请是否通过请以最终审批结果为准，谢谢！',
        successTitle: '【融资利率调整申请】受理结果通知',
        successTip: '您的融资利率申请已审批通过，具体信息如下，很高兴为您服务，谢谢！',
        confirmItems: ['clientName', 'fundAccount', 'financingRate', 'createTime'],
    },
}

export const ApplyTypeMap = {
    1: '融资利率',
    2: '普通账户',
    3: '信用交易',
    4: '融资融券、平仓',
    5: '其它品种',
}