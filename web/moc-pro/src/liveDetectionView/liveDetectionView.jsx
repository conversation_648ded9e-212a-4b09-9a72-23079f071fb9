import React, { Component } from 'react';
import { render } from 'react-dom';
import Events from 'Events';
import Storage from 'Storage';
import Tool from 'Tool';
import BaseComponent from '../../common/vendors/BaseComponent.js'
import Button from '../components/button/Button.jsx';

import LiveDetectionComponent from './component/liveDetectionComponent';
import UploadVideoComponent from './component/uploadVideoComponent'
import WebRTCComponent from './component/webRTCComponent';
import downloadTipIcon from './component/images/confirm.png';

const PAGE_STATUS = {
    INIT: "0",
    NATIVE_LIVE_DETECTION: "1", //native sdk 活体
    UPLOAD_VIDEO: "2", //h5/native skd上传单向视频
    WEBRTC: "3", //WebRTC 活体+单向视频
    WEBRTC_VIDEO: "4", //WebRTC 双向视频APP下载
};


class LiveDetectionView extends BaseComponent {
    constructor(props) {
        super(props);

        this.initPage = this.initPage.bind(this);
        this.goVideoWitness = this.goVideoWitness.bind(this);
        this.finishLiveDetection = this.finishLiveDetection.bind(this);
        this.finishSingleVideo = this.finishSingleVideo.bind(this);
        this.goDownloadPage = this.goDownloadPage.bind(this);
        //window.test=this.finishSingleVideo;
        this.state = {
            name: '',
            tel: '',
            sdkConfig: {
                isReadModes: false
            },
            pageStatus: PAGE_STATUS.INIT,
            supportLiveDetection: true,
        };

    }

    componentWillMount() { }

    componentDidMount() {
        this.appInit((data) => {
            this.initPage(data);
        })

        var me = this;
        //window.GoBackOnLoad=function(){
        //    me.appInit((data)=>{
        //        me.initPage(data);
        //    })
        //}
        Controller.onAppear(function () {
            me.appInit((data) => {
                // me.initPage(data);
            })
        });

        Tool.weblog({
            logId: '************',
            action: 'liveDetection',
            url: window.location.href
        });
    }
    queryInfo(){
        //查询用户相关信息
        let param = {
            UniqueKey:'CWAUQ',
            client_id:'($usercode)',
            fund_account:'($fund_account)',
            op_station:'',
            password:'($password)',
        };
        Tool.waiting.show();
        Tool.fetchTo(param).then((oData)=>{
            console.log(oData);
            Tool.waiting.hide();
            if(+oData.code == 0){
                let name = oData.result.client_name;
                let tel = oData.result.mobile_tel;
                this.setState({
                    name,
                    tel
                })
            }
        }).catch((error)=>{
            Tool.waiting.hide();
        });
    }
    /**
	 * 查询自述式和问答式sdk的配置参数，对结果进行格式转化，并生成财人汇sdk对应的配置参数
	 * @returns config
	 */
	queryConfig(){
		let param = {
			UniqueKey: 'PARAMETER_LIST', //功能号
			param_key: 'nlpEnableYJBAPP,asrEnableYJBAPP,ttsEnableYJBAPP',
			business_type: '' //此处空值必传
        };
        let sdkConfig = this.state.sdkConfig;
		Tool.fetchTo(param).then((res) => {
            console.log('res==', JSON.stringify(res));
            
            if(res.code == 0){
                res.result.map(configObjItem => {
                    for (let configName in configObjItem) {
                        sdkConfig[configName] = configObjItem[configName].paramValue;
                    }
                });
                //"开户单向视频TTS个性化语音播报开关 1-开 0-关",
                //"开户单向视频ASR语音识别开关 1-开 0-关"
                //"开户单向视频语义匹配开关 1-开 0-关",

                // 转化为功能号所需的字段，财人汇配置值
                sdkConfig.isReadModes = sdkConfig.ttsEnableYJBAPP == '1'; //是否是问答模式
                sdkConfig.isSkipCheckAsrResults = sdkConfig.nlpEnableYJBAPP == '1' ? '0' : '1'; //是否跳过语义校验
                sdkConfig.isNeedAsrs = sdkConfig.asrEnableYJBAPP; //是否使用语音识别
                this.setState({
                    sdkConfig
                })
            }
        });
	}
    componentDidUpdate() {
        var me = this;
        window.GoBackOnLoad = function () {
            me.appInit((data) => {
                me.initPage(data);
            })
        }
    }
    async initPage(data) {
        console.log('init page', data);
        this.queryInfo();
        this.queryConfig();
        this.setState({
            initMessage: data.initMessage,
        });
        // this.setState({
        //     pageStatus: PAGE_STATUS.UPLOAD_VIDEO,
        //     supportLiveDetection:false,
        // })

        // return;
        //todo  待办联调
        if (data && data.groupData) {
            // test code
            // let todoInfo = {
            //     pageTitle: '补开股东户',
            //     todoId: '333',
            //     businessType: 30002,
            //     busiParam: {
            //         serial_no: '33333'
            //     }
            // }
            let todoInfo = data.groupData.param;

            Controller.setTitle({
                title: todoInfo.pageTitle,
            });

            this.setState({
                todoId: todoInfo.todoId,
                todoInfo: JSON.parse(JSON.stringify(todoInfo)),
                serial_no: todoInfo.busiParam.serial_no,
                businessType: todoInfo.businessType,
                busiParam: todoInfo.busiParam,
            })

            localStorage.setItem('authenticationWay', 'liveDetect');
        }

        if (Controller.getAppId() == 'yjb3.0') {
            //佣金宝APP，使用客户端sdk活体
            //版本号判断在创建待办时处理
            this.setState({
                pageStatus: PAGE_STATUS.NATIVE_LIVE_DETECTION,
                supportLiveDetection:true,
            })
        } else {
            this.setState({
                pageStatus: PAGE_STATUS.WEBRTC
            })
        }



    }

    /**
     * 往后推一步待办，跳转至视频见证节点
     *
     * @memberof LiveDetectionView
     */
    goVideoWitness() {
        Tool.weblog({
            logId: '202212231750',
            action: 'goVideoWitness',
            url: window.location.href
        });
        
        console.log('跳转至视频见证页面')
        let todoInfo=this.state.todoInfo;
        let initMessage = this.state.initMessage;

        Tool.supportVideoWitness().then((supportVideo)=> {
            let app_id = Controller.getAppId();
            Tool.weblog({
                logId: '202212051700',
                action: 'videowitness',
                appId: app_id,
                videowitness: supportVideo,
                url: window.location.href
            });
            if (supportVideo) {
                let param = {
                    UniqueKey: "ACSO_03",
                    todoId:todoInfo.todoId,
                    op_type: 'convert',
                    option_type: 'convert',
                    op_station: initMessage.opStation,
                };
                return new Promise((resolve, reject) => {
                    Tool.waiting.show();
                    Tool.fetchTo(param)
                        .then(function (data) {
                            Tool.waiting.hide();
                            Reserve_Tool.nextStep(data.result);
                        })
                        .catch((e) => {
                            //接口请求异常;
                            Tool.waiting.hide();
                            // reject && reject(e);
                        });
                });
            }
        })
    }

    /**
     * 完成native sdk活体检测，进入单向视频上传逻辑
     *
     * @memberof LiveDetectionView
     */
    finishLiveDetection() {
        this.setState({
            pageStatus: PAGE_STATUS.UPLOAD_VIDEO
        })
    }


    /**
     * 完成单向视频，提交单向视频档案路径，推动待办
     *
     * @param {*} storage_type
     * @param {*} storage_address
     * @memberof LiveDetectionView
     */
    finishSingleVideo(storage_type, storage_address) {
        //todo
        let todoInfo=this.state.todoInfo;
        let initMessage = this.state.initMessage;
        let param = {
            UniqueKey: "TMSS_01", 
            archive_type:'7',//单向视频
            storage_type:storage_type,
            client_id:'($usercode)',
            todoId:todoInfo.todoId,
            serial_no: todoInfo.busiParam.serial_no,
            business_type: todoInfo.businessType,
            busi_param: encodeURIComponent(JSON.stringify(todoInfo.busiParam)),
            op_station: initMessage.opStation,
        };

        if(storage_type=="0"){
            param.file_id=storage_address;
        }else{
            param.storage_address=storage_address;
        }


        return new Promise((resolve, reject) => {
            Tool.waiting.show();
            Tool.fetchTo(param)
                .then(function (data) {
                    Tool.waiting.hide();
                    Reserve_Tool.nextStep(data.result);
                })
                .catch((e) => {
                    //接口请求异常;
                    Tool.waiting.hide();
                    //setPageStatus(PAGE_STATUS.NORMAL); //复原界面
                    // reject && reject(e);
                });
        });

    }

    goDownloadPage () {
        let todoInfo = this.state.todoInfo;
        if (Controller.getAppId() == 'yjb3.0') {
            Events.click('yjbApp', null, function (data) {
                console.log("执行完毕");
            }, {});
        } else {
            Tool.goAppDownloadPage(todoInfo.businessType);
        }
    }


    render() {
        let { pageStatus ,supportLiveDetection,initMessage,todoInfo} = this.state;
        let { name , tel} = this.state;
        switch (pageStatus) {
            case PAGE_STATUS.INIT:
                return <div></div>;

                break;
            case PAGE_STATUS.NATIVE_LIVE_DETECTION:
                return (
                    <div>
                        <LiveDetectionComponent
                            finishLiveDetection={this.finishLiveDetection}
                            goVideoWitness={this.goVideoWitness}
                            todoInfo={todoInfo}
                        />
                    </div>
                );

                break;

            case PAGE_STATUS.UPLOAD_VIDEO:
                return (
                    <div>
                        <UploadVideoComponent
                            name = {name}
                            tel = {tel}
                            supportLiveDetection={this.state.supportLiveDetection}
                            sdkConfig={this.state.sdkConfig}
                            goVideoWitness={this.goVideoWitness}
                            finishSingleVideo={this.finishSingleVideo}
                        />
                    </div>
                );

                break;
            case PAGE_STATUS.WEBRTC:
                return (
                    <div>
                        <WebRTCComponent 
                            finishSingleVideo={this.finishSingleVideo}
                            goVideoWitness={this.goVideoWitness}
                            name = {name}
                            initMessage={initMessage}
                            todoInfo={todoInfo}
                        />
                    </div>
                );
                break;
            case PAGE_STATUS.WEBRTC_VIDEO:
                return (
                    <div>
                        <div className='download-tip'>
                            <p>
                                <img src={downloadTipIcon} style={{width: '100%'}}/>
                            </p>
                            <div>
                                <p>尊敬的客户您好！</p>
                                <p>接下来您需要与客服进行双向视频完成{todoInfo.businessType == 30002 ? '补开股东户' : todoInfo.pageTitle}，请您下载国金佣金宝APP继续办理业务。</p>
                            </div>
                        </div>
                        <Button buttonName={'下载-国金佣金宝APP'} class="act" onClick={this.goDownloadPage} data-tap-disabled="true" />
                    </div>
                );
                break;
            default:
                return <div></div>;
        }

    }
}

render(<LiveDetectionView />, document.querySelector("#content"));