
const HtmlWebpackPlugin = require('html-webpack-plugin');
const path = require('path');
const packageJson = require('./package.json');
const controllerVersion = `${packageJson.version}.${new Date().getTime()}`;


let pages = [
    {
        name: "indexView",
        htmlFileName: "indexView",
        src: "./src/indexView/IndexView.jsx",
        title: "业务办理",
        template:"./templates/common-index.html"
    },
    {
        name: "sysNoticeView",
        htmlFileName: "sysNoticeView",
        src: "./src/sysNoticeView/SysNoticeView.jsx",
        title: "系统公告",
        template:"./templates/common.html"
    },
    {
        name: "noticeView",
        htmlFileName: "noticeView",
        src: "./src/noticeView/NoticeView.jsx",
        title: "公告",
        template:"./templates/common.html"
    },
    // 理财反洗钱
    {
        name: "conditionCheck",
        htmlFileName: "conditionCheck",
        src: "./src/conditionCheckView/ConditionCheckView.jsx",
        title: "",
        template:"./templates/common.html"
    },
    {
        name: "conditionCheckView",
        htmlFileName: "conditionCheckView",
        src: "./src/conditionCheckView/ConditionCheckView.jsx",
        title: "",
        template:"./templates/common.html"
    },
    {
        name: "additionalConditionCheck",
        htmlFileName: "additionalConditionCheck",
        src: "./src/conditionCheckView/AdditionalConditionCheckView.jsx",
        title: "",
        template:"./templates/common.html"
    },
    {
        name: "suitablyView",
        htmlFileName: "suitablyView",
        src: "./src/suitablyView/SuitablyView.jsx",
        title: "",
        template:"./templates/common.html"
    },
    {
        name: "signPactView",
        htmlFileName: "signPactView",
        src: "./src/signPactView/SignPactView.jsx",
        title: "",
        template:"./templates/common.html"
    },
    {
        name: "quesListView",
        htmlFileName: "quesListView",
        src: "./src/quesListView/QuesListView.jsx",
        title: "",
        template:"./templates/common.html"
    },
    {
        name: "ggtQueryView",
        htmlFileName: "ggtQueryView",
        src: "./src/ggtQueryView/GgtQueryView.jsx",
        title: "",
        template:"./templates/common.html"
    },
    {
        name: "ggtRetView",
        htmlFileName: "ggtRetView",
        src: "./src/ggtRetView/GgtRetView.jsx",
        title: "",
        template:"./templates/common.html"
    },
    {
        name: "startUpBoardView",
        htmlFileName: "startUpBoardView",
        src: "./src/startUpBoardView/StartUpBoardView.jsx",
        title: "",
        template:"./templates/common.html"
    },
    {
        name: "startUpBoardV2View",
        htmlFileName: "startUpBoardV2View",
        src: "./src/startUpBoardView/StartUpBoardV2View.jsx",
        title: "",
        template:"./templates/common.html"
    },
    {
        name: "creditStartUpBoardView",
        htmlFileName: "creditStartUpBoardView",
        src: "./src/startUpBoardView/CreditStartUpBoardView.jsx",
        title: "",
        template:"./templates/common.html"
    },
    {
        name: "creditStartUpBqConfirmView",
        htmlFileName: "creditStartUpBqConfirmView",
        src: "./src/startUpBoardView/CreditStartUpBqConfirmView.jsx",
        title: "",
        template:"./templates/common.html"
    },
    {
        name: "startUpBoardRetView",
        htmlFileName: "startUpBoardRetView",
        src: "./src/startUpBoardRetView/StartUpBoardRetView.jsx",
        title: "",
        template:"./templates/common.html"
    },
    {
        name: "startUpBoardV2RetView",
        htmlFileName: "startUpBoardV2RetView",
        src: "./src/startUpBoardRetView/StartUpBoardV2RetView.jsx",
        title: "",
        template:"./templates/common.html"
    },
    {
        name: "startUpBoardZqRetView",
        htmlFileName: "startUpBoardZqRetView",
        src: "./src/startUpBoardRetView/StartUpBoardZqRetView.jsx",
        title: "",
        template:"./templates/common.html"
    },
    {
        name: "startUpBoardBqRetView",
        htmlFileName: "startUpBoardBqRetView",
        src: "./src/startUpBoardRetView/StartUpBoardBqRetView.jsx",
        title: "",
        template:"./templates/common.html"
    },
    {
        name: "creditStartUpBoardOpenRetView",
        htmlFileName: "creditStartUpBoardOpenRetView",
        src: "./src/startUpBoardRetView/CreditStartUpBoardOpenRetView.jsx",
        title: "",
        template:"./templates/common.html"
    },
    //创业板转签部分-end

    {
        name: "deListQueryView",
        htmlFileName: "deListQueryView",
        src: "./src/deListQueryView/DeListQueryView.jsx",
        title: "",
        template:"./templates/common.html"
    },
    {
        name: "deListRetView",
        htmlFileName: "deListRetView",
        src: "./src/deListRetView/deListRetView.jsx",
        title: "",
        template:"./templates/common.html"
    },
    {
        name: "stockAView",
        htmlFileName: "stockAView",
        src: "./src/stockAView/StockAView.jsx",
        title: "",
        template:"./templates/common.html"
    },
    {
        name: "stockAOpenView",
        htmlFileName: "stockAOpenView",
        src: "./src/stockAOpenView/StockAOpenView.jsx",
        title: "",
        template:"./templates/common.html"
    },
    {
        name: "stockARetView",
        htmlFileName: "stockARetView",
        src: "./src/stockARetView/StockARetView.jsx",
        title: "",
        template:"./templates/common.html"
    },
    {
        name: "stockAApplyRetView",
        htmlFileName: "stockAApplyRetView",
        src: "./src/stockARetView/stockAApplyRetView.jsx",
        title: "",
        template:"./templates/common.html"
    },
    {
        name: "openCreditView",
        htmlFileName: "openCreditView",
        src: "./src/openCreditView/OpenCreditView.jsx",
        title: "",
        template:"./templates/common.html"
    },
    {
        name: "specifiedView",
        htmlFileName: "specifiedView",
        src: "./src/specifiedView/SpecifiedView.jsx",
        title: "",
        template:"./templates/common.html"
    },
    {
        name: "specifiedResultView",
        htmlFileName: "specifiedResultView",
        src: "./src/resultView/SpecifiedResultView.jsx",
        title: "",
        template:"./templates/common.html"
    },
    {
        name: "riskwarnView",
        htmlFileName: "riskwarnView",
        src: "./src/riskwarnView/RiskwarnView.jsx",
        title: "",
        template:"./templates/common.html"
    },
    {
        name: "riskwarnResultView",
        htmlFileName: "riskwarnResultView",
        src: "./src/resultView/RiskwarnResultView.jsx",
        title: "",
        template:"./templates/common.html"
    },
    {
        name: "creditAView",
        htmlFileName: "creditAView",
        src: "./src/creditAView/CreditAView.jsx",
        title: "",
        template:"./templates/common.html"
    },
    {
        name: "creditAResultView",
        htmlFileName: "creditAResultView",
        src: "./src/resultView/CreditAResultView.jsx",
        title: "",
        template:"./templates/common.html"
    },
    {
        name: "creditLimitView",
        htmlFileName: "creditLimitView",
        src: "./src/creditLimitView/CreditLimitView.jsx",
        title: "",
        template:"./templates/common.html"
    },
    {
        name: "creditLimitAdjustView",
        htmlFileName: "creditLimitAdjustView",
        src: "./src/creditLimitAdjustView/CreditLimitAdjustView.jsx",
        title: "",
        template:"./templates/common.html"
    },
    {
        name: "creditLimitRetView",
        htmlFileName: "creditLimitRetView",
        src: "./src/creditLimitRetView/CreditLimitRetView.jsx",
        title: "",
        template:"./templates/common.html"
    },
    {
        name: "wjdcListView",
        htmlFileName: "wjdcListView",
        src: "./src/wjdcListView/WjdcListView.jsx",
        title: "",
        template:"./templates/common.html"
    },
    {
        name: "sztradepwdListView",
        htmlFileName: "sztradepwdListView",
        src: "./src/sztradepwdListView/SztradepwdListView.jsx",
        title: "",
        template:"./templates/common.html"
    },
    {
        name: "szpwdApplyView",
        htmlFileName: "szpwdApplyView",
        src: "./src/szpwdApplyView/SzpwdApplyView.jsx",
        title: "",
        template:"./templates/common.html"
    },
    {
        name: "szpwdActiveView",
        htmlFileName: "szpwdActiveView",
        src: "./src/szpwdActiveView/SzpwdActiveView.jsx",
        title: "",
        template:"./templates/common.html"
    },
    {
        name: "szpwdReportView",
        htmlFileName: "szpwdReportView",
        src: "./src/szpwdReportView/SzpwdReportView.jsx",
        title: "",
        template:"./templates/common.html"
    },
    {
        name: "thirdpartView",
        htmlFileName: "thirdpartView",
        src: "./src/thirdpartView/ThirdpartView.jsx",
        title: "",
        template:"./templates/common.html"
    },
    {
        name: "thirdCheckView",
        htmlFileName: "thirdCheckView",
        src: "./src/thirdCheckView/ThirdCheckView.jsx",
        title: "",
        template:"./templates/common.html"
    },
    {
        name: "thirdOperView",
        htmlFileName: "thirdOperView",
        src: "./src/thirdOperView/ThirdOperView.jsx",
        title: "",
        template:"./templates/common.html"
    },
    {
        name: "thirdBindView",
        htmlFileName: "thirdBindView",
        src: "./src/thirdBindView/ThirdBindView.jsx",
        title: "",
        template:"./templates/common.html"
    },
    {
        name: "thirdRetView",
        htmlFileName: "thirdRetView",
        src: "./src/thirdRetView/ThirdRetView.jsx",
        title: "",
        template:"./templates/common.html"
    },

    {
        name: "chooseBankView",
        htmlFileName: "chooseBankView",
        src: "./src/chooseBankView/ChooseBankView.jsx",
        title: "",
        template:"./templates/common.html"
    },
    {
        name: "newCardRetView",
        htmlFileName: "newCardRetView",
        src: "./src/newCardRetView/NewCardRetView.jsx",
        title: "",
        template:"./templates/common.html"
    },
    {
        name: "finishCardView",
        htmlFileName: "finishCardView",
        src: "./src/finishCardView/FinishCardView.jsx",
        title: "",
        template:"./templates/common.html"
    },
    {
        name: "bindCardRetView",
        htmlFileName: "bindCardRetView",
        src: "./src/bindCardRetView/BindCardRetView.jsx",
        title: "",
        template:"./templates/common.html"
    },

    {
        name: "openfundView",
        htmlFileName: "openfundView",
        src: "./src/openfundView/OpenfundView.jsx",
        title: "",
        template:"./templates/common.html"
    },
    {
        name: "comingSoonView",
        htmlFileName: "comingSoonView",
        src: "./src/comingSoonView/ComingSoonView.jsx",
        title: "",
        template:"./templates/common.html"
    },
    {
        name: "simpleTipView",
        htmlFileName: "simpleTipView",
        src: "./src/simpleTipView/SimpleTipView.jsx",
        title: "",
        template:"./templates/common.html"
    },
    {
        name: "simpleHintView",
        htmlFileName: "simpleHintView",
        src: "./src/simpleHintView/SimpleHintView.jsx",
        title: "",
        template:"./templates/common.html"
    },
    {
        name: "goGroupView",
        htmlFileName: "goGroupView",
        src: "./src/goGroupView/GoGroupView.jsx",
        title: "",
        template:"./templates/common.html"
    },
    // 要交易登录那边修改这个链接。弃用
    {
        name: "clearpwdConditionCheckView",
        htmlFileName: "clearpwdConditionCheckView",
        src: "./src/clearpwd/ClearpwdConditionCheckView.jsx",
        title: "重置密码",
        template:"./templates/common.clearpwd.group.html"
    },
    {
        name: "verifyFundAccountView",
        htmlFileName: "verifyFundAccountView",
        src: "./src/clearpwd/VerifyFundAccountView.jsx",
        title: "",
        template:"./templates/common.html"
    },
    {
        name: "verifyPhoneStatusView",
        htmlFileName: "verifyPhoneStatusView",
        src: "./src/clearpwd/VerifyPhoneStatusView.jsx",
        title: "",
        template:"./templates/common.html"
    },
    {
        name: "verifyPhoneNumView",
        htmlFileName: "verifyPhoneNumView",
        src: "./src/clearpwd/VerifyPhoneNumView.jsx",
        title: "",
        template:"./templates/common.html"
    },
    {
        name: "uploadIdView",
        htmlFileName: "uploadIdView",
        src: "./src/clearpwd/UploadIdView.jsx",
        title: "",
        template:"./templates/common.html"
    },
    {
        name: "uploadSupplementView",
        htmlFileName: "uploadSupplementView",
        src: "./src/clearpwd/UploadSupplementView.jsx",
        title: "",
        template:"./templates/common.html"
    },
    {
        name: "personalInfoView",
        htmlFileName: "personalInfoView",
        src: "./src/clearpwd/PersonalInfoView.jsx",
        title: "",
        template:"./templates/common.html"
    },
    {
        name: "uploadVideoView",
        htmlFileName: "uploadVideoView",
        src: "./src/clearpwd/UploadVideoView.jsx",
        title: "",
        template:"./templates/common.html"
    },
    {
        name: "resetPasswordView",
        htmlFileName: "resetPasswordView",
        src: "./src/clearpwd/ResetPasswordView.jsx",
        title: "",
        template:"./templates/common.html"
    },
    {
        name: "clearPasswordResultView",
        htmlFileName: "ClearPasswordResultView",
        src: "./src/clearpwd/ClearPasswordResultView.jsx",
        title: "",
        template:"./templates/common.html"
    },

    {
        name: "cdrQueryView",
        htmlFileName: "cdrQueryView",
        src: "./src/innovationView/cdrQueryView.jsx",
        title: "",
        template:"./templates/common.html"
    },
    {
        name: "cdrRetView",
        htmlFileName: "cdrRetView",
        src: "./src/innovationView/cdrRetView.jsx",
        title: "",
        template:"./templates/common.html"
    },
    {
        name: "creditCdrQueryView",
        htmlFileName: "creditCdrQueryView",
        src: "./src/innovationView/creditCdrQueryView.jsx",
        title: "",
        template:"./templates/common.html"
    },
    {
        name: "creditCdrRetView",
        htmlFileName: "creditCdrRetView",
        src: "./src/innovationView/creditCdrRetView.jsx",
        title: "",
        template:"./templates/common.html"
    },
    {
        name: "innovationStockQueryView",
        htmlFileName: "innovationStockQueryView",
        src: "./src/innovationView/innovationStockQueryView.jsx",
        title: "",
        template:"./templates/common.html"
    },
    {
        name: "innovationStockRetView",
        htmlFileName: "innovationStockRetView",
        src: "./src/innovationView/innovationStockRetView.jsx",
        title: "",
        template:"./templates/common.html"
    },
    {
        name: "creditInnovationStockQueryView",
        htmlFileName: "creditInnovationStockQueryView",
        src: "./src/innovationView/creditInnovationStockQueryView.jsx",
        title: "",
        template:"./templates/common.html"
    },
    {
        name: "creditInnovationStockRetView",
        htmlFileName: "creditInnovationStockRetView",
        src: "./src/innovationView/creditInnovationStockRetView.jsx",
        title: "",
        template:"./templates/common.html"
    },
     //CDR与创新企业 end
    {
        name: "reverseReposQueryView",
        htmlFileName: "reverseReposQueryView",
        src: "./src/innovationView/reverseReposQueryView.jsx",
        title: "",
        template:"./templates/common.html"
    },
    {
        name: "reverseReposRetView",
        htmlFileName: "reverseReposRetView",
        src: "./src/innovationView/reverseReposRetView.jsx",
        title: "",
        template:"./templates/common.html"
    },
    {
        name: "londonCdrQueryView",
        htmlFileName: "londonCdrQueryView",
        src: "./src/innovationView/londonCdrQueryView.jsx",
        title: "",
        template:"./templates/common.html"
    },
    {
        name: "londonCdrRetView",
        htmlFileName: "londonCdrRetView",
        src: "./src/innovationView/londonCdrRetView.jsx",
        title: "",
        template:"./templates/common.html"
    },
    {
        name: "reservationListView",
        htmlFileName: "reservationListView",
        src: "./src/reservation/ReservationListView.jsx",
        title: "",
        template:"./templates/common.html"
    },
    {
        name: "phoneVerifyView",
        htmlFileName: "phoneVerifyView",
        src: "./src/phoneVerifyView/PhoneVerifyView.jsx",
        title: "",
        template:"./templates/common.html"
    },
    {
        name: "phoneVerifyInvestorView",
        htmlFileName: "phoneVerifyInvestorView",
        src: "./src/phoneVerifyView/PhoneVerifyInvestorView.jsx",
        title: "",
        template:"./templates/common.html"
    },
    {
        name: "accountCancellationApplyRetView",
        htmlFileName: "accountCancellationApplyRetView",
        src: "./src/accountCancellation/AccountCancellationApplyRetView.jsx",
        title: "",
        template:"./templates/common.html"
    },
    {
        name: "accountCancellationQueryView",
        htmlFileName: "accountCancellationQueryView",
        src: "./src/accountCancellation/AccountCancellationQueryView.jsx",
        title: "",
        template:"./templates/common.html"
    },
    {
        name: "accountCancellationQuestionnaireView",
        htmlFileName: "accountCancellationQuestionnaireView",
        src: "./src/accountCancellation/AccountCancellationQuestionnaireView.jsx",
        title: "",
        template:"./templates/common.html"
    },
    {
        name: "accountCancellationUploadIdView",
        htmlFileName: "accountCancellationUploadIdView",
        src: "./src/accountCancellation/AccountCancellationUploadIdView.jsx",
        title: "",
        template:"./templates/common.html"
    },
    {
        name: "accountCancellationUploadSupplementView",
        htmlFileName: "accountCancellationUploadSupplementView",
        src: "./src/accountCancellation/AccountCancellationUploadSupplementView.jsx",
        title: "",
        template:"./templates/common.html"
    },
    {
        name: "accountCancellationVideoView",
        htmlFileName: "accountCancellationVideoView",
        src: "./src/accountCancellation/AccountCancellationVideoView.jsx",
        title: "",
        template:"./templates/common.html"
    },
    {
        name: "accountCancellationInfoView",
        htmlFileName: "accountCancellationInfoView",
        src: "./src/accountCancellation/AccountCancellationInfoView.jsx",
        title: "",
        template:"./templates/common.html"
    },
    {
        name: "stibQueryView",
        htmlFileName: "stibQueryView",
        src: "./src/innovationView/stibQueryView.jsx",
        title: "",
        template:"./templates/common.html"
    },
    {
        name: "stibRetView",
        htmlFileName: "stibRetView",
        src: "./src/innovationView/stibRetView.jsx",
        title: "",
        template:"./templates/common.html"
    },
    {
        name: "creditStibQueryView",
        htmlFileName: "creditStibQueryView",
        src: "./src/innovationView/creditStibQueryView.jsx",
        title: "",
        template:"./templates/common.html"
    },
    {
        name: "creditStibRetView",
        htmlFileName: "creditStibRetView",
        src: "./src/innovationView/creditStibRetView.jsx",
        title: "",
        template:"./templates/common.html"
    },
    {
        name: "stockBeiQueryView",
        htmlFileName: "stockBeiQueryView",
        src: "./src/stockBeiView/stockBeiQueryView.jsx",
        title: "",
        template:"./templates/common.html"
    },
    {
        name: "stockBeiAdditionConditionView",
        htmlFileName: "stockBeiAdditionConditionView",
        src: "./src/stockBeiView/stockBeiAdditionConditionView.jsx",
        title: "",
        template:"./templates/common.html"
    },
    {
        name: "stockBeiRetView",
        htmlFileName: "stockBeiRetView",
        src: "./src/stockBeiView/stockBeiRetView.jsx",
        title: "",
        template:"./templates/common.html"
    },
    {
        name: "stockBeiCloseRetView",
        htmlFileName: "stockBeiCloseRetView",
        src: "./src/stockBeiView/StockBeiCloseRetView.jsx",
        title: "",
        template:"./templates/common.html"
    },
    {
        name: "creditStockBeiQueryView",
        htmlFileName: "creditStockBeiQueryView",
        src: "./src/creditStockBeiView/creditStockBeiQueryView.jsx",
        title: "",
        template:"./templates/common.html"
    },
    {
        name: "creditStockBeiSpecialView",
        htmlFileName: "creditStockBeiSpecialView",
        src: "./src/creditStockBeiView/creditStockBeiSpecialView.jsx",
        title: "",
        template:"./templates/common.html"
    },
    {
        name: "creditStockBeiInfoCollectView",
        htmlFileName: "creditStockBeiInfoCollectView",
        src: "./src/creditStockBeiView/creditStockBeiInfoCollectView.jsx",
        title: "",
        template:"./templates/common.html"
    },
    {
        name: "creditStockBeiOpenRetView",
        htmlFileName: "creditStockBeiOpenRetView",
        src: "./src/creditStockBeiView/creditStockBeiOpenRetView.jsx",
        title: "",
        template:"./templates/common.html"
    },
    {
        name: "creditStockBeiAudioView",
        htmlFileName: "creditStockBeiAudioView",
        src: "./src/creditStockBeiView/creditStockBeiAudioView.jsx",
        title: "",
        template:"./templates/common.html"
    },
    {
        name: "debentureQueryView",
        htmlFileName: "debentureQueryView",
        src: "./src/debentureView/debentureQueryView.jsx",
        title: "",
        template:"./templates/common.html"
    },
    {
        name: "debentureRetView",
        htmlFileName: "debentureRetView",
        src: "./src/debentureView/debentureRetView.jsx",
        title: "",
        template:"./templates/common.html"
    },
    {
        name: "debentureCloseRetView",
        htmlFileName: "debentureCloseRetView",
        src: "./src/debentureView/debentureCloseRetView.jsx",
        title: "",
        template:"./templates/common.html"
    },
    {
        name: "creditDebentureQueryView",
        htmlFileName: "creditDebentureQueryView",
        src: "./src/debentureView/creditDebentureQueryView.jsx",
        title: "",
        template:"./templates/common.html"
    },
    {
        name: "inputCreditInfoView",
        htmlFileName: "inputCreditInfoView",
        src: "./src/debentureView/inputCreditInfoView.jsx",
        title: "",
        template:"./templates/common.html"
    },
    {
        name: "creditDebentureRetView",
        htmlFileName: "creditDebentureRetView",
        src: "./src/debentureView/creditDebentureRetView.jsx",
        title: "",
        template:"./templates/common.html"
    },
    {
        name: "creditDebentureCloseRetView",
        htmlFileName: "creditDebentureCloseRetView",
        src: "./src/debentureView/creditDebentureCloseRetView.jsx",
        title: "",
        template:"./templates/common.html"
    },
    {
        name: "openCreditHintView",
        htmlFileName: "openCreditHintView",
        src: "./src/innovationView/openCreditHintView.jsx",
        title: "",
        template:"./templates/common.html"
    },
    {
        name: "investorListView",
        htmlFileName: "investorListView",
        src: "./src/investorView/investorListView.jsx",
        title: "",
        template:"./templates/common.html"
    },
    {
        name: "investorIntroView",
        htmlFileName: "investorIntroView",
        src: "./src/investorView/investorIntroView.jsx",
        title: "",
        template:"./templates/common.html"
    },
    {
        name: "investorRetView",
        htmlFileName: "investorRetView",
        src: "./src/investorView/investorRetView.jsx",
        title: "",
        template:"./templates/common.html"
    },
    {
        name: "investorUploadPicView",
        htmlFileName: "investorUploadPicView",
        src: "./src/investorView/investorUploadPicView.jsx",
        title: "",
        template:"./templates/common.html"
    },
    {
        name: "investorContractView",
        htmlFileName: "investorContractView",
        src: "./src/investorView/investorContractView.jsx",
        title: "",
        template:"./templates/common.html"
    },
    {
        name: "doubleRecordListView",
        htmlFileName: "doubleRecordListView",
        src: "./src/doubleRecordView/doubleRecordListView.jsx",
        title: "",
        template:"./templates/common.html"
    },
    {
        name: "doubleRecordHistoryView",
        htmlFileName: "doubleRecordHistoryView",
        src: "./src/doubleRecordView/doubleRecordHistoryView.jsx",
        title: "",
        template:"./templates/common.html"
    },
    {
        name: "doubleRecordRetView",
        htmlFileName: "doubleRecordRetView",
        src: "./src/doubleRecordView/doubleRecordRetView.jsx",
        title: "",
        template:"./templates/common.html"
    },
    {
        name: "userInfoConfirmView",
        htmlFileName: "userInfoConfirmView",
        src: "./src/userInfoConfirmView/userInfoConfirmView.jsx",
        title: "",
        template:"./templates/common.html"
    },
    {
        name: "fundSignUpView",
        htmlFileName: "fundSignUpView",
        src: "./src/fundSignUpView/FundSignUp.jsx",
        title: "",
        template:"./templates/common.html"
    },
    {
        name: "closedEndFundView",
        htmlFileName: "closedEndFundView",
        src: "./src/closedEndFund/ClosedEndFundView.jsx",
        title: "",
        template:"./templates/common.html"
    },
    {
        name: "closedEndFundOpenView",
        htmlFileName: "closedEndFundOpenView",
        src: "./src/closedEndFund/ClosedEndFundOpenView.jsx",
        title: "",
        template:"./templates/common.html"
    },
    {
        name: "closedEndFundRetView",
        htmlFileName: "closedEndFundRetView",
        src: "./src/closedEndFund/ClosedEndFundRetView.jsx",
        title: "",
        template:"./templates/common.html"
    },
    //预约业务
    {
        name: "reserveUploadIdCardView",
        htmlFileName: "reserveUploadIdCardView",
        src: "./src/reserveView/ReserveUploadIdCardView.jsx",
        title: "",
        template:"./templates/common.html"
    },
    {
        name: "reserveUploadSupplementCardView",
        htmlFileName: "reserveUploadSupplementCardView",
        src: "./src/reserveView/ReserveUploadSupplementCardView.jsx",
        title: "",
        template:"./templates/common.html"
    },
    {
        name: "reserveUploadHeadimgView",
        htmlFileName: "reserveUploadHeadimgView",
        src: "./src/reserveView/ReserveUploadHeadimgView.jsx",
        title: "",
        template:"./templates/common.html"
    },
    {
        name: "reserveRetView",
        htmlFileName: "reserveRetView",
        src: "./src/reserveView/ReserveRetView.jsx",
        title: "",
        template:"./templates/common.html"
    },
    {
        name: "reserveListView",
        htmlFileName: "reserveListView",
        src: "./src/reserveView/ReserveListView.jsx",
        title: "",
        template:"./templates/common.html"
    },
    {
        name: "reserveVideoWitnessView",
        htmlFileName: "reserveVideoWitnessView",
        src: "./src/reserveView/ReserveVideoWitnessView.jsx",
        title: "",
        template:"./templates/common.html"
    },
    {
        name: "normalUploadIdCardView",
        htmlFileName: "normalUploadIdCardView",
        src: "./src/reserveView/NormalUploadIdCardView.jsx",
        title: "",
        template:"./templates/common.html"
    },
    {
        name: "normalUploadSupplementCardView",
        htmlFileName: "normalUploadSupplementCardView",
        src: "./src/reserveView/NormalUploadSupplementCardView.jsx",
        title: "",
        template:"./templates/common.html"
    },
    {
        name: "idInfoEditView",
        htmlFileName: "idInfoEditView",
        src: "./src/reserveView/IdInfoEditView.jsx",
        title: "",
        template:"./templates/common.html"
    },
    {
        name: "contractQueryView",
        htmlFileName: "contractQueryView",
        src: "./src/contractQueryView/contractQueryView.jsx",
        title: "",
        template:"./templates/common.html"
    },
    {
        name: "contractInfoView",
        htmlFileName: "contractInfoView",
        src: "./src/contractQueryView/contractInfoView.jsx",
        title: "",
        template:"./templates/common.html"
    },
    {
        name: "contractFileView",
        htmlFileName: "contractFileView",
        src: "./src/contractQueryView/contractFileView.jsx",
        title: "",
        template:"./templates/common.html"
    },
    {
        name: "contractFileThirdView",
        htmlFileName: "contractFileThirdView",
        src: "./src/contractQueryView/contractFileThirdView.jsx",
        title: "",
        template:"./templates/common.html"
    },
    {
        name: "contractPureContentView",
        htmlFileName: "contractPureContentView",
        src: "./src/contractQueryView/contractPureContentView.jsx",
        title: "",
        template:"./templates/common.html"
    },
    {
        name: "contractInfoThirdView",
        htmlFileName: "contractInfoThirdView",
        src: "./src/contractQueryView/contractInfoThirdView.jsx",
        title: "",
        template:"./templates/common.html"
    },
    {
        name: "optionAccountView",
        htmlFileName: "optionAccountView",
        src: "./src/optionAccountView/OptionAccountView.jsx",
        title: "",
        template:"./templates/common.html"
    },
    {
        name: "optionRetView",
        htmlFileName: "optionRetView",
        src: "./src/optionAccountView/OptionRetView.jsx",
        title: "",
        template:"./templates/common.html"
    },
    {
        name: "optionConfirmPermissionView",
        htmlFileName: "optionConfirmPermissionView",
        src: "./src/optionAccountView/OptionConfirmPermissionView.jsx",
        title: "",
        template:"./templates/common.html"
    },
    {
        name: "optionAccountHintView",
        htmlFileName: "optionAccountHintView",
        src: "./src/simpleHintView/OptionAccountHintView.jsx",
        title: "",
        template:"./templates/common.html"
    },
    {
        name: "optionAccountSHHintView",
        htmlFileName: "optionAccountSHHintView",
        src: "./src/simpleHintView/OptionAccountSHHintView.jsx",
        title: "",
        template:"./templates/common.html"
    },
    {
        name: "optionAccountConditionCheckView",
        htmlFileName: "optionAccountConditionCheckView",
        src: "./src/simpleHintView/OptionAccountConditionCheckView.jsx",
        title: "",
        template:"./templates/common.html"
    },
    {
        name: "stockRotationQueryView",
        htmlFileName: "stockRotationQueryView",
        src: "./src/stockRotation/StockRotationQueryView.jsx",
        title: "",
        template:"./templates/common.html"
    },
    {
        name: "stockRotationChooseTypeView",
        htmlFileName: "stockRotationChooseTypeView",
        src: "./src/stockRotation/StockRotationChooseTypeView.jsx",
        title: "",
        template:"./templates/common.html"
    },
    {
        name: "thirdBoardOpenRetView",
        htmlFileName: "thirdBoardOpenRetView",
        src: "./src/stockRotation/ThirdBoardOpenRetView.jsx",
        title: "",
        template:"./templates/common.html"
    },
    {
        name: "stockRotationCloseRetView",
        htmlFileName: "stockRotationCloseRetView",
        src: "./src/stockRotation/StockRotationCloseRetView.jsx",
        title: "",
        template:"./templates/common.html"
    },
    {
        name: "iadviserBasicQueryView",
        htmlFileName: "iadviserBasicQueryView",
        src: "./src/iadviserBasic/IadviserBasicQueryView.jsx",
        title: "",
        template:"./templates/common.html"
    },
    {
        name: "counselingSignAgreementView",
        htmlFileName: "counselingSignAgreementView",
        src: "./src/counselingSignAgreement/counselingSignAgreementView.jsx",
        title: "",
        template:"./templates/common.html"
    },
    {
        name: "counselingResultView",
        htmlFileName: "counselingResultView",
        src: "./src/counselingSignAgreement/counselingResultView.jsx",
        title: "",
        template:"./templates/common.html"
    },
    {
        name: "counselingConfirmationView",
        htmlFileName: "counselingConfirmationView",
        src: "./src/suitablyView/CounselingConfirmationView.jsx",
        title: "",
        template:"./templates/common.html"
    },
    {
        name: "priceAdjustConfirmView",
        htmlFileName: "priceAdjustConfirmView",
        src: "./src/priceAdjustConfirmView/priceAdjustConfirmView.jsx",
        title: "",
        template:"./templates/common.html"
    },
    {
        name: "specialSecuritiesQueryView",
        htmlFileName: "specialSecuritiesQueryView",
        src: "./src/specialSecuritiesView/specialSecuritiesQueryView.jsx",
        title: "",
        template:"./templates/common.html"
    },
    {
        name: "specialSecuritiesRetView",
        htmlFileName: "specialSecuritiesRetView",
        src: "./src/specialSecuritiesView/specialSecuritiesRetView.jsx",
        title: "",
        template:"./templates/common.html"
    },
    {
        name: "liveDetectionView",
        htmlFileName: "liveDetectionView",
        src: "./src/liveDetectionView/liveDetectionView.jsx",
        title: "",
        template:"./templates/common.html"
    },
    {
        name: "taxInfoView",
        htmlFileName: "taxInfoView",
        src: "./src/personalView/taxInfoView/TaxInfoView.jsx",
        title: "",
        template:"./templates/common.html"
    },
    {
        name: "changeTradePasswordView",
        htmlFileName: "changeTradePasswordView",
        src: "./src/changePassword/ChangeTradePasswordView.jsx",
        title: "",
        template:"./templates/common.html"
    },
    {
        name: "changeFundPasswordView",
        htmlFileName: "changeFundPasswordView",
        src: "./src/changePassword/ChangeFundPasswordView.jsx",
        title: "",
        template:"./templates/common.html"
    },
    {
        name: "associationConfirmView",
        htmlFileName: "associationConfirmView",
        src: "./src/associationConfirm/AssociationConfirmView.jsx",
        title: "",
        template:"./templates/common.html"
    },
    {
        name: "associationListView",
        htmlFileName: "associationListView",
        src: "./src/associationConfirm/AssociationListView.jsx",
        title: "",
        template:"./templates/common.html"
    },
    {
        name: "positionProveView",
        htmlFileName: "positionProveView",
        src: "./src/associationConfirm/PositionProveView.jsx",
        title: "",
        template:"./templates/common.html"
    },
    {
        name: "associationConfirmResultView",
        htmlFileName: "associationConfirmResultView",
        src: "./src/associationConfirm/AssociationConfirmResultView.jsx",
        title: "",
        template:"./templates/common.html"
    },
    {
        name: "searchProgress",
        htmlFileName: "searchProgress",
        src: "./src/searchProgress/SearchProgress.jsx",
        title: "",
        template:"./templates/common.html"
    },
    {
        name: "businessReturnVisitView",
        htmlFileName: "businessReturnVisitView",
        src: "./src/businessReturnVisitView/BusinessReturnVisitView.jsx",
        title: "",
        template:"./templates/common.html"
    },
    {
        name: "checkAccountView",
        htmlFileName: "checkAccountView",
        src: "./src/checkAccount/CheckAccountView.jsx",
        title: "",
        template:"./templates/common.html"
    },
    {
        name: "checkAccountResultView",
        htmlFileName: "checkAccountResultView",
        src: "./src/checkAccount/CheckAccountResultView.jsx",
        title: "",
        template:"./templates/common.html"
    },
    {
        name: "delistHolderRightsView",
        htmlFileName: "delistHolderRightsView",
        src: "./src/delistHolderRights/DelistHolderRightsView.jsx",
        title: "",
        template:"./templates/common.html"
    },
    {
        name: "delistHolderRightsRetView",
        htmlFileName: "delistHolderRightsRetView",
        src: "./src/delistHolderRightsRet/DelistHolderRightsRetView.jsx",
        title: "",
        template:"./templates/common.html"
    },
    {
        name: "delistHolderRightsCancelView",
        htmlFileName: "delistHolderRightsCancelView",
        src: "./src/delistHolderRightsCancel/DelistHolderRightsCancelView.jsx",
        title: "",
        template:"./templates/common.html"
    },
    {
        name: "delistHolderRightsCancelRetView",
        htmlFileName: "delistHolderRightsCancelRetView",
        src: "./src/delistHolderRightsCancelRet/DelistHolderRightsCancelRetView.jsx",
        title: "",
        template:"./templates/common.html"
    },
    {
        name: "checkSdVersionView",
        htmlFileName: "checkSdVersionView",
        src: "./src/checkSdVersion/CheckSdVersionView.jsx",
        title: "升级提醒",
        template:"./templates/common.html"
    },
    {
        name: "contractSignView",
        htmlFileName: "contractSignView",
        src: "./src/contractSignView/ContractSignView.jsx",
        title: "协议签署",
        template:"./templates/common.html"
    },
    {
        name: "goGroupView",
        htmlFileName: "goGroupView4qq",
        src: "./src/goGroupView/GoGroupView.jsx",
        title: "",
        template:"./templates/common.html"
    },
    {
        name: "goGroupView",
        htmlFileName: "goGroupView4ths",
        src: "./src/goGroupView/GoGroupView.jsx",
        title: "",
        template:"./templates/common.html"
    },

]

//配置项初始化
pages = pages.map((val) => {
    let { name, title, htmlFileName, src, template } = val;
    let htmlName = '';
    //name 配置必传字段
    //title 页面标题 默认为空
    title = title || "&lrm;";
    //htmlFileName html文件名
    htmlName = htmlFileName || name;
    htmlFileName = (htmlFileName || name) + ".html";
    
    //src 源码路径 默认为`./src/${name}/${name}.js
    src = src || `./src/${name}/${name}.js`;
    //template html模板路径 默认为"./html/template.html"
    template = template || "./templates/common.html";

    return { name, title, htmlFileName, src, template, htmlName };
});

let entryObj = {};
pages.map((val) => {
    const { name, src } = val;
    entryObj[name] = [
        path.resolve(__dirname, "./switchPublicPath.js"),
        path.resolve(__dirname,src),
    ];
});

const ProjectPath = "/yjbwebmoc/moc/web/moc-pro/build";
const CDN_HOSTNAM = {
    DEV: "https://devwebappscdn.yjbtest.com",
    FZ: "https://fzwebappscdn.yjbtest.com",
    PROD: "https://webappscdn.yongjinbao.com.cn",
};
const CDN_PUBLIC_PATH = {
    DEV: CDN_HOSTNAM.DEV + ProjectPath,
    FZ: CDN_HOSTNAM.FZ + ProjectPath,
    PROD: CDN_HOSTNAM.PROD + ProjectPath,
};

const htmls = pages.flatMap((val) => {
    const { title, name, template, src, htmlFileName, htmlName } = val;
    let baseHtmlPluginOption = {
        title,
        // hash: true,
        template: template,
        chunks: [name],
        // 添加版本号变量
        templateParameters: {
            CONTROLLER_VERSION: controllerVersion
        }
    };
    return [
        new HtmlWebpackPlugin({
            ...baseHtmlPluginOption,
            filename: htmlFileName
        }),
        new HtmlWebpackPlugin({
            ...baseHtmlPluginOption,
            filename: `${htmlName}.fz.html`,
            publicPath: CDN_PUBLIC_PATH.FZ,
            CDN_HOST: CDN_HOSTNAM.FZ,
        }),
        new HtmlWebpackPlugin({
            ...baseHtmlPluginOption,
            filename: `${htmlName}.prod.html`,
            publicPath: CDN_PUBLIC_PATH.PROD,
            CDN_HOST: CDN_HOSTNAM.PROD,
        }),
    ]
});

module.exports = {
    htmlPlugins: htmls,
    entry: entryObj,
};
