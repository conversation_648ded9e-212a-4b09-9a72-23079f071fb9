/**
 * Created by JinYC on 2017/10/10.
 */
import Storage from '../../common/vendors/Storage.js';
import HandleData from '../../common/vendors/HandleData.js';
import Module from '../../common/vendors/Modules.js';
import ViewControl from '../../common/vendors/ViewControl.js';
import Tool from '../../common/vendors/Tool';
import { trackEvent } from '@sinolink/collect';
class Events {
	//构造函数
	constructor() {
		//console.log('Event init');
	}

	/**
	 * 开始Event click 事件执行  name group名称
	 */
	click = (groupName, groupId, callback, groupParam) => {
		Tool.waiting.show();
		let this_ = this;
		// let isSdTest = localStorage.getItem('isSdTest')
        // tosd为调试参数，无业务场景
        const tosd = comm.TOOLS.GetQueryString("sdTag");
        const realGroupName = groupName.split('ForWeb')[0];
		try {
			if(!settings.sdBothSwitch) {
				Dialog.notice("当前网络加载异常，请稍后重试。");
				setTimeout(function(){Controller.closeAllPage('',3);},3000);
				Tool.Loggercollect({ logtype: 'getparam_error', message: location.href });
				Tool.waiting.hide();
				return;
			}
		} catch (e) {}
		
        // 分业务开关
        // if(!['delistHolderRights', 'searchProgress'].includes(realGroupName) || tosd == '1') {
		if(true) {
            if(comm.TOOLS.getCookie('app_id') == 'yjb3.0') {
                // 客户端版本校验，每个业务可能限制不同的客户端版本
                try {
                    jssdk.ready(() => {
                        jssdk.getAppVersion(v => {
                            console.log('vvv:', v);
                            // 如果groupName在sdBothSwitch配置中且版本
                            if (!((settings.sdBothSwitch || []).includes(realGroupName) && comm.TOOLS.compareVersions.compare(v, '8.01.001', '<'))) {
                                groupName= this.getSdName(groupName); 
                            }
                            this.clickNext(groupName,groupId,callback,groupParam)
                        });
                    });
                } catch (_) {
                    groupName= this.getSdName(groupName);
                    this.clickNext(groupName,groupId,callback,groupParam)
                }
            } else {
                groupName= this.getSdName(groupName);
                this.clickNext(groupName,groupId,callback,groupParam)
            }
		}
        // } else {
        //     this.clickNext(groupName,groupId,callback,groupParam)
        // }
        
    };
    clickNext=(groupName,groupId,callback,groupParam)=> {
		trackEvent({
            event_name: 'ywbl_click',
            page_name: "ywbl",
            module_name:"eventClick",
            element_name: groupName,
            location: location.href
        });
        let this_=this;
        this.clearGroup(groupName);//清理可能存在的历史遗留信息
        this.initGroup(groupName,groupId,function(data){
            
            //console.log(this_.getCurrentModule(data).name);
            var m = this_.getCurrentModule(data).name;
            //2 , 找到对应渲染模块
            ViewControl[m](data,function(data){
                Tool.waiting.hide();
                callback(data);
            });
        },groupParam);
    }
	/**
	 * 获取思迪的groupName
	 */
	getSdName = groupName => {
		if (groupConfig[`${groupName.split('ForWeb')[0]}_sd`] == undefined) {
			return groupName;
		}
		if (
			groupName.substring(groupName.length - 6, groupName.length) == 'ForWeb' &&
			(groupConfig[groupName.substring(0, groupName.length - 6)] || ['doubleRecordForWeb'].includes(groupName))
		) {
			groupName = groupName.split('ForWeb')[0] + '_sd';
			let moduleConfig = JSON.parse(JSON.stringify(groupConfig[groupName]));
			console.log(moduleConfig['modules'][0].setting);
			let i = 0;
			try {
				while (i < moduleConfig['modules'].length && moduleConfig['modules'][i].setting.isOpenNewPage) {
					moduleConfig['modules'][i].setting.isOpenNewPage = false;
					i++;
				}
				groupConfig[groupName] = moduleConfig;
				console.log(groupConfig);
			} catch (e) {
				console.log(e);
			}
		} else {
			groupName = groupName + '_sd';
		}
		return groupName;
	};

	/**
	 * 创建Group
	 */
	getGroup = (groupName, callbackData) => {
		/**
		 * 根据GroupId加载配置文件
		 */
		for (var group in groupConfig) {
			// console.log(group);
			// console.log(groupConfig[group]);

			if (groupName === group) {
				var currentGroup = groupConfig[group];
				/**
				 * group 定义唯一标识
				 */
				currentGroup.id = comm.TOOLS.generateUUID();

				//break;
				return currentGroup;
			}
		}
		//HandleData.handleModuleData(currentGroup,{},0,function(data){
		//    callbackData(currentGroup.id);
		//});
	};

	/**
	 * Group初始化 name group名称
	 */
	initGroup = (groupName, groupId, callbackData, groupParam) => {
		/**
		 * 已有groupId,流程继续
		 */
		if (groupId && Storage.getItem(groupId)) {
			this.handleGroup(Storage.getItem(groupId), function(callbackHandle) {
				callbackData(callbackHandle);
			});
			return;
		}
		/**
		 * 根据GroupId加载配置文件
		 */
		for (var group in groupConfig) {
			// console.log(group);
			// console.log(groupConfig[group]);

			if (groupName === group) {
				var currentGroup = groupConfig[group];
				/**
				 * group 定义唯一标识
				 */
				currentGroup.id = comm.TOOLS.generateUUID();
				currentGroup.param = groupParam;
				//存储group配置
				Storage.setItem(currentGroup.id, currentGroup);

				this.handleGroup(currentGroup, function(callbackHandle) {
					callbackData(callbackHandle);
				});
				break;
			}
		}
	};

	/**
	 * Group 处理函数
	 */
	handleGroup = (groupConf, callbackData) => {
		// var modules = currentGroup.modules;
		/**
		 * module 处理结果分为两种 :  1, next  2,block
		 * 1, next   则继续执行加一个Module模块
		 * 2, block  多用于需用户行为触发模块, eq : 条件检测,不通过则一直处于block状态
		 */
		this.initModule(groupConf, function(callbackModule) {
			// block
			if (HandleData.getCurrentModule(callbackModule).id == HandleData.getCurrentModule(groupConf).id) {
				//回调至view层,处理UI及跳转
				callbackData(callbackModule);
			}
		});
	};

	/**
	 *  Module初始化
	 */
	initModule = (groupConf, callbackData) => {
		var module = HandleData.getCurrentModule(groupConf);
		/**
		 * 为当前模块生成唯一id标识
		 */
		if (module.id == null) {
			module.id = comm.TOOLS.generateUUID();
		}
		/**
		 *  通过对象的属性访问 & 递归 1,next场景
		 */
		var this_ = this;
		this.evil(groupConf, module, function(callbackModule) {
			/**
			 * module队列无变化,则回调给view层处理,若有,则递归下一任务
			 */
			var currentModule = HandleData.getCurrentModule(callbackModule);
			if (currentModule.id != null && currentModule.id == module.id) {
				callbackData(callbackModule);
			} else {
				/**
				 *  递归遍历, 终止标记为 next==false (尾模块) or module 对象模块列表为空
				 */
				this_.initModule(groupConf, callbackData);
			}
		});
	};

	/**
	 * 函数加载器
	 */
	evil = (groupConf, module, callbackData) => {
		console.log(module.name);
		//window[module.name](currentGroup,callbackData);

		for (var m in Module) {
			if (m == module.name) {
				Module[m](groupConf, callbackData);
			}
		}
	};

	/**
	 *  Module完成
	 */
	handleModuleData = (groupConf, result, state, callback) => {
		HandleData.handleModuleData(groupConf, result, state, callback);
	};

	/**
	 * 获取group中的当前module
	 */
	getCurrentModule = groupConf => {
		return HandleData.getCurrentModule(groupConf);
	};

	/**
	 *  获取当前module的位置
	 */
	getModuleIndex = groupConf => {
		return HandleData.getModuleIndex(groupConf);
	};

	/**
	 * 根据module-id 获取 module
	 */
	getModuleById = (id, groupConf) => {
		return HandleData.getModuleById(id, groupConf);
	};

	///**
	// * 根据groupId获取group中的当前module
	// */
	//getCurrentModuleById = (groupId) => {
	//    return HandleData.getCurrentModuleById(groupId);
	//};

	/**
	 * 根据groupId获取groupConf
	 */
	getGroupConf = groupId => {
		return HandleData.getGroupConf(groupId);
	};

	/**
	 * 设置group参数
	 */
	setGroupParam = (groupId, param) => {
		return HandleData.setGroupParam(groupId, param);
	};

	/**
	 * 获取group
	 */
	getGroupParam = groupId => {
		return HandleData.getGroupParam(groupId);
	};

	clearGroup = groupName => {
		try {
			if (groupConfig[groupName]) {
				let group = groupConfig[groupName];
				let modules = group.modules;
				group.id = '';
				modules.map(module => {
					delete module.done;
					delete module.id;
				});
			}
		} catch (e) {
			console.log(e);
		}
	};
}

//module.exports = new Events();
//if(window) window.Events = new Events();

if (window && typeof module === 'object' && typeof module.exports === 'object') {
	module.exports = window.Events = new Events();
}
