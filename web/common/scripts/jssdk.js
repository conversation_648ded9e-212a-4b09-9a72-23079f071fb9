/**@module yjb3-jssdk */
/**
 * 
 * version: 1.0.8 
 * 
 */
var $jssdk = (function(){

    var constants = constants || {};
    /**
     * 　　1- 新闻<br>
     * 　　2- 资金<br>
     * 　　3- 公告<br>
     * 　　4- 简况<br>
     * 　　5- 财务<br>
     *　　 6- 研报<br>
     */
    constants.QUOTATION_TABS = {
        1: '新闻',
        2: '资金',
        3: '公告',
        4: '简况',
        5: '财务',
        6: '研报'
    }

    //全局对象，用于存放回调方法的的uuid
    window.respCallbacks = {};
    
    /**
     * 供native调用，用来统一处理native的返回信息
     * @param resp 处理结果
     */
    window.respFromNative = function(data, callbackId) {
        if (callbackId) {
            //取出回调
            var respCallback = window.respCallbacks[callbackId];
            //调用前端回调
            typeof respCallback === 'function' && respCallback(data);
            delete window.respCallbacks[callbackId];
        } else {
            console.log('native resp failed');
            return;
        }
    }

    /**
     * 触发deviceready事件,供native调用
     * @function deviceReady
     *
     */
    window.deviceReady = function(){
        var drEvent = document.createEvent('Event');
        drEvent.initEvent('deviceready',true,true);
        document.dispatchEvent(drEvent);
    };

    /**
     * 调用native功能
     * @param funcId native功能号
     * @param {string} param 入参
     * @param respCallback 回调
     * @param noNativeInterfaceAvailable 未桥接的回调
     */
    function exec(funcId, param, respCallback, noNativeInterfaceAvailable) {
        //创建请求UUID
        var callbackId = d8f8759ddabe4b8b81240a32bbf4d244().replace(/-/g,'');
        
        //以请求id为键，将回调放入回调列表
        window.respCallbacks[callbackId] = respCallback;
        
        //当yjbInteface未被有效绑定时，undefined会导致js报错，所以这个检测很有必要
        // if (window.yjbInterface == {} || window.yjbInterface == undefined || window.yjbInterface.execute == undefined) {
        //     typeof noNativeInterfaceAvailable === 'function' && noNativeInterfaceAvailable(new ReferenceError('未与native进行桥接！'));
        //     return;
        // }
        // window.yjbInterface.execute(funcId, JSON.stringify(param), callbackId);
        
        var userAgent = navigator? (navigator.userAgent? navigator.userAgent : '' ): '';
        var isWKWebView = userAgent.indexOf('ios/wkwebview') > -1 ;
        if(isWKWebView){
            if (window.webkit.messageHandlers.yjbInterface == {} || window.webkit.messageHandlers.yjbInterface == undefined || window.webkit.messageHandlers.yjbInterface.postMessage == undefined) {
                console.error('未与native进行桥接！');
                return;
            }
            window.webkit.messageHandlers.yjbInterface.postMessage({functionId:funcId, param:JSON.stringify(param), callbackId:callbackId});
        }else{
            //当yjbInteface未被有效绑定时，undefined会导致js报错，所以这个检测很有必要
            if (window.yjbInterface == {} || window.yjbInterface == undefined || window.yjbInterface.execute == undefined) {
                // typeof noNativeInterfaceAvailable === 'function' && noNativeInterfaceAvailable(new ReferenceError('未与native进行桥接！'));
                console.error('未与native进行桥接！');
                return;
            }
            window.yjbInterface.execute(funcId, JSON.stringify(param), callbackId);
        }
    }


    /**
     * 监听native桥接，桥接成功后执行回调函数
     * @function ready
     * @param {function} onDeviceReady 回调函数
     */
    function ready(onDeviceReady){
        //如果已有桥接对象，直接resovle;如果没有桥接对象，注册监听
        // if(typeof window.yjbInterface === 'object' && typeof window.yjbInterface.execute === 'function'){
        //     typeof onDeviceReady === 'function' && onDeviceReady();
        // } else {
        //     document.addEventListener('deviceready', function(){
        //         typeof onDeviceReady === 'function' && onDeviceReady();
        //     }, false);
        // }

        var userAgent = navigator? (navigator.userAgent? navigator.userAgent : '' ): '';
        var isWKWebView = userAgent.indexOf('ios/wkwebview') > -1 ;
        var isInitDevice = false;
        if(isWKWebView){
            isInitDevice = typeof window.webkit.messageHandlers.yjbInterface === 'object' && typeof window.webkit.messageHandlers.yjbInterface.postMessage === 'function';
        }else{
            isInitDevice = typeof window.yjbInterface === 'object' && typeof window.yjbInterface.execute === 'function';
        }
        //如果已有桥接对象，直接resovle;如果没有桥接对象，注册监听
        if(isInitDevice){
            typeof onDeviceReady === 'function' && onDeviceReady();
        } else {
            document.addEventListener('deviceready', function(){
                typeof onDeviceReady === 'function' && onDeviceReady();
            }, false);
        }
    }


    /**
     * 打开新的webview
     * @function open
     * @param {object} params 参数<br>
     *   　　url	    {string}	当前需要打开的url地址<br>
     *   　　fullScreen	{number}	是否全屏展示 0-否，1-是  在展示资讯内容的时候有需要全屏展示<br>
     *   　　title	    {string}	页面的标题，如果不传或者传空，在load结束时，使用H5的标题。<br>
     *   　　leftType	{number}	"按钮类型<br>
     *     　　　　          0-没有按钮 <br>
     *        　　　　       1-返回<br>
     *       　　　　        98-图片自定义(设置按钮为图片格式)"<br>
     *       　　　　        99-自定义(设置为默认底图，显示对应文本)"<br>
     *   　　rightType	{number}	类型同上<br>
     *   　　leftText	{string}	当type=98时图片的地址，当type=99时按钮标题<br>
     *   　　rightText	{string}	当type=98时图片的地址，当type=99时右边按钮标题<br>
     *   　　leftJS	    {string}	点击左边按钮，调用的js函数名或者js语句块，客户端直接调用即可，如果是函数，H5传值需为”functionName()”而不是functionName。<br>
     *   　　rightJS	{string}	点击右边按钮，调用的js函数名或者js语句块，客户端直接调用即可，如果是函数，H5传值需为”functionName()”而不是functionName。<br>
     *   　　leftURL	{string}	点击左边按钮，跳转的链接地址，未设置leftJS有效<br>
     *   　　rightURL	{string}	点击右边按钮，跳转的链接地址，未设置rightJS有效<br>
     *   　　animated {number}   跳转是否有动画，0为没有，1为有<br>
     *   　　showLoadingPage   {number}    0:不显示loading效果，1：显示loading效果<br>
     * @param {boolean} needCloseCurrentView 是否需要关闭当前webview
     */
    function open(params,needCloseCurrentView){
        var functionId = needCloseCurrentView ? 2007 : 2008;
        exec(functionId,params);
    }

    /**
     * 关闭当前webview
     * @function close 
     * @param {number} type 类型<br/>
     *    　　0	    返回到客户端页面堆栈中的上一个页面(默认为0)<br>
     *    　　1	    返回到自选首页<br>
     *    　　2	    返回到行情首页<br>
     *    　　3	    返回到普通交易首页<br>
     *    　　4	    返回到信用交易首页<br>
     *    　　5	    返回到首页<br>
     *    　　6	    返回到自选-组合<br>
     *    　　7	    返回到“我的“首页<br>
     *       8      返回到"理财"首页<br>
     *    　　98	    返回到指定页面，需传第二个参数，指定返回的backUrl<br>
     *       99     自定义返回页面（仅支持web页面跳转）
     *                  
     * @param {object} args 其他传参<br>
     *      当type=98时，定义backUrl为具体返回url
     * 
     */
    function close(type, args){
        var params = {
                type: 0
            };
        if(type ===1){
            params = {
                type: 1
            };
        }else if (type && type < 98){
            params.type = 2;
            params.rootType = type
        } else if(type === 98){
            params.type = 3;
            params.url = args.backUrl
        } else if(type === 99){ //自定义返回页面（仅支持web页面跳转）
            params.animated = 0;
            params.type = 3;
            params.url = '/bridge/build/bridge.html?login_type=' 
                        + args.loginType + '&back_url=' 
                        + encodeURIComponent(args.backUrl) 
                        + (args.version ? ( '&version=' + args.version) : '')
                        + (args.channelId ? ('&channel_id=' + args.channelId) : '')
                        +(typeof args.needLogin !== 'undefined' ? ('&is_weak_login=' + args.needLogin) : '')
        }
        exec(2009,params);
    }

    /**
     * 注销登录
     * @function logout
     * @param {number} loginType 
     *      登录类型： <br>
     *              1-普通登录<br>
     *              2-信用登录<br>
     *              3-担保品划转登录（普通+信用）<br>
     *              4-通行证passport登录<br>
     *              5-普通+passort登录<br>
     * @param {function} onSuccess 
     * @param {function} onFail 
     */
    function logout(loginType,onSuccess, onFail){
        exec(4001,{type: loginType},onSuccess,onFail);
    }

    /**
     * 获取版本号
     * @function getSystemVersion
     * @param {function} callback callback接收结果作为参数
     */
    function getSystemVersion(callback){
        exec(2000, {
            keys: ['systemVersion']
        }, function(data){
            var result = null;
            if(+data.code === 0){
                result = data.result.systemVersion
            }
            callback(result)
        })

    }

    /**
     * 获取系统 
     * @function getSystemName
     * @param {function} callback callback接收结果作为参数
     */
    function getSystemName(callback){
        exec(2000, {
            keys: ['systemName']
        }, function(data){
            var result = null;
            if(+data.code === 0){
                result = data.result.systemName
            }
            callback(result)
        })

    }

    /**
     * 获取app版本号
     * @function getAppVersion
     * @param {function} callback callback接收结果作为参数
     */
    function getAppVersion(callback){
        exec(2000, {
            keys: ['appVersion']
        }, function(data){
            var result = null;
            if(+data.code === 0){
                result = data.result.appVersion
            }
            callback(result)
        })

    }

    /**
     * 获取UDID
     * @function getUDID
     * @param {function} callback callback接收结果作为参数
     */
    function getUDID(callback){
        exec(2000, {
            keys: ['UDID']
        }, function(data){
            var result = null;
            if(+data.code === 0){
                result = data.result.UDID
            }
            callback(result)
        })
    }

    /**
     * 获取UID
     * @function getUID
     * @param {function} callback callback接收结果作为参数
     */
    function getUID(callback){
        exec(2000, {
            keys: ['uid']
        }, function(data){
            var result = null;
            if(+data.code === 0){
                result = data.result.uid
            }
            callback(result)
        })
    }

    /**
     * 登录
     * @function login
     * @param {number} loginType 
     *              1-普通交易登录<br>
     *              2-信用交易登录<br>
     *              3-担保品划转登录<br>
     * @param {string} backUrl 登录成功后跳转的url，默认当前页面
     * @param {string} version
     *                  '2.0'-SSO2.0<br>
     *                  '1.0'-SSO1.0<br>
     *                  'ukey'-UKey登录
     * @param {string} channelId 渠道id
     *                  如果version为ukey,必须传入channelId
     * @param {number} newPage 1-在新的webView中打开backUrl, 0-关闭当前webview，并在新的webview中打开backUrl
     * @param {number} type  2009接口参数
     * @param {number} rootType  2009接口参数
     */
    function login(loginType, backUrl, version, channelId, newPage, type ,rootType){
        backUrl = backUrl || location.href;
        //删除backUrl中instant_token和op_station参数
        backUrl = backUrl.replace(/&?instant_token=[^&]*/g,'');
        backUrl = backUrl.replace(/&?op_station=[^&]*/g,'');
        backUrl = backUrl.replace(/&?app_id=[^&]*/g,'');
        backUrl = backUrl.replace(/&?t=[^&]*/g,'');

        open({
            leftType: 1,
            animated: 0,
            url:'/bridge/build/bridge.html?login_type=' 
                    + loginType + '&back_url=' 
                    + encodeURIComponent(backUrl) 
                    + (version ? ( '&version=' + version) : '')
                    + (channelId ? ('&channel_id=' + channelId) : '')
                    + (type!=null ? ('&type='+type) : '')
                    + (rootType!=null ? ('&root_type='+rootType) : '')
                    + '&from_url=' + encodeURIComponent(location.href)
        },!newPage)
    }

    /**
     * 注册回到页面时执行的方法
     * 注意：回调中立即执行window.alert会阻塞IOS页面跳转
     * @function onAppear
     * @param {function} fn 
     */
    function onAppear(fn){
        if(typeof fn !== 'function') throw TypeError('参数只能为function类型')
        window.yjbsdk = window.yjbsdk || {};
        window.yjbsdk.onPageAppear = fn;
        exec(2011,{'onAppear': 'window.yjbsdk.onPageAppear()'});
    }
    /**
     * 注册回到页面时执行的方法
     * 注意：回调中立即执行window.alert会阻塞IOS页面跳转
     * @function onAppear
     * @param {function} fn
     */
    function onDisappear(fn){
        if(typeof fn !== 'function') throw TypeError('参数只能为function类型')
        window.yjbsdk = window.yjbsdk || {};
        window.yjbsdk.onPageDisappear = fn;
        exec(2011,{'onDisappear': 'window.yjbsdk.onPageDisappear()'});
    }
    /**
     * 注册关闭页面时执行的方法
     * 注意：回调中立即执行window.alert会阻塞IOS页面跳转
     * @function onAppear
     * @param {function} fn
     */
    function onClose(fn){
        if(typeof fn !== 'function') throw TypeError('参数只能为function类型')
        window.yjbsdk = window.yjbsdk || {};
        window.yjbsdk.onPageClose = fn;
        exec(2011,{'onClose': 'window.yjbsdk.onPageClose()'});
    }
    /**
     * 拨打电话
     * @function call
     * @param {string} phoneNumber 电话号码（座机、手机）
     */
    /**@public */
    function call(phoneNumber){
        exec(3000,{phone: phoneNumber})
    }

    /**
     * 自定义标题和左右上角按钮 <br>
     * @function setTitle
     * @see {@link open} 
     * @param {object} param 参数说明见open
     */
    function setTitle(param){
        exec(2012,param);
    }

    /**
     * 弹出提示框
     * @function showAlert
	 * @param {string} content 显示内容
	 * @param {string} title 显示标题
     * @param {function} success 点击我知道了后回调
     */
    function showAlert(content,title, success) {
        exec(3003,{
                content: content,
                buttonTitle: title || '我知道了'
            },
            function(data){
                if(+data.code === 0) {
                    success && success(data.result);
                }
            }
        )
    }

    /**
     * 弹出确认提示框
     * @function showConfirm
     * @param {string} content 显示内容
     * @param {function} onConfirm 点击确定后回调
     * @param {function} onCancel 点击取消后回调
	 * @param {function} confirmTitle 确定键标题
     * @param {function} cancelTitle 取消键标题
     */
    function showConfirm(content, onConfirm, onCancel,confirmTitle,cancelTitle) {
        exec(
            3003,
            {
                content: content,
                buttonTitle:confirmTitle || '确定',
                otherButtonTitle:cancelTitle || '取消'
            },
            function(data){
                if(+data.code === 0) {
                     if(data.result && data.result.clicked === 0){
                        typeof onConfirm === 'function' && onConfirm(data.result)
                    }else if (data.result.clicked === 1){
                        typeof onCancel === 'function' && onCancel(data.result)
                    }
                } 
            }
        )
    }

    /**
     * 打开开户链接
     * @function openAccount
     */
    function openAccount() {

        var isAndroid = navigator.userAgent.toLowerCase().indexOf('android') > -1,
            androidAPPURL = 'cn.com.gjzq.yjb.kh.forothers://?PreFix=QBA&SkinType=1',
            iosAPPURL = 'com.yjb.kh://ui=1&terminal=tzt&callid=gjtrade.gjkaihu',
            androidDownloadURL = 'https://appdl.yongjinbao.com.cn/yjb/android/yjbkh.apk',
            iosDownloadURL = 'https://itunes.apple.com/cn/app/yong-jin-bao-kai-hu/id887771302?l=zh&ls=1&mt=8';


        var appURL = iosAPPURL,
            downloadURL = iosDownloadURL;

        if (isAndroid) {
            appURL = androidAPPURL;
            downloadURL = androidDownloadURL;
        }

        exec(3002, {
            appURL: appURL,
            downloadURL: downloadURL
        });
    }

    /**
     * 完成适当性测评,关闭webview，继续登录前的操作
     * @function doneSuitability
     * @param {any} loginType 登录类型 见：login
     * @param {any} isValid  是否完成：0-未完成，1-已完成
     */
    function doneSuitability(loginType, isValid) {
        exec(4009, {type: +loginType, valid: +isValid});
    }

    /**
     * 选择照片
     * @function chooseImg
     * @param {Object} param 
     *  {<br />
     *  source: 0-从相册选择，1-拍照　<br />
     *  front: 0-后置摄像头，1-前置摄像头<br />
     *  size: 客户端需要将图片高或者宽中较大的一边，缩小到这个size，然后将另一边按比例缩放<br />
     *  compressionQuality:JPEG压缩比例，范围为0.0-1.0。size和压缩比暂定1200和0.6，后续可调整。<br />
     *  floatingLayer：　浮层类型，仅在source为1时生效。0没有，1身份证正面（人头），2身份证反面（国徽），3自拍头像。<br />
     * }<br />
     * @param {function} callback 回调函数<br/>
     *  code 0成功，1失败，2取消。<br />
     *  result.data: base64编码的图片信息
     */
    function chooseImg(param,  callback){
        exec(3004, param, callback);
    }
    /**
     * 跳转到个股行情。<br>
     * 若指定tabId,跳转到指定的tab页面
     * @function quotation
     * @param {number} stockCode 股票代码
     * @param {number} tabId     tabId <br>
     * 　　1- 新闻<br>
     * 　　2- 资金<br>
     * 　　3- 公告<br>
     * 　　4- 简况<br>
     * 　　5- 财务<br>
     *　　 6- 研报<br>
     * @param {number} codeType 股票类型(十进制)
     */
    function quotation(stockCode, tabId, codeType){
        if(!stockCode) throw new TypeError('stockCode is required');
        var param = {
            type: 1, //跳转个股综合屏

            stockCode: stockCode+''
        }
        //如果指定tabId，跳转到指定tab位置
        if(tabId) param.tabName = constants.QUOTATION_TABS[tabId];
        if(codeType) param.codeType = +codeType;
        exec(4002,param);
    }

    /**
     * 跳转到信息导入页面。<br>
     */
    function informationImport(){
        var param = {
            type: 16
        }
        exec(4002,param);
    }

    /**
     * 跳转客户端本地页面跳转
     */
    function clientJump(param){

        exec(4002,param);
    }

    /**
     * 获取自选股列表
     * @function getPortfolioList
     * @param {function} callback callback接收结果作为参数
     */
    function getPortfolioList(callback){
        exec(4012, {
            keys: ['optionalStocks']
        }, function(data){
            var result = null;
            if(+data.code === 0){
                result = data.result.optionalStocks
            }
            callback(result)
        })

    }

    /**
     * 获取登录状态
     * @function getLoginStatus
     * @param {function} callback callback接收结果作为参数
     */
    function getLoginStatus(callback){
        exec(4012, {
            keys: ['loginStatus']
        }, function(data){
            var result = null;
            if(+data.code === 0){
                result = data.result.loginStatus
            }
            callback(result)
        })

    }

    /**
     * 添加股票至自选股
     * @function addToPortfolio
     * @param {number} stockCode 股票代码
     * @param {function} onSuccess 成功回调
     * @param {function} onFail 失败回调
     * @param {number} codeType 股票类型(十进制)
     */
    function addToPortfolio(stockCode, onSuccess, onFail, codeType){
        if(!stockCode) throw new TypeError('stockCode is required');
        var param = {
            stockCode: stockCode+''
        };
        if(codeType) param.codeType = +codeType;
        exec(4011, param, function(data){
            if(+data.code === 0){
                typeof onSuccess === 'function' && onSuccess(data);
            } else {
                typeof onFail === 'function' && onFail(data);
            }
        })
    }

    /**
     * 获取股票搜索历史
     * @function getStockSearchHistory
     * @param {function} callback callback接收结果作为参数 
     */
    function getStockSearchHistory(callback){
        exec(4012, {
            keys: ['quotationSearchList']
        }, function(data){
            var result = null;
            if(+data.code === 0){
                result = data.result.quotationSearchList
            }
            callback(result)
        })
    }


    /**
     * ukey登录
     * 回调函数接收返回结果，结果:0 - 登录成功; 1 - 取消登录
     * @function ukeyLogin 
     * @param {function} callback 回调函数接收返回结果，结果:0 - 登录成功; 1 - 取消登录
     */
    function ukeyLogin(callback){
        exec(4000, {
            type: 1,
            needPassportId: 1,
            needStockAccount: 1
        }, function(data){
            callback(data.code)
        })        
    }
    /**
     * 获取系统参数(依赖内部环境)
     */
    function getPassportId(callback){
        exec(4004, {
            keys: ['passportId']
        }, function(data){
            var passportId = null;
            if(+data.code === 0){
                passportId = data.result && data.result.passportId ? data.result.passportId : null;
            }
            callback(passportId);
        })
    }
    /**
     * 获取系统参数(依赖内部环境)
     */
    function getUserId(callback){
        exec(4004, {
            keys: ['userId']
        }, function(data){
            var userId = null;
            if(+data.code === 0){
                userId = data.result && data.result.userId ? data.result.userId : null;
            }
            callback(userId);
        })
    }
    /**
     * user登录
     * 回调函数接收返回结果，结果:0 - 登录成功; 1 - 取消登录
     * @function ukeyLogin
     * @param {function} callback 回调函数接收返回结果，结果:0 - 登录成功; 1 - 取消登录
     */
    function userLogin(callback){
        exec(4015, {
            needUKey: 1,
        }, function(data){
            callback(data.code)
        })
    }

    /**
     * 从非系统参数中获取与channelId对应的ukey
     * @function getUkeyByChannelId
     * @param {String} channelId 渠道编号
     * @param {function} callback 获取成功后，callback接收ukey作为参数（获取失败时，返回null）
     */
    function getUkeyByChannelId(channelId, callback){
        exec(4012, {
            keys: ['ukeys']
        }, function(data){
            var ukey = null;
            if(+data.code === 0){
                var ukeys = data.result && data.result.ukeys ? data.result.ukeys : {};
                ukey = ukeys[channelId];
            } 
            callback(ukey);
        })
    }

    /**
     * 从非系统参数中获取userUKeys
     * @param {String} channelId 渠道编号
     * @function getUserUKeys
     * @param {function} callback 获取成功后，callback接收ukey作为参数（获取失败时，返回null）
     */
    function getUserUKeysByChannelId(channelId,callback){
        exec(4012, {
            keys: ['userUKeys']
        }, function(data){
            //alert(''+JSON.stringify(data));
            var userUKey = null;
            if(+data.code === 0){
                var userUKeys = data.result && data.result.userUKeys ? data.result.userUKeys : {};
                userUKey = userUKeys[channelId];
            }
            callback(userUKey);
        })
    }

    /**
     * 获取资金流向数据
     * @function getCapital
     * @param {String} code 渠道编号
     * @param {String} codeType 渠道编号
     * @param {function} callback 回调（获取失败时，返回null）
     */
    function getCapital(code,codeType,callback){
        exec(4017, {
            code: code,
            codeType:codeType
        }, function(data){
            var result = null;
            if(+data.code === 0){
                result = data.result
            }
            callback(result)
        })
    }

    /**
     * 支付宝支付
     * @function goAlipay
     * @param {String} order 对应支付宝支付的参数orderString
     * @param {function} callback 支付宝支付返回的信息
     *
     */
    function goAlipay(order, callback){
        exec(3012, {
            order: order
        }, function(data){
            var result = null;
            if(+data.code === 0){
                result = data.result
            }
            callback(result)
        })
    }

    /**
     * 微信支付
     * @function goWeChatPayment
     * @param {String} appId 商家向财务通申请的appId
     * @param {String} partnerId 商家向财务通申请的商家id
     * @param {String} prepayId 预支付订单
     * @param {String} nonceStr 随机串,防重发
     * @param {String} timeStamp 时间串,防重发
     * @param {String} packageStr 商家根据财付通文档填写的数据和签名
     * @param {String} sign 商家根据微信开放平台文档对数据做的签名
     * @param {function} callback 微信支付返回的信息
     */
    function goWeChatPayment(appId,partnerId, prepayId, nonceStr, timeStamp, packageStr, sign, callback){
        exec(3013, {
            appId:appId,
            partnerId: partnerId,
            prepayId: prepayId,
            nonceStr: nonceStr,
            timeStamp: timeStamp,
            package: packageStr,
            sign: sign,
            callback: callback
        }, function(data){
            // var result = null;
            var result = data;
            result.errCode = data.code;
            result.errStr = data.message;
            callback(result)
        })
    }

    /**
     * 微信分享
     * @function goWeChatShare
     * @param {String} param 分享参数
     * @param {function} callback 微信分享返回的信息
     */
    function goWeChatShare(param, callback){
        exec(3015, param, function(data){
            callback(data)
        })
    }
        
    /**
     * 设置电池栏(statusBar)字体颜色 仅对iOS生效
     * @function statusBar
     * @param {object} param 参数<br>
     *       style    {number}    0-透明底色-黑字，1-透明底色-白字。<br>
     */
    function statusBar(param){
        exec(3016,param);
	}
		
		/**
     * 二维码扫描
     * @function scanCode
     * @param {function} callback 扫描返回的信息
     */
    function scanCode(callback){
			exec(3007, {}, function(data){
					callback(data)
			})
		}

    /**
     * pdf跳转
     * @function pdfRead
     * @param {object} event 
     */
    function pdfRead(event,title){
        if(!title){
            title = '';
        }
        var oUrl = {leftType : 1,url : ''};
        oUrl.url = event.currentTarget.dataset["url"];
        if(!oUrl.url)return;
        exec(1003,{url:oUrl.url,name:"pdf",type:2,title:title});
    }

    /**
     * 打开文件
     * @function pdfRead
     * @param {object} param 
     */
    function openFile(param){
        console.log(param);
        exec(1003,{
            url: param.url,
            type: param.type,
            title: param.title
        });
    }


    /**
     * 读取已经阅读之后的文件
     * @function infoRead {function} 
     * 
     */
    function infoRead(options,callback){
        exec(2005,options,function(data){
            var readData = null;
            if(+data.code === 0){
                readData = data.result && data.result.data ? data.result.data : {};
            } 
            callback(readData);
        });
    }

    /** 
     * 保存已经阅读的新闻的infoCode
     * @function infoSaveAndGo {function} 
     *
     */
    function infoSaveAndGo(options){
        exec(2004,options);
    }
    /**
     * 获取版本号
     * @function getInterfaceVersion
     * @param {function} callback callback接收结果作为参数
     */
    function getInterfaceVersion(callback){
        exec(2000, {
            keys: ['interfaceVersion']
        }, function(data){
            var result = null;
            if(+data.code === 0){
                result = data.result.interfaceVersion
            }
            callback(result)
        })

    }

    /**
     * 客户端本地录制视频
     * @function chooseVideo
     * @param {Object} param
     *  {<br />
     *  front	number	0代表非前置（后置）摄像头，1代表前置摄像头。
     *  width	number	视频宽度
     *  height	number	视频高度
     *  duration	number	视频最大长度，单位秒，默认-1，不限制视频长度。
     *  bitRate	number	码率
     *  fps	number	帧数
     *  title	string	录制屏幕上方显示的标题（例如“请用标准普通话匀速朗读”）
     *  content	string	录制屏幕上方显示的正文（例如“本人自愿在国金证券开户”）
     * }<br />
     * @param {function} callback 回调函数<br/>
     *  code 0成功，1失败，2取消。<br />
     *  result.filePath: 客户端存储视频的本地路径。
     *  result.fileSize: 视频文件大小
     *  result.duration: 视频文件长度
     */
    function chooseVideo(param,  callback){
        exec(3009, param, callback);
    }
    /**
     * 通过http/https向服务器post数据

     * @function uploadFile
     * @param {Object} param
     *  {<br />
     *  url	string	http/https上传接口的服务器地址。
     *  filePath	string	native本地文件地址。
     *  fileData	string	path和data二选一，可以直接上传以base64编码后的二进制流，也可以上传本地文件。
     * }<br />
     * @param {function} callback 回调函数<br/>
     *  code 0成功，1失败<br />
     *  result	string	json格式字符串，从文件上传服务返回，透传给H5.
     */
    function uploadFile(param,  callback){
        exec(1004, param, callback);
    }
    /**
     * 注册点击安卓物理返回按键时执行的方法
     * 注意：回调中立即执行window.alert会阻塞IOS页面跳转
     * @function onBackKeyPressed
     * @param {function} fn
     */
    function onBackKeyPressed(fn){
        if(typeof fn !== 'function') throw TypeError('参数只能为function类型')
        window.yjbsdk = window.yjbsdk || {};
        window.yjbsdk.onBackKeyPressed = fn;
        exec(2011,{'onBackKeyPressed': 'window.yjbsdk.onBackKeyPressed()'});
    }

    function goBackPrev(){
        var params ={
            type:1,
        }
        exec(2009,params);
    }
    function startVideoWitness(params,respCallback, noNativeInterfaceAvailable){
        console.log(params);
        exec(3005,params,respCallback, noNativeInterfaceAvailable);
    }

    
    function startVideoWitnessForZg(params,respCallback, noNativeInterfaceAvailable){
        console.log(params);
        exec(3025,params,respCallback, noNativeInterfaceAvailable);
    }

    function endVideoWitnessForZg(params,respCallback, noNativeInterfaceAvailable){
        console.log(params);
        exec(3026,params,respCallback, noNativeInterfaceAvailable);
    }

    //自述式单向视频
    function toSelfVideo(params,respCallback){
        console.log(params);
        exec(3036,params,respCallback);
    }

    //问答式单向
    function toQAVideo(params,respCallback){
        console.log(params);
        exec(3037,params,respCallback);
    }

    //股东户权限变更通知
    function stockAuthNotify(param,  callback){
        exec(4021, param, callback);
    }

    //活体检测
    function liveDetection(param,  callback){
        exec(3018, param, callback);
    }

    // 打开思迪sdk
    function toThinkive(url, closeStackView) {
        exec(4023, {thinkiveUrl: url, closeStackView: closeStackView});
    }

    // 打开微信小程序
    function openMiniProgram(param,  callback){
        exec(3027, param, callback);
    }

    //截屏监控
    function screenShot(param,  callback){
       var type = param.type;
       window.showAlert = ()=>{
            showAlert('请妥善保存您的账号及密码，您保存的图片内容可能涉及到敏感信息，请勿发送给他人');
       }
       if(type==1){
           // ios 781
            exec(2011, {
                "onScreenShot":"window.showAlert()"
            }, callback);
       }else if(type==2){
           // 安卓 781
            exec(3040, {},callback );
       }
         
    }

/*****************************工具类方法*************************************/


    /**
     * 是否为object类型
     * @param {any} arg 
     * @returns {boolean} 是否为object类型
     */
    function isObject(arg){
        var result = false;
        if(arg !== null && typeof arg === "object" && arg.constructor === Object){
            result = true;
        }
        return result;
    }

    /*****************************工具类方法END*************************************/


    /**
     * 如果是执行单元测试，暴露所有工具方法，以供测试
     */
    if(typeof process === 'object' && typeof process.env === 'object' && process.env.NODE_ENV === 'testing') {
        return {
            exec: exec,
            open: open,
            close: close,
            goBack: close, //返回前一个页面，同关闭当前webview一个功能
            logout: logout,
            login: login,
            onAppear: onAppear,
            onClose: onClose,
            onDisappear: onDisappear
        }
    } else {
        //仅暴露公共函数供第三方使用
        return {
            ready: ready,
            open: open,
            close: close,
            /**
             * 返回
             * @function goBack
             * @param {number} type
             *  0-返回上一个webview（默认）<br>
             *  1-返回到自选首页<br>
             *  2-返回到行情首页<br>
             *  3-返回到普通交易首页<br>
             *  4-返回到信用交易首页<br>
             *  5-返回到发现首页<br>
             *  6-返回自选-组合<br>
             *  
             *  98-返回到指定hybrid页面 注type为98时，需传第二个参数<br/>
             *  99-返回到指定web页面 注：type为99时，需传第二个参数<br>
             *  @param {Object} param 跳转参数
             * 　　　　　　　　　　　　needLogin: <br>
             *                      　　　　0-不需要登录<br>
             *                      　　　　1-需要登录　<br>
             *                     　loginType <br>
             *                     　　　　1-普通交易登录<br>
             *                     　　　　2-信用交易登录<br>
             *                     　　　　3-担保品划转登录<br>
             *                     　backUrl 跳转的url，默认当前页面<br>
             *                     　version<br>
             *                     　　　　'2.0'-SSO2.0<br>
             *                     　　　　'1.0'-SSO1.0<br>
             *                     　　　　'ukey'-UKey登录
             *                     　channelId 渠道id
             *                     　　　　如果version为ukey,必须传入channelId
             **/
            goBack: close, //返回前一个页面，同关闭当前webview一个功能
            logout: logout,
            login: login,
            onAppear: onAppear,
            onClose: onClose,
            onDisappear: onDisappear,
            call: call,
            setTitle: setTitle,
            showConfirm: showConfirm,
            showAlert: showAlert,
            doneSuitability: doneSuitability,
            chooseImg: chooseImg,
            ukeyLogin: ukeyLogin,
            userLogin: userLogin,
            getUkeyByChannelId: getUkeyByChannelId,
            getUserUKeysByChannelId:getUserUKeysByChannelId,
            getStockSearchHistory: getStockSearchHistory,
            quotation: quotation,
            addToPortfolio: addToPortfolio,
            getPortfolioList: getPortfolioList,
            getLoginStatus: getLoginStatus,
            openAccount : openAccount,
            goAlipay : goAlipay,
            goWeChatPayment:goWeChatPayment,
            getSystemVersion : getSystemVersion,
            getSystemName: getSystemName,
            getAppVersion:getAppVersion,
            getPassportId:getPassportId,
            getUserId:getUserId,
            getUDID : getUDID,
            getUID : getUID,
			goWeChatShare:goWeChatShare,
			scanCode:scanCode,
            informationImport:informationImport,
            clientJump:clientJump,
            openFile: openFile,
            pdfRead : pdfRead,
            infoRead : infoRead,
            infoSaveAndGo : infoSaveAndGo,
            getInterfaceVersion:getInterfaceVersion,
            chooseVideo:chooseVideo,
            uploadFile:uploadFile,
            onBackKeyPressed:onBackKeyPressed,
            goBackPrev:goBackPrev,
            startVideoWitness:startVideoWitness,
            startVideoWitnessForZg: startVideoWitnessForZg,
            endVideoWitnessForZg: endVideoWitnessForZg,
            toSelfVideo: toSelfVideo,
            toQAVideo: toQAVideo,
            stockAuthNotify,
            liveDetection:liveDetection,            
            statusBar:statusBar,
            toThinkive: toThinkive,   
            screenShot:screenShot, 
            openMiniProgram: openMiniProgram,   
        }
    }

    })();

    //uuid_v4
    !function(n){{var e;e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:this,e.d8f8759ddabe4b8b81240a32bbf4d244=n()}}(function(){return function n(e,r,o){function t(f,u){if(!r[f]){if(!e[f]){var a="function"==typeof require&&require;if(!u&&a)return a(f,!0);if(i)return i(f,!0);var d=new Error("Cannot find module '"+f+"'");throw d.code="MODULE_NOT_FOUND",d}var l=r[f]={exports:{}};e[f][0].call(l.exports,function(n){var r=e[f][1][n];return t(r?r:n)},l,l.exports,n,e,r,o)}return r[f].exports}for(var i="function"==typeof require&&require,f=0;f<o.length;f++)t(o[f]);return t}({1:[function(n,e,r){function o(n,e){var r=e||0,o=t;return o[n[r++]]+o[n[r++]]+o[n[r++]]+o[n[r++]]+"-"+o[n[r++]]+o[n[r++]]+"-"+o[n[r++]]+o[n[r++]]+"-"+o[n[r++]]+o[n[r++]]+"-"+o[n[r++]]+o[n[r++]]+o[n[r++]]+o[n[r++]]+o[n[r++]]+o[n[r++]]}for(var t=[],i=0;i<256;++i)t[i]=(i+256).toString(16).substr(1);e.exports=o},{}],2:[function(n,e,r){(function(n){var r,o=n.crypto||n.msCrypto;if(o&&o.getRandomValues){var t=new Uint8Array(16);r=function(){return o.getRandomValues(t),t}}if(!r){var i=new Array(16);r=function(){for(var n,e=0;e<16;e++)0===(3&e)&&(n=4294967296*Math.random()),i[e]=n>>>((3&e)<<3)&255;return i}}e.exports=r}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{}],3:[function(n,e,r){function o(n,e,r){var o=e&&r||0;"string"==typeof n&&(e="binary"==n?new Array(16):null,n=null),n=n||{};var f=n.random||(n.rng||t)();if(f[6]=15&f[6]|64,f[8]=63&f[8]|128,e)for(var u=0;u<16;++u)e[o+u]=f[u];return e||i(f)}var t=n("./lib/rng"),i=n("./lib/bytesToUuid");e.exports=o},{"./lib/bytesToUuid":1,"./lib/rng":2}]},{},[3])(3)});

    if(typeof module === 'object' && typeof module.exports === 'object'){
        module.exports = $jssdk;
    }

