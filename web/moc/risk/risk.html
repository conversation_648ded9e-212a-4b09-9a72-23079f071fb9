<!DOCTYPE html>
<html style="">
<head>
	<title>投资风险测评</title>
	<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
	<meta name="viewport" content="initial-scale=1.0,maximum-scale=1.0,minimum-scale=1.0,user-scalable=no,target-densitydpi = medium-dpi">
	<meta name="format-detection" content="telephone=no">
	<meta name="apple-touch-fullscreen" content="YES">
	<meta name="apple-mobile-web-app-capable" content="yes">
	<meta name="apple-mobile-web-app-status-bar-style" content="black">
	<link href="../common/css/base-min.css" rel="stylesheet" type="text/css" media="all"/>
	<link href="../risk/getmesg.css" rel="stylesheet" type="text/css" media="all" />
	<link href="./style/risk.css" rel="stylesheet" type="text/css" media="all" />
	<script src="../js/config.js"></script>
	<script src="../common/js/jquery-1.8.3.min.js"></script>
	<script src="../../common/scripts/common-full.js"></script>
	<script src="../../common/scripts/jssdk.js"></script><script src="../../common/scripts/bridge/controller.js"></script>
	<script src="../../common/scripts/bridge/app.js"></script>
	<script src="../common/js/common-min.js"></script>
	<script src="../js/publicSelect.js"></script>
	<script src="../js/vconsole.min.js"></script>
	<!-- <script src="https://cdn.bootcss.com/vConsole/3.2.0/vconsole.min.js"></script> -->
    <script>
		var vConsole = new VConsole();
		if(localStorage.getItem('isSdTest') == null || localStorage.getItem('isSdTest') == '0') {
			localStorage.setItem('isSdTest', '1');
			localStorage.setItem('isSdUatTest', '1');
        }
    </script>
<body hidden>

<div class="content">
	<div class="question">
		<p align="center"><img src="../img/loading_red.png" class="loading" /></p>
		<p align="center">正在载入数据,请稍候...</p>
	</div>
	<div id="prebutton" class="prebutton" style="display:none;"><span id="topre">上一题</span></div>
</div>	
<div id="footer" hidden><div class="opera"><a href="javascript:;">开始评测</a></div></div>

<div id="showError">
	<div class="tableWrapper">
		<div class="lostHole">
			<div class="requestLost">

			</div>
			<p class="lostTip">
				亲，宝宝正在努力清算，请稍后再来更新您的风险测评。
			</p>
		</div>
	</div>
	<!--<div class="footer"><a href="javascript:Controller.goBackPage();">返回</a></div>-->
</div>

<div class="notice" style="display: none;font-size: 14px;">
	<div class="topimg" style="width: 270px;margin: 0 auto;padding-top: 40px;">
		<img style="width: 100%;" src="notice_top.png">
	</div>
	<div class="notice_title" style="text-align: center;line-height: 22px;padding-top: 10px;font-size: 20px;"></div>
	<div class="notice_content" style="margin: 0 10%;padding: 10px 0;line-height: 22px;color: #666666;text-align: center;"></div>
</div>

<script src="../tol/appUtils.js"></script>
<script src="../tol/user.js"></script>
<script src="../tol/utils.js"></script>
<script src="../tol/service.js"></script>
<script src="../js/publicSelect.js"></script>
<script type="text/javascript">
	

	var auto = true;
	var actionName = '';
	var ready = false;
	var confitConfirmNum = 0;
	var step = 1; // 当前题目标识 1-第一题；2-中间题；3-最后一题
	var currentIndex = -1;
	var num = 0; // 渲染到页面的题目数量
	var quesListHtml = '';
	var newQuesList = []; // 页面渲染题目的数组
	var clickable = true;
	var current_questionnaire_version = null; // 当前风测版本号
	var last_questionnaire_version = null; // 最近一次风测版本号
	var user_answer_list = []; // 最近一侧风测答案

	var PAPERTYPE = 1, //试卷类别
			ANSWERCUR = 'answerCur',LISTCUR = 'mistake';
	var questionnaire_id='';
	var account_type = $.getUrlParameter('account_type');
	var app_id = $.getUrlParameter('app_id');
	var fromlogin = $.getUrlParameter('from');
	var again = $.getUrlParameter('again');
	var organ_flag=0; // 客户类型标识   0-个人户；1-机构户
	var client_id = '',op_station='';
	var id_no = '';
	var gender = '';
	var formatedQuestions = [];

	/**
	 * type 代表冲突的二种类型 1\2
	 * 1 常规互斥
	 * 2 自定义逻辑互斥
	 * 
	 * relationship 代表互斥类型 1\2
	 * 1 非强制互斥
	 * 2 强制互斥
	*/
	var quesConflictConfig = {
		'quesIdItems': ['889','890','892','900','1027','1028','1029','8020'],
		'forceQuesIdItems': ['892','1028','1029','8020'],
		'quesIndexMap': {
			'889': 2,
			'890': 3,
			'892': 5,
			'900': 8,
			'1027': 12,
			'1028': 13,
			'1029': 17,
			'8020': 15
		},
		'optionIndexMap': {
			'1': 'A',
			'2': 'B',
			'3': 'C',
			'4': 'D',
			'5': 'E',
			'6': 'F',
			'7': 'G'
		},
		'autoConflictItem':[{
                quesId: '888',
                type: '3',
                tipMessage: '您的年龄与选项可能存在不相符，请再次确认信息真实准确或进行修改。',
				quesIdPairItems: [],
				condition: {
                    matchedOptionId: '6',
                    maleAge: '50',
					femaleAge: '45'
                },
                eventId: '2020029',
             }
		],
        'quesIdConflictConfigItems': [
            {
                quesId: '889',
                type: '1',
                quesIdPairItems: [
                    {   
                        relationship: '1',
                        matchedQuesId: '890',
                        quesIdPair: [889,890],
                        optionIdPair: [['1','2'],['5','6']],
                        tipMessage: '您可用于投资的金额（第3题）与您的家庭收入情况（第2题）可能不符，请确认勾选的信息准确无误。',
                        eventId: '1104801'
                    } 
                ]
            },{
                quesId: '890',
                type: '1',
                quesIdPairItems: [
                    {
                        relationship: '1',
                        matchedQuesId: '889',
                        quesIdPair: [890,889],
                        optionIdPair: [['5','6'],['1','2']],
                        tipMessage: '您可用于投资的金额（第3题）与您的家庭收入情况（第2题）可能不符，请确认勾选的信息准确无误。',
                        eventId: '1104802'
                    } 
                ]
            },{
                quesId: '892',
                type: '1',
                quesIdPairItems: [
                    {
                        relationship: '2',
						matchedQuesId: '1029',
                        matchedOptIds: ['5'],
                        quesIdPair: [892,1029],
                        optionIdPair: [['1'],['5']],
                        tipMessage: '您的投资回报用途（第17题）与您的债务情况（第5题）不符，请确认勾选的信息准确无误并重新选择。',
                        eventId: '1104803'
                    }
                ]
            },{
                quesId: '900',
                type: '2',
                tipMessage: '您的投资经验与您的年龄情况可能不符，请确认勾选的信息准确无误。',
				quesIdPairItems: [],
				condition: {
                    matchedOptionId: '4',
                    age: '26'
                },
                eventId: '1104804'
            },{
                quesId: '1027',
                type: '1',
                quesIdPairItems: [
                    {
                        relationship: '1',
                        matchedQuesId: '1028',
                        quesIdPair: [1027,1028],
                        optionIdPair: [['2','3','4','23','24','34','234','12','13','14','123','124','134','1234'],['1']],
                        tipMessage: '您的投资需求（第13题）可能与您重点参与的投资品种（第12题）不符，请确认勾选的信息准确无误。',
                        eventId: '1104805'
                    },{
                        relationship: '1',
                        matchedQuesId: '8020',
                        quesIdPair: [1027,8020],
                        optionIdPair: [['2','3','4','23','24','34','234','12','13','14','123','124','134','1234'],['1']],
                        tipMessage: '您能承受的最大投资损失（第15题）可能与您重点参与的投资品种（第12题）不符，请确认勾选的信息准确无误。',
                        eventId: '1104806'
                    }
                ]
            },{
                quesId: '1028',
                type: '1',
                quesIdPairItems: [
                    {
                        relationship: '2',
						matchedQuesId: '8020',
						matchedOptIds: ['1'],
                        quesIdPair: [1028,8020],
                        optionIdPair: [['2','3','4','23','24','34','234','12','13','14','123','124','134','1234'],['1']],
                        tipMessage: '您能承受的最大投资损失（第15题）与您的投资需求（第13题）不符，请确认勾选的信息准确无误并重新选择。',
                        eventId: '1104808'
                    },{
                        relationship: '1',
                        matchedQuesId: '1027',
                        quesIdPair: [1028,1027],
                        optionIdPair: [['1'],['2','3','4','23','24','34','234','12','13','14','123','124','134','1234']],
                        tipMessage: '您的投资需求（第13题）可能与您重点参与的投资品种（第12题）不符，请确认勾选的信息准确无误。',
                        eventId: '1104807'
                    }
                ]
            },{
                quesId: '1029',
                type: '1',
                quesIdPairItems: [
                    {
                        relationship: '2',
						matchedQuesId: '892',
                        matchedOptIds:['1'],
                        quesIdPair: [1029,892],
                        optionIdPair: [['5'],['1']],
                        tipMessage: '您的投资回报用途（第17题）与您的债务情况（第5题）不符，请确认勾选的信息准确无误并重新选择。',
                        eventId: '1104809'
                    }
                ]
            },{
                quesId: '8020',
                type: '1',
                quesIdPairItems: [
                    {
                        relationship: '2',
                        matchedQuesId: '1028',
                        matchedOptIds:['2','3','4'],
                        quesIdPair: [8020,1028],
                        optionIdPair: [['1'],['2','3','4','23','24','34','234','12','13','14','123','124','134','1234']],
                        tipMessage: '您能承受的最大投资损失（第15题）与您的投资需求（第13题）不符，请确认勾选的信息准确无误并重新选择。',
                        eventId: '1104811'
                    },{
                        relationship: '1',
                        matchedQuesId: '1027',
                        quesIdPair: [8020,1027],
                        optionIdPair: [['1'],['2','3','4','23','24','34','234','12','13','14','123','124','134','1234']],
                        tipMessage: '您能承受的最大投资损失（第15题）可能与您重点参与的投资品种（第12题）不符，请确认勾选的信息准确无误。',
                        eventId: '1104810'
                    }
                ]
            }
        ]
    }

	if(app_id == undefined){
		app_id=Controller.getAppId();
	}
	//适配3.0页面返回按钮事件
	if(app_id == 'yjb3.0' && fromlogin=='login'){
		window.doneSuitability0 = function(){
			Controller.doneSuitability(account_type,0);
		}
		jssdk.ready(function(){
			jssdk.setTitle({
				navBarBgColor: "#FFFFFF",
				titleColor: "#333333",
				rightTextColor: "#333333",
				leftText: '/common/imgs/left_arrow_grey.png',
				'fullScreen':0,
				'leftType':98,
				'leftJS':'window.doneSuitability0()',
				'rightType':0,
			});
            jssdk.statusBar({
                style: 0,
            });
        })
	}
	//适配3.0,重新测评的返回按钮事件  (如果second,重新测评,也认为是未完成,)
	else if(app_id == 'yjb3.0' && again=='second'){
		window.doneSuitability1 = function(){
			Controller.doneSuitability(account_type,0);
		}
		jssdk.ready(function(){
			jssdk.setTitle({
				navBarBgColor: "#FFFFFF",
				titleColor: "#333333",
				rightTextColor: "#333333",
				leftText: '/common/imgs/left_arrow_grey.png',
				'fullScreen':0,
				'leftType':98,
				'leftJS':'window.doneSuitability1()',
				'rightType':0,
			});
            jssdk.statusBar({
                style: 0,
            });
        })
	} else if (app_id == 'yjb3.0'){
		jssdk.ready(function(){
			jssdk.setTitle({
				navBarBgColor: "#FFFFFF",
				titleColor: "#333333",
				rightTextColor: "#333333",
				leftText: '/common/imgs/left_arrow_grey.png',
				'fullScreen':0,
				'leftType':98,
				'leftJS':'Controller.goBackPage()',
				'rightType':0,
			});
            jssdk.statusBar({
                style: 0,
            });
        })
	}


	function backPassport(){
		//window.changeURL("http://action:10002/?");
		Controller.goBackPage();
	}

	var isAcceptMargin=function(callback){
		var boolValue=true;
		service.isacceptmargin({},function(data){
			if(data.code==0){
				if(!data.result.isacceptmargin){
					boolValue=false;
					//boolValue=true; //临时测试
				}
			}
			callback(boolValue);
		},{
			fnTimeOut:function(){
				callback(boolValue);
			}
		})
	};

	function toRemoveSelect(question){
		var index = question.index;
		var question_options = question.question_options;

		question_options.forEach(function(option){
			option.selected = false;
			var option_id = option.option_id;
			$('#ques-'+index+'option-'+option_id).removeClass(ANSWERCUR);
		})

		question.select = '';
		question.question_options = question_options;
	}

	 /**
     * by Jgl 2020-07-28
     * type
     *  -1 互斥约束
     *  -2 年龄约束
     */
    function handleConflict(quest,quesId,optionId) {
		// checkMatchedNum(quest);
		
		var that = this;
        /**
         * 判断当前时选中还是取消选中，在非强制互斥下可以直接返回，在强制互斥下，要将之前置灰的选项恢复可选择
         * 
         * selected
         * -true 选中
         * -false 取消选中
         * 
         */
        
		var select = '';
		var question_options = quest.question_options;
		var question_type = quest.question_type;
		var index = quest.index;
		question_options.forEach(function(option){
			if(option.selected){
				select = select + option.option_id;
			}
		})
        var selected = false;
        if(select.indexOf(optionId + '') != -1){
            selected = true;
		}

        var quesIdItems = quesConflictConfig.quesIdItems;
        var quesIdConflictConfigItems  = quesConflictConfig.quesIdConflictConfigItems;
		var quesIndexMap = quesConflictConfig.quesIndexMap;
		var optionIndexMap = quesConflictConfig.optionIndexMap;
        if(quesIdItems.indexOf(quesId) != -1){
            var idx = quesIdItems.indexOf(quesId);
            var quesConf = quesIdConflictConfigItems[idx];
 
            var type = quesConf.type;
			
            switch(type){
                case '1':
                    var quesIdPairItems = quesConf.quesIdPairItems;
		
					var conflictLenth = quesIdPairItems.length;
					if(conflictLenth == 2){
						var matchRes = checkMatchedNum(quest);
						console.log(matchRes);

						var matchNum = matchRes['num'];
						var matched1 = matchRes['matched1'];
						var matched2 = matchRes['matched2'];
						console.log('matchNum=',matchNum);
						console.log('matched1=',matched1);
						console.log('matched2=',matched2);
					}

					for(var i = 0; i< quesIdPairItems.length; i ++){
						var value = quesIdPairItems[i];

                        var relationship = value.relationship;
                        var matchedQuesId = value.matchedQuesId;
                        var matchedOptIds = value.matchedOptIds;
                        var matchedQues = getQuesById(matchedQuesId);
          
                        var matchedQuestionSelect = matchedQues.select;
         
                        if(value.optionIdPair[0].indexOf(optionId)!=-1){

                            var tipInfo = value.tipMessage;
                            var eventId = value.eventId;

                            /**
                             * 强制互斥还是非强制互斥逻辑判断
                             * 
                             * relation
                             * -1 非强制互斥
                             * -2 强制互斥
                             * 
                             */

							/**
                             * 强制互斥约束由于没有用户选择的动作，所以不需要留痕
							 * 2021-06-18 By Jgl
							 * 新版强制互斥做的操作仅仅是取消当前选中项
							 * 2022-08-01 By Jgl
							 * 强制互斥交互改造
                             */
							
							if(relationship == '2' && value.optionIdPair[1].indexOf(matchedQuestionSelect)!= -1){
				
							}

							var conflictDone  = true;
							if(typeof value.done === 'undefined'){
								conflictDone = false
							}
							conflictDone = conflictLenth == 2 && conflictDone;
							// 弹窗提示
							value.done = true;
							
							if(question_type == 1
							&&conflictLenth == 2
							&& matchNum == confitConfirmNum
							&& matchNum == 1
							&& matched1 == true
							&& matched2 == false){
								auto = true;
								nextOneItem();
							}
                            if(!conflictDone && relationship == '1' && value.optionIdPair[1].indexOf(matchedQuestionSelect)!= -1){
                            
                                if(!selected){
                                    console.log('用户取消选中，不做处理，直接返回')
                                    return;
                                }

								

								layerUtils.confirmCustom(tipInfo,'重新选择','确认无误',function(){
									confitConfirmNum=0;
									console.log('点击重新选择按钮')
									actionName = '重新选择';
									// 非强制互斥逻辑，取消当前选择
									that.toRemoveSelect(quest);

								},function(){
									confitConfirmNum++;
									console.log('点击确认无误按钮');
									actionName = '确认无误';
									console.log('confitConfirmNum=',confitConfirmNum);
									console.log('conflictLenth=',conflictLenth);
									var quesIndex = quesIndexMap[quesId];
									var optionIndex = '';
									var currentQuestionSelect = quest.select;
									currentQuestionSelect.split('').forEach(function(option){
										optionIndex += optionIndexMap[option] || '';
									})

									var matchedQuesIndex = quesIndexMap[matchedQuesId];
									var matchedOptionIndex = '';
									matchedQuestionSelect.split('').forEach(function(option){
										matchedOptionIndex += optionIndexMap[option] || '';
									})
									var message = `题${quesIndex}选${optionIndex}，题${matchedQuesIndex}选${matchedOptionIndex}，客户自主确认`;
									PUBLCISELECT.Loggercollect({
										event_id: eventId,
										event_name: '风险测评互斥',
										message: message
									},function(oData){
										console.log(oData);
										if(oData['GRID0']=='Success'){
											//进行强制互斥检查
											var forceConflictStatus = checkForceConflict(quest);
											if(forceConflictStatus){
												handleForceConflict(quest);
											}else{
												unSelectResetForceConflict(quest);
											}
											// unSelectResetForceConflict(quest);
											if(question_type == 1 && matchNum == 1){
												auto = true;
												ready = true;
												console.log('LINE514');
												nextOneItem();
												return;
											}
								
											if(question_type == 1 && conflictLenth == 1){
												auto = true;
												ready = true;
												console.log('LINE522');
												nextOneItem();
												return;
											}
											if(matchNum == 2 && conflictLenth == 2){
												that.handleConflict(quest,quesId,optionId);
											}
											if(question_type == 1 && confitConfirmNum == 2){
												auto = true;
												ready = true;
												console.log('LINE532');
												nextOneItem();
											}

											if(conflictLenth == 2
											 	&& question_type == 1
												&& matchNum == 1
												&& matched1 == true
												&& matched2 == false){
													auto = true;
													console.log('LINE541');
													nextOneItem();
												}
												
										}else{
											// 留痕失败逻辑
											layerUtils.alert('很抱歉，受理失败了','确定',function(){
												console.log('取消当前选中项');
												that.toRemoveSelect(quest);
											});
										}
					
									}); 
								})
								
								break;
							}


                        }
					}
					
					// console.log('互斥逻辑处理完毕');
					// console.log('开始检测是否双重互斥中一种情形');
					// console.log('conflictLenth=',conflictLenth);
					// console.log('actionName=',actionName);
					// console.log('confitConfirmNum=',confitConfirmNum);
					// console.log('quesIdPairItems=',quesIdPairItems);
					// console.log('quesIdPairItems[0][done]=',quesIdPairItems[0]['done']);
					// console.log('quesIdPairItems[1][done]=',quesIdPairItems[1]['done']);

					// console.log(conflictLenth == 2 );
					// console.log(quesIdPairItems[0]['done'] == true);
					// console.log(quesIdPairItems[1]['done'] == true );
					// console.log(actionName=="确认无误");
					// console.log(confitConfirmNum == 1);

					if(conflictLenth == 2 
					&& quesIdPairItems[0]['done'] == true
					&& quesIdPairItems[1]['done'] == true 
					&& actionName=="确认无误"){
						// console.log('开始下一题');
						// auto = true;
						// nextOneItem()
						// return;
					}

					// ready代表跳转下一题状态
					// if(ready == false && actionName == '确认无误' && conflictLenth == 2 && confitConfirmNum == 1){
					// 	auto = true;
					// 	nextOneItem();
					// }


                    break;
                case '2':

                    if(!selected){
                        console.log('用户取消选中，不做处理，直接返回')
                        return;
                    }

                    var tipInfo = quesConf.tipMessage;
                    var eventId = quesConf.eventId;
                    var condition = quesConf.condition;
                    var age = condition.age;
                    var matchedOptionId = condition.matchedOptionId;
                    
                    var currentYear = new Date().getFullYear();
                    var currentMonth = new Date().getMonth()+1;
                    var currentDay = new Date().getDate();
                    if(currentMonth<10){
                        currentMonth= '0'+ currentMonth
                    }
                    if(currentDay<10){
                        currentDay= '0'+ currentDay
                    }
                    var currentDate = currentYear+''+currentMonth+''+currentDay;

					// 生日后一天才算大一岁
                    var birthDate = Number(id_no.substring(6, 14)) + 1;

                    var daysDiff = parseInt(currentDate) - birthDate;


                    if(optionId == matchedOptionId && daysDiff < Number(age)*10000){
                            
						// 弹窗提示
						layerUtils.confirmCustom(tipInfo,'重新选择','确认无误',function(){ 
		
							console.log('点击重新选择按钮')
							// 非强制互斥逻辑，取消当前选择
							that.toRemoveSelect(quest);

						},function(){
							console.log('点击确认无误按钮');
							var message = '题8选D，客户自主确认';
							PUBLCISELECT.Loggercollect({
								event_id: eventId,
								event_name: '风险测评互斥',
								message: message
							},function(oData){
								console.log(oData);
								if(oData['GRID0']=='Success'){
									//留痕成功，不做任何处理
									auto = true;
									nextOneItem();
								}else{
									// 留痕失败逻辑
									layerUtils.alert('很抱歉，受理失败了','确定',function(){
										console.log('取消当前选中项');
										that.toRemoveSelect(quest);

									});
								}
								
							}); 
						})
                    }
                    break;
					// case '3':

					// 	if(!selected){
					// 		console.log('用户取消选中，不做处理，直接返回')
					// 		return;
					// 	}
						
					// 	break;
                default:
                    console.log('未匹配项'+type)
            }

            
        }
	}

	function getQuesById(quesNo){
		var question;
		formatedQuestions.forEach(function(ques){
			if(ques.question_id == quesNo){
				question = ques;
			}
		})
		return question;
	}

	function getQuesByIdx(queryIdx){
		var question;
		formatedQuestions.forEach(function(ques, idx){
			if(queryIdx == idx){
				question = ques;
			}
		})
		return question;
	}
	
	$(function(){
		
		try{
			//行为采集
			PUBLCISELECT.LoggercollectUnlogin({
				event_id:1105712,
				event_name:'risk',
				message: window.location.href 
			});
		}catch(e){}

		/**
		 *  接入(桥接)逻辑处理
		 */
		
		App.appInit(function(result){
			if(result&&result.code === 0){
				setTimeout(()=>{
					if(comm.TOOLS.getCookie('app_id') == 'yjb3.0') {
						// 客户端版本校验，每个业务可能限制不同的客户端版本
						try {
							jssdk.ready(() => {
								jssdk.getAppVersion(v => {
									// 如果groupName在sdBothSwitch配置中且版本
									if (!((settings.sdBothSwitch || []).includes('risk') && comm.TOOLS.compareVersions.compare(v, '8.01.001', '<'))) {
										Controller.basePageInit(location.href.split('yjbwebmoc/moc/')[0]+'yjbwebmoc/moc/web/moc-pro/build/goGroupView.html?groupName=riskForWeb&' + location.search.split('?')[1]);
									} else {
										$('body').show();
									}
								});
							});
						} catch (_) {
							Controller.basePageInit(location.href.split('yjbwebmoc/moc/')[0]+'yjbwebmoc/moc/web/moc-pro/build/goGroupView.html?groupName=riskForWeb&' + location.search.split('?')[1]);				
						}
					} else {
						Controller.basePageInit(location.href.split('yjbwebmoc/moc/')[0]+'yjbwebmoc/moc/web/moc-pro/build/goGroupView.html?groupName=riskForWeb&' + location.search.split('?')[1]);
					}
				}, 500);
				client_id = result.result.clientId;
				op_station = result.result.opStation;

				//判断公告
				queryNotice({type:1002,subtype:10020018},function(status){
					if(status){
						if ((settings.sdSwitch || []).includes('risk')) {
							setTimeout(()=>{
								if(comm.TOOLS.getCookie('app_id') == 'yjb3.0') {
									// 客户端版本校验，每个业务可能限制不同的客户端版本
									try {
										jssdk.ready(() => {
											jssdk.getAppVersion(v => {
												// 如果groupName在sdBothSwitch配置中且版本
												if (!((settings.sdBothSwitch || []).includes('risk') && comm.TOOLS.compareVersions.compare(v, '8.01.001', '<'))) {
													Controller.basePageInit(location.href.split('yjbwebmoc/moc/')[0]+'yjbwebmoc/moc/web/moc-pro/build/goGroupView.html?groupName=riskForWeb&' + location.search.split('?')[1]);
												} else {
													$('body').show();
												}
											});
										});
									} catch (_) {
										Controller.basePageInit(location.href.split('yjbwebmoc/moc/')[0]+'yjbwebmoc/moc/web/moc-pro/build/goGroupView.html?groupName=riskForWeb&' + location.search.split('?')[1]);				
									}
								} else {
									Controller.basePageInit(location.href.split('yjbwebmoc/moc/')[0]+'yjbwebmoc/moc/web/moc-pro/build/goGroupView.html?groupName=riskForWeb&' + location.search.split('?')[1]);
								}
							}, 500);
						} else {
							$('body').show();
						}
						//检查清算时间
//						isAcceptMargin(function(result){
//							if(result){
								//查询userinfoquery
								PUBLCISELECT.getUserInfo({fundAccount: '($fund_account)', clientId: client_id},function(oReturn){
									//var organ_flag=0;

									console.log('oReturn',oReturn);
									id_no = oReturn.id_no;
									gender = oReturn.gender;
									questionnaire_id = '***********';
									if(oReturn.is_organ_client == 0){ //机构户
										questionnaire_id = '***********';
										organ_flag=1;
									}else if(oReturn.is_organ_client == 1){ //个人户
										questionnaire_id = '***********';
										organ_flag=0;
									}
									//获取风险测评问卷
									$.getData(SENDTO60, {
										UniqueKey: 'QCQ',
										questionnaire_id:questionnaire_id
										//paper_type:PAPERTYPE,
										//organ_flag:organ_flag,
										//prodta_no:''
									}, function (oReturn) {
										if (oReturn.ERRORNO != 0) {
											waiting.hide();
											layerUtils.alert($.replaceString(BASETIP, { error_no: oReturn.ERRORNO, error_info: oReturn.ERRORMESSAGE }));
											return false;
										}
										var oData = oReturn.GRID0;//errno = oData.error_no;
										console.log('[接口请求][格式化数据]');
										console.log(oData);
										if (oData.code != 0 ) {
											waiting.hide();
											layerUtils.alert($.replaceString(BASETIP, { error_no: oData.code, error_info: oData.message }));
											return false;
										}
										var questions = oData.result.questionnaire_content;
										current_questionnaire_version =  oData.result.questionnaire_version
										console.log('questions',questions);
										console.log('current_questionnaire_version',current_questionnaire_version);
										
										questions.forEach(function(ques, idx){
											ques.index = idx;
											ques.select = '';
											ques.question_options.forEach(function(option){
												option.selected = false;
												option.disabled = false;
											})
											formatedQuestions.push(ques);
										})
										console.log('formatedQuestions',formatedQuestions);
										splitPapers(formatedQuestions);
										//console.table(oData.result);
									})
									// 获取客户最后一条风测记录
									$.getData(SENDTO60, {
										UniqueKey: 'QRQ',
										questionnaire_id:questionnaire_id,
										user_account:'($usercode)',
										account_type:4,
										page_row:1,
										page_size:1,
									}, function (oReturn) {
										// 测试数据
										// oReturn.GRID0 = {"code":0,"message":null,"result":[{"account_type":"4","create_time":*************,"id":133351,"opstation":"","origin":"CHANNEL:yjbweb;DEVICE:mobile;SYSTEM:android","outer_status":null,"questionnaire_id":"***********","questionnaire_name":"投资者适当性评估表（个人）","questionnaire_version":"1.1","result_level":"C1（低风险承受）","score":-59,"score_level_version":"1.1","user_account":"********","user_answer":"[{\"answer\":[{\"choice\":\"2\"}],\"question_id\":\"889\"},{\"answer\":[{\"choice\":\"3\"}],\"question_id\":\"890\"},{\"answer\":[{\"choice\":\"1\"}],\"question_id\":\"891\"},{\"answer\":[{\"choice\":\"1\"}],\"question_id\":\"892\"},{\"answer\":[{\"choice\":\"1\"}],\"question_id\":\"1025\"},{\"answer\":[{\"choice\":\"4\"}],\"question_id\":\"899\"},{\"answer\":[{\"choice\":\"1\"}],\"question_id\":\"900\"},{\"answer\":[{\"choice\":\"1\"}],\"question_id\":\"894\"},{\"answer\":[{\"choice\":\"3\"}],\"question_id\":\"1026\"},{\"answer\":[{\"choice\":\"1\"}],\"question_id\":\"893\"},{\"answer\":[{\"choice\":\"1\"},{\"choice\":\"3\"},{\"choice\":\"4\"}],\"question_id\":\"1027\"},{\"answer\":[{\"choice\":\"1\"}],\"question_id\":\"1028\"},{\"answer\":[{\"choice\":\"1\"}],\"question_id\":\"897\"},{\"answer\":[{\"choice\":\"1\"}],\"question_id\":\"8020\"},{\"answer\":[{\"choice\":\"1\"}],\"question_id\":\"898\"},{\"answer\":[{\"choice\":\"1\"}],\"question_id\":\"1029\"},{\"answer\":[{\"choice\":\"1\"}],\"question_id\":\"1030\"}]"}]}
										if (oReturn.ERRORNO != 0) {
											waiting.hide();
											layerUtils.alert($.replaceString(BASETIP, { error_no: oReturn.ERRORNO, error_info: oReturn.ERRORMESSAGE }));
											return false;
										}
										var oData = oReturn.GRID0;
										if (oData.code != 0 ) {
											waiting.hide();
											layerUtils.alert($.replaceString(BASETIP, { error_no: oData.code, error_info: oData.message }));
											return false;
										}
										console.log('oData',oData);
										if(oData.result.length>0){
											last_questionnaire_version = oData.result[0].questionnaire_version
											user_answer_list = JSON.parse(oData.result[0].user_answer) || []
										}
										console.log('last_questionnaire_version,user_answer_list',last_questionnaire_version,user_answer_list);
									})

								})
//							}else{
//								$('.content').hide();
//								$('#footer').hide();
//								$('#showError').show();
//								//Controller.pageInit('riskerror.html');
//							}
//						});
					}
				});


			}else{
				setTimeout(function(){
					// $.use 异步加载
					layerUtils.iMsg(-1,'登录超时请返回重新登录！');
				},1000);
				setTimeout(function(){
					//Controller.closeAllPage('',98,{backUrl:'/me/view/index.html'});
                    Controller.closeAllPage('',3);
				},3000);
				return false;
			}
		},true);

	})
	// 获取指定的题目
	function geneOneItem(newQuesList,item,idx){
		var x = idx;
		var quesCode = '';
		// var item = mesgList[x];
			//x>=7 最后两道题使用long 样式，改为宽度为 100% ， x<2 前2道题是选择年龄和职业
			var temp_question_content=item.question_title;
			if(item.question_type==2){
				temp_question_content=item.question_title+"（多选）";
			}
			quesCode
					+='<div class="list '+(x>=0?'long':'')+'" data-index="'+ x+'" data-quesno="'+item.question_id+'" data-questype="'+item.question_type+'">'
					+'<p class="sortNo"><span class="pre">'+(x+1)+'</span>/'+newQuesList.length+'</p>'
					+'<p class="quesText" >' + temp_question_content + '</p>';

			var anserList = item.question_options;

			quesCode+='<div class="options">'
			for(var obj=0;obj<anserList.length;obj++) {
				var option = anserList[obj];
				var disabled = option.disabled;
				var selected = option.selected;
				var className = "answer";
				if(disabled){
					className += " disabled";
				}
				if(selected){
					className += " answerCur";
				}

				if(item.question_type==2){
					quesCode += '<p id="ques-'+x+'option-'+option.option_id+'" class="'+className+'" ><span class="ico more"></span>' + option.option_content + '</p>';
				}else{
					quesCode += '<p id="ques-'+x+'option-'+option.option_id+'" class="'+className+'" ><span class="ico"></span>' + option.option_content + '</p>';
				}

			}
			quesCode+='</div>'

			quesCode +='</div>';
			return quesCode;
	}
	// 下一题
	function nextOneItem(){
		clickable = true;
		confitConfirmNum = 0;
		console.log('before');
		console.log(currentIndex);
		if(currentIndex >= 0){
			var currItem = getQuesByIdx(currentIndex);
			if(currItem.select == ''){
				console.log('当前题未选中');
				return;
			}
		}
		
		currentIndex++;
		console.log('after');
		console.log(currentIndex);
		if(currentIndex == num-1){
			console.log('本题是最后一题');
			step = 3;
			$('.opera a').html('确认并提交');
		}
		if(currentIndex >= num){
			console.log('数组已越界');
			return;
		}
		var oneItem = newQuesList[currentIndex];
		console.log(oneItem);
		console.log(oneItem.question_id);
		if(oneItem.select){
			auto = false;
		}
		quesListHtml = geneOneItem(newQuesList, oneItem, currentIndex);
		if(currentIndex == 0){
			$('#prebutton').hide();
		}
		if(currentIndex > 0){
			$('#prebutton').show();
		}
		$('.question').hide().html(quesListHtml).slideDown(function(){
			$('.opera a').unbind("click");
			bindEvent();
		});
	}
	function preOneItem(){
		clickable = true;
		confitConfirmNum = 0;
		auto = false;
		console.log('before');
		console.log(currentIndex);
		currentIndex--;
		console.log('after');
		console.log(currentIndex);
		if(currentIndex == num-1){
			console.log('本题是最后一题');
			step = 3;
			$('.opera a').html('确认并提交');
		}
		if(currentIndex >= num){
			console.log('数组已越界');
			return;
		}
		var oneItem = newQuesList[currentIndex];
		console.log(oneItem);
		console.log(oneItem.question_id);
		quesListHtml = geneOneItem(newQuesList, oneItem, currentIndex);
		if(currentIndex == 0){
			$('#prebutton').hide();
		}
		if(currentIndex > 0){
			$('#prebutton').show();
			$('.opera a').html('下一题');
		}
		step = 2;
		$('.question').hide().html(quesListHtml).slideDown(function(){
			$('.opera a').unbind("click");
			bindEvent();
		});
	}
	$('#topre').click(function(){
		console.log('上一题');
		preOneItem();
	});

	//解析问卷回执数据
	function splitPapers(questions){
		//查询职业的数据字典
		var quesDesc;
		switch(organ_flag){
			case 0:
				quesDesc = '<div class="ques-desc" style="line-height:22px;border-radius:10px;">\
					<div style="font-weight:bold;font-size:18px;">风险承受能力评估问卷须知</div>\
					<div style="padding: 10px 0 20px;">本问卷旨在了解您可承受的风险程度等情况，借此协助您选择合适的产品或服务类别，以符合您的风险承受能力。</div>\
					<div style="padding: 10px 0 20px;">风险承受能力评估是本公司向投资者履行适当性义务的一个环节，其目的是使本公司所提供的产品或服务与您的风险承受能力等级相匹配。</div>\
					<div style="padding: 10px 0 20px;"><span style="font-weight:bold;">本公司特别提醒您：本公司向投资者履行风险承受能力评估等适当性义务，并不能取代您自己的投资判断，也不会降低产品或服务的固有风险。</span>同时，与产品或服务相关的投资风险、履约责任以及费用等将由您自行承担。</div>\
					<div style="padding: 10px 0 20px;">本公司提示您：本公司将根据您的测评结果评估您的风险承受能力等级，该风险承受能力等级将影响您购买产品或接受服务的范围。<span style="font-weight:bold;">您应当如实提供测评相关信息，并对所提供信息的真实性、准确性、完整性负责，否则您应当自行承担相应法律责任，同时本公司有权拒绝向您提供服务或者销售产品。</span>您对风险测评问卷某一问题的具体回答不构成本公司向您提供服务或销售产品时风险评估的唯一依据，也不构成您与本公司就本公司向您提供服务或销售产品之间达成任何约定或协议安排。</div>\
					<div style="padding: 10px 0 20px;">本公司建议：当您的各项状况发生重大变化时，需对您所投资的产品及时进行重新审视，以确保您的投资决定与您可承受的投资风险程度等实际情况一致。</div>\
					<div style="padding: 10px 0 20px;">本公司在此承诺，对于您在本问卷中所提供的一切信息，本公司将严格按照法律法规要求承担保密义务。除法律法规规定的有权机关依法定程序进行查询以外，本公司保证不会将涉及您的任何信息提供、泄露给任何第三方，或者将相关信息用于违法、不当用途。</div>\
				</div>';
			break;
			case 1:
			quesDesc = '<div class="ques-desc" style="line-height:22px;border-radius:10px;">\
				<div style="font-weight:bold;font-size:18px;">风险承受能力评估问卷须知</div>\
                <div style="padding: 10px 0 20px;">本问卷旨在了解贵单位可承受的风险程度等情况，借此协助贵单位选择合适的产品或服务类别，以符合贵单位的风险承受能力。</div>\
				<div style="padding: 10px 0 20px;">风险承受能力评估是本公司向投资者履行适当性义务的一个环节，其目的是使本公司所提供的产品或服务与贵单位的风险承受能力等级相匹配。</div>\
				<div style="padding: 10px 0 20px;"><span style="font-weight:bold;">本公司特别提醒贵单位：本公司向投资者履行风险承受能力评估等适当性义务，并不能取代贵单位自己的投资判断，也不会降低产品或服务的固有风险。</span>同时，与产品或服务相关的投资风险、履约责任以及费用等将由贵单位自行承担。</div>\
				<div style="padding: 10px 0 20px;">本公司提示贵单位：本公司将根据贵单位的测评结果评估贵单位的风险承受能力等级，该风险承受能力等级将影响贵单位购买产品或接受服务的范围。<span style="font-weight:bold;">贵单位应当如实提供测评相关信息，并对所提供信息的真实性、准确性、完整性负责，否则贵单位应当自行承担相应法律责任，同时本公司有权拒绝向贵单位提供服务或者销售产品。</span>贵单位对风险测评问卷某一问题的具体回答不构成本公司向贵单位提供服务或销售产品时风险评估的唯一依据，也不构成贵单位与本公司就本公司向贵单位提供服务或销售产品之间达成任何约定或协议安排。</div>\
				<div style="padding: 10px 0 20px;">本公司建议：当贵单位的各项状况发生重大变化时，需对贵单位所投资的产品及时进行重新审视，以确保贵单位的投资决定与贵单位可承受的投资风险程度等实际情况一致。</div>\
				<div style="padding: 10px 0 20px;">本公司在此承诺，对于贵单位在本问卷中所提供的一切信息，本公司将严格按照法律法规要求承担保密义务。除法律法规规定的有权机关依法定程序进行查询以外，本公司保证不会将涉及贵单位的任何信息提供、泄露给任何第三方，或者将相关信息用于违法、不当用途。</div>\
			</div>';
			break;

		}
	

		var quesCode = '';
		
		console.log('[根据配置隐藏项，过滤题目]');

		var needHidden = settings.needHiddenQues;
		var needHiddenArr = needHidden.split('|');
		
		var quesList = questions;
		quesList.forEach(function(element) {
			if(needHiddenArr.indexOf(element.question_id)==-1){
				newQuesList.push(element);
			}
		});

		$('.question').hide().html(quesDesc).slideDown(function(){
			bindEvent();
		});
		$('#footer').slideDown();
		num = newQuesList.length;
		console.log(currentIndex,num,newQuesList);
		return;
		$('.question').hide().html(quesDesc+quesCode).slideDown(function(){
			$('.opera a').unbind("click");
			bindEvent();
		});
		

	}
	function unSelectResetForceConflict(question){
		console.log('开始处理强制互斥匹配项');

		console.log(question);
		var question_id = question.question_id;

		var quesIdConflictConfigItems = quesConflictConfig.quesIdConflictConfigItems;
		console.log(quesIdConflictConfigItems);
		quesIdConflictConfigItems.forEach(function(item){
			if(item.quesId == question_id){
				var quesIdPairItems = item.quesIdPairItems;
				quesIdPairItems.forEach(function(it){

					var relationship = it.relationship;
					var matchedQuesId = it.matchedQuesId;
					var matchedQues = getQuesById(matchedQuesId);
					var matchedOptIds = it.matchedOptIds;
					if(relationship == 2){
						console.log(matchedOptIds);
						var options = matchedQues.question_options;

						options.forEach(function(op){
							console.log(op);
							op.disabled = false;
						})
					}

				})
				
			}
		})
		console.log(formatedQuestions);
	}
	function handleForceConflict(question){
		console.log('开始处理强制互斥匹配项');

		console.log(question);
		var question_id = question.question_id;

		var quesIdConflictConfigItems = quesConflictConfig.quesIdConflictConfigItems;
		console.log(quesIdConflictConfigItems);
		quesIdConflictConfigItems.forEach(function(item){
			if(item.quesId == question_id){
				var quesIdPairItems = item.quesIdPairItems;
				quesIdPairItems.forEach(function(it){

					var relationship = it.relationship;
					var matchedQuesId = it.matchedQuesId;
					var matchedQues = getQuesById(matchedQuesId);
					var matchedOptIds = it.matchedOptIds;
					if(relationship == 2){
						console.log(matchedOptIds);
						var options = matchedQues.question_options;

						options.forEach(function(op){
							console.log(op);
							if(matchedOptIds.indexOf(op.option_id) != -1){
								op.disabled = true;
							}else{
								op.disabled = false;
							}
						})
					}

				})
				
			}
		})
		console.log(formatedQuestions);
	}
	function checkMatchedNum(question){
		console.log('开始检查是否非强制互斥');
		console.log(question);
		var num = 0;
		var matched1 = false;
		var matched2 = false;

		var quesId = question.question_id;
		var quesIdItems = quesConflictConfig.quesIdItems;
        var quesIdConflictConfigItems  = quesConflictConfig.quesIdConflictConfigItems;

		var quesIndexMap = quesConflictConfig.quesIndexMap;
		var optionIndexMap = quesConflictConfig.optionIndexMap;

		var idx = quesIdItems.indexOf(quesId);
		var quesConf = quesIdConflictConfigItems[idx];
		console.log(quesConf);

		var quesIdPairItems = quesConf.quesIdPairItems;
		var quesIdPairItem1 = quesIdPairItems[0];
		var quesIdPairItem2 = quesIdPairItems[1];

		var matchedQuesId1 = quesIdPairItem1.matchedQuesId;
		var matchedQuesId2 = quesIdPairItem2.matchedQuesId;

		var optionIdPair1 = quesIdPairItem1.optionIdPair;
		var optionIdPair2 = quesIdPairItem2.optionIdPair;

		var ques1 = getQuesById(matchedQuesId1);
		var ques2 = getQuesById(matchedQuesId2);

		var select1 = ques1.select;
		var select2 = ques2.select;

		if(quesIdPairItem1.relationship == 1 && optionIdPair1[1].indexOf(select1) != -1){
			num++;
			matched1 = true;

		}

		if(quesIdPairItem2.relationship == 1 && optionIdPair2[1].indexOf(select2) != -1){
			num++;
			matched2 = true;
		}

		return {
			num: num,
			matched1: matched1,
			matched2: matched2
		};
		






		// quesId: '1027',
		// type: '1',
		// quesIdPairItems: [
		// 	{
		// 		relationship: '1',
		// 		matchedQuesId: '1028',
		// 		quesIdPair: [1027,1028],
		// 		optionIdPair: [['2','3','4','23','24','34','234','12','13','14','123','124','134','1234'],['1']],
		// 		tipMessage: '您的投资需求（第13题）可能与您重点参与的投资品种（第12题）不符，请确认勾选的信息准确无误。',
		// 		eventId: '1104805'
		// 	},{
		// 		relationship: '1',
		// 		matchedQuesId: '8020',
		// 		quesIdPair: [1027,8020],
		// 		optionIdPair: [['2','3','4','23','24','34','234','12','13','14','123','124','134','1234'],['1']],
		// 		tipMessage: '您能承受的最大投资损失（第15题）可能与您重点参与的投资品种（第12题）不符，请确认勾选的信息准确无误。',
		// 		eventId: '1104806'
		// 	}
		// ]

	}
	function checkForceConflict(question){
		console.log('开始检查是否强制互斥');
		console.log(question);
		var question_id = question.question_id;
		var forceQuesIdItems = quesConflictConfig.forceQuesIdItems;
		
		if(forceQuesIdItems.indexOf(question_id) == -1){
			return false;
		}
		
		var select = question.select;
		var quesIdConflictConfigItems = quesConflictConfig.quesIdConflictConfigItems;
		console.log(quesIdConflictConfigItems);
		var status = false;
		quesIdConflictConfigItems.forEach(function(item){
			if(item.quesId == question_id){
				var quesIdPairItems = item.quesIdPairItems;
				quesIdPairItems.forEach(function(it){

					var relationship = it.relationship;
					var optionIdPair = it.optionIdPair;
					if(relationship == 2 && optionIdPair[0].indexOf(select) != -1 ){
						console.log('强制互斥检查匹配成功');
						status = true;
					}

				})
			}
		})

		return status;
	}
	function checkConflict(question){
		console.log('开始检查是否非强制互斥匹配');
		console.log(question);
		var question_id = question.question_id;
		var quesIdItems = quesConflictConfig.quesIdItems;
		
		if(quesIdItems.indexOf(question_id) == -1){
			return false;
		}

		var select = question.select;
		var quesIdConflictConfigItems = quesConflictConfig.quesIdConflictConfigItems;
		console.log(quesIdConflictConfigItems);
		var status = false;
		quesIdConflictConfigItems.forEach(function(item){
			if(item.quesId == question_id){
				var quesIdPairItems = item.quesIdPairItems;
				quesIdPairItems.forEach(function(it){
					var matchedQuesId = it.matchedQuesId;
					
					var matchedques = getQuesById(matchedQuesId);
					var matchedselect = matchedques.select;

					var optionIdPair = it.optionIdPair;
					if(optionIdPair[0].indexOf(select) != -1 && optionIdPair[1].indexOf(matchedselect) != -1){
						console.log('互斥检查匹配成功');
						status = true;
					}

				})
			}
			
		})
		

		return status;
	}
    // 立即弹框，非互斥模式
	function handleMustConfilict(question){
		console.log('开始检查是否非强制互斥匹配');
		console.log(question);
		var that = this;

		var question_id = question.question_id;
		
		var select = question.select;
		var autoConflictItem = quesConflictConfig.autoConflictItem;
		console.log(autoConflictItem);
		var status = false;
		autoConflictItem.forEach(function(item){
			if(item.quesId == question_id && question_id =='888' && question.select == item.condition.matchedOptionId){
				var tipInfo = item.tipMessage;
						var eventId = item.eventId;
						var condition = item.condition;
						var maleAge = condition.maleAge;
						var femaleAge = condition.femaleAge;
						var matchedOptionId = condition.matchedOptionId;
						
						var currentYear = new Date().getFullYear();
						var currentMonth = new Date().getMonth()+1;
						var currentDay = new Date().getDate();
						if(currentMonth<10){
							currentMonth= '0'+ currentMonth
						}
						if(currentDay<10){
							currentDay= '0'+ currentDay
						}
						var currentDate = currentYear+''+currentMonth+''+currentDay;

						// 生日后一天才算大一岁
						var birthDate = Number(id_no.substring(6, 14)) + 1;

						var daysDiff = parseInt(currentDate) - birthDate;
						var isNotPass = false;
						if(gender=='女士' && daysDiff < Number(femaleAge)*10000){isNotPass = true}
						if(gender=='先生' && daysDiff < Number(maleAge)*10000){isNotPass = true}
						if(isNotPass){
							// 弹窗提示
							layerUtils.confirmCustom(tipInfo,'重新选择','确认无误',function(){ 
								console.log('点击重新选择按钮')
								// 非强制互斥逻辑，取消当前选择
								that.toRemoveSelect(question);
	
							},function(){
								console.log('点击确认无误按钮');
								var message = '题1选E，客户自主确认';
								PUBLCISELECT.Loggercollect({
									event_id: eventId,
									event_name: '风险测评互斥',
									message: message
								},function(oData){
									console.log(oData);
									if(oData['GRID0']=='Success'){
										//留痕成功，不做任何处理
										auto = true;
										nextOneItem();
									}else{
										// 留痕失败逻辑
										layerUtils.alert('很抱歉，受理失败了','确定',function(){
											console.log('取消当前选中项');
											that.toRemoveSelect(question);
	
										});
									}
									
								}); 
							})
						}else{
							// 满足条件，下一步
							auto = true;
							nextOneItem();
						}

			}

		})
	
	}

	function toNext(question,quesNo,quesCode){
		var question_type = question.question_type;
		var conflictStatus = checkConflict(question);

		// 选择了立即弹框
		if(question.question_id =='888' && question.select =='6' ){
			handleMustConfilict(question)
		}else if(conflictStatus){
			auto = false;
			handleConflict(question,quesNo,quesCode);
		}else{
			//进行强制互斥检查
			var forceConflictStatus = checkForceConflict(question);
			if(forceConflictStatus){
				handleForceConflict(question);
			}else{
				unSelectResetForceConflict(question);
			}
			//单选会自动跳转到下一题
			console.log(num - currentIndex);
			auto = false;
			if(num - currentIndex > 1 && question_type == 1){
				auto = true;
				setTimeout(function() {
					nextOneItem();
				}, 300);
			}
		}

		console.log('当前选择结果');
		console.log(formatedQuestions);
	}

	function bindEvent(){
		console.log('bindEvent');
		var that = this;
		$('.answer').each(function(index,obj){
			console.log(index);
			// console.log(obj);
			$(obj).on('click',function(){
				/**
				 * 判断逻辑，首先根据索引定位到具体的Ques、Option，然后根据业务逻辑进行判断
				*/
				if(!clickable){
					return;
				}
				clickable = false;

				//重置可点击状态
				setTimeout(function() {
					clickable = true;
				}, 1000);

				var selectedQuesArr = [];
				var selectedQuesNoArr = [];
				
				var idx = $(this).parents('.list').attr("data-index");
				var quesNo = $(this).parents('.list').attr("data-quesno");
				var quesCode = $(this).parents('.list').find('.answer').index(this)+1;

				var question = formatedQuestions[Number(idx)];
				var option;
			
				
				var question_type = question.question_type;
				var question_options = question.question_options;
				
				quesCode = quesCode+'';
				
				switch(question_type){
					case '1':
						question_options.forEach(function(option){
							if(option.option_id == quesCode){
								option.selected = true;
							}else{
								option.selected = false;
							}
						})
						break;
					case '2':
						clickable = true;
						question_options.forEach(function(option){
							if(option.option_id == quesCode){
								option.selected = !option.selected;
							}
						})
						break;
					default: 
					console.log('未匹配题型:'+question_type);
				}

				var select = [];
				question_options.forEach(option=>{
					if(option.selected){
						select.push(option.option_id);
					}
				})
				question.select = select.sort().join('');
				question.question_options = question_options;

				formatedQuestions[idx] = question;

				if($(this).parents('.list').attr("data-questype")==2){
					//多选题
					if($(this).hasClass(ANSWERCUR)){
						$(this).removeClass(ANSWERCUR)
					}else{
						$(this).addClass(ANSWERCUR).parents('.list').removeClass(LISTCUR);
					}
					
				}else{
					//单选题
					$(this).addClass(ANSWERCUR).siblings().removeClass(ANSWERCUR).parents('.list').removeClass(LISTCUR);
				}
				

				// 对Q12、Q13、Q15存在两对非强制互斥情况的数据进行初始化
				var quesIdPairItems = [];
				var quesIdConflictConfigItems = quesConflictConfig.quesIdConflictConfigItems;
				for(var i = 0; i < quesIdConflictConfigItems.length; i++){
					if(question.question_id == quesIdConflictConfigItems[i]['quesId']){
						quesIdPairItems = quesIdConflictConfigItems[i]['quesIdPairItems'];
						break;
					}
				}
				if(quesIdPairItems.length == 2){
					quesIdPairItems.forEach(function(element) {
						if(typeof element.done !== 'undefined'){
							delete element.done;
						}
					});
				}

				console.log('当前题目');
				console.log(question);
				if(question.select == ''){
					console.log('当前题目未勾选');
					if(quesConflictConfig.forceQuesIdItems.indexOf(question.question_id) != -1){
						unSelectResetForceConflict(question);
					}
					
					return;
				}
				
				// 只对个人户做提示
				if(organ_flag == 0 && currentIndex == 14 && select == '1'){
					// 弹窗提示
					var eventId = '1105710';
					var tipInfo = '尊敬的投资者，该题选择此选项，您的风险承受能力等级将被认定为C1（最低类别）。我司普通股票交易服务的风险等级为R3（中风险），为保护您的合法权益，我司将不会给您提供普通股票交易服务，您确定该选项反映您的真实情况吗？';
					layerUtils.confirmCustom(tipInfo,'确认','去修改',function(){ 
						console.log('点击确认按钮');
						var message = '确认';
						PUBLCISELECT.Loggercollect({
							event_id: eventId,
							event_name: '确认留痕埋点',
							message: message
						},function(oData){
							console.log(oData);
							if(oData['GRID0']=='Success'){
								//留痕成功，不做任何处理
								toNext(question,quesNo,quesCode);
							}else{
								// 留痕失败逻辑
								layerUtils.alert('很抱歉，受理失败了','确定',function(){
									console.log('取消当前选中项');
								

								});
							}
							
						}); 
						
						

					},function(){
						console.log('点击去修改按钮');
						that.toRemoveSelect(question);
					})

					return;
				}else{
					toNext(question,quesNo,quesCode);
				}
				

				


				
				
			})
		})
		
		$('.opera a').on('click',function(){
			console.log('底部按钮触发事件');
			console.log('step===',step);
            try{
                PUBLCISELECT.Loggercollect({event_id:settings.loggerId['risk_submit'],logtype:'risk_submit'}); //行为采集
			}catch(e){}
			
			if(step == 1){
				// 客户最后一条风测版本与当前版本相同
				if(last_questionnaire_version == current_questionnaire_version){
					let tipInfo = '您已做过风险测评问卷，您可以点击下方快速测评按钮，在原测评问卷基础上修改答案；您也可以重新进行风险测评。'
					layerUtils.confirmCustom(tipInfo,'重新测评','快速测评',function(){
						// 重新测评
						nextOneItem();
						step = 2;
						$('.opera a').html('下一题');
					},function(){
						// 快速测评  回显答案
						console.log('user_answer_list',user_answer_list);
						console.log('newQuesList',newQuesList);
						let multipleArr = ['1027']
						let mapNewQuesList = newQuesList.map(item => {
							user_answer_list.forEach(Uitem => {
								if(Uitem.question_id == item.question_id){ // 选择到对应的题目
									if(multipleArr.indexOf(Uitem.question_id) > -1){ // 多选题
										// 该题的select 和 选项的selected
										let choiceArr = Uitem.answer.map(Aitem => Aitem.choice)
										item.select = choiceArr.join('')
										item.question_options.forEach(Mitem => {
											Uitem.answer.forEach(i => {
												if(i.choice == Mitem.option_id){
													Mitem.selected = true
												}
											})
										})
									} else{ // 单选题
										item.select = Uitem.answer[0].choice
										item.question_options.forEach(QOitem => { //选中对应的选择
											if(QOitem.option_id == Uitem.answer[0].choice){
												QOitem.selected = true
											}
										})
									}
								}
							})
							return item
						})
						newQuesList = mapNewQuesList
						console.log('回显的数组',newQuesList);
						nextOneItem();
						step = 2;
						$('.opera a').html('下一题');
					})
					return
				}
				nextOneItem();
				step = 2;
				$('.opera a').html('下一题');
				return;
			}
			if(step == 2){
				console.log('nextOneItem');
				var ques = getQuesByIdx(currentIndex);
				if(ques.select == ''){
					layerUtils.notice('请先回答当前题目');
					return;
				}
				if(!auto){
					nextOneItem();
				}
				return;
			}
			var ques = getQuesByIdx(currentIndex);
			if(ques.select == ''){
				layerUtils.notice('请先回答当前题目');
				return;
			}

			var osInfo =getOsInfo();
			var arrNotAnser = [];
			var strPanperAnser = {
				user_account :client_id,
				account_type: 4, // 2 普通 3 信用 4 客户编号
				opstation:op_station,
				origin:"CHANNEL:"+app_id+";DEVICE:"+osInfo['DEVICE']+";SYSTEM:"+osInfo['SYSTEM'],
				user_answers:[]
			};

			var needHidden = settings.needHiddenQues;
			var needHiddenArr = needHidden.split('|');

			//组装用户选择数据
			formatedQuestions.forEach(function(item){
				console.log(item);
				var question_id = item.question_id;
				var select = String(item.select);
				
				var selectitems = select.split('');
				console.log(selectitems);
				var answer=[];
				selectitems.forEach(function(one){
					var answerOption = {
						choice : one
					};
					answer.push(answerOption);
				})
				
				var user_answer = {
					question_id: question_id,
					answer: answer
				};
				if(needHiddenArr.indexOf(question_id) == -1){
					strPanperAnser.user_answers.push(user_answer);
				}

			})
			
			waiting.show();
            $.getData(SENDTO60, {
                UniqueKey: 'QCQPRESAVE',
                client_id:"($usercode)", // 客户号  Y  client_id 和 prodta_no 必须传一个
                paper_type:PAPERTYPE, //试卷类别 Y
                branch_no:'($khbranch)',
                questionnaire_id: questionnaire_id,
                user_answers:JSON.stringify(strPanperAnser), //试卷的答案 Y 格式：801&1|802&2^3|  			801，802表示题号1,2,3　是答案
            }, function (oReturn) {
                if (oReturn.ERRORNO != 0) {
                    waiting.hide();
                    layerUtils.alert($.replaceString(BASETIP, { error_no: oReturn.ERRORNO, error_info: oReturn.ERRORMESSAGE }));
                    return false;
                }
                var oData = oReturn.GRID0;//errno = oData.error_no;
                if (oData.code != 0 ) {
                    waiting.hide();
                    layerUtils.alert($.replaceString(BASETIP, { error_no: oData.code, error_info: oData.message }));
                    return false;
                }

                //试算的结果
                var preSaveResult= oData.result;
                //提交柜台的参数
                var userParams = {
                    questionnaire_id: questionnaire_id,
                    paper_type:PAPERTYPE, //试卷类别 Y
                    user_answers:JSON.stringify(strPanperAnser), //试卷的答案 Y 格式：801&1|802&2^3|  			801，802表示题号1,2,3　是答案
                    organ_flag:organ_flag,
					questionnaire_version:current_questionnaire_version,// 当前风测版本
                };

                //将传来的backUrl存储至session中,由于backURl为调用方传递,避免不确定的兼容问题.
                var saveData={};
                saveData.backUrl = $.getUrlParameter('backUrl') || '';
                saveData.account_type = $.getUrlParameter('account_type');
                saveData.app_id = $.getUrlParameter('app_id');
                saveData.fromlogin = $.getUrlParameter('from');
                saveData.preSaveResult = preSaveResult;
                saveData.userParams = userParams;
				var t = new Date().getTime();

                TZT.TOOLS.saveMapMesgLong(saveData,function(){
                    Controller.pageInit('riskresult.html?is_sa='+$.getUrlParameter('is_sa') + '&t=' + t);
                })
            });

		});
	}

	function getOsInfo(){
		function detectOS() {
			var sUserAgent = navigator.userAgent;
			var isWin = (navigator.platform == "Win32") || (navigator.platform == "Windows");
			var isMac = (navigator.platform == "Mac68K") || (navigator.platform == "MacPPC") || (navigator.platform == "Macintosh") || (navigator.platform == "MacIntel");
			if (isMac) return "Mac";
			var isUnix = (navigator.platform == "X11") && !isWin && !isMac;
			if (isUnix) return "Unix";
			var isLinux = (String(navigator.platform).indexOf("Linux") > -1);
			if (isLinux) return "Linux";
			if (isWin) {
				var isWin2K = sUserAgent.indexOf("Windows NT 5.0") > -1 || sUserAgent.indexOf("Windows 2000") > -1;
				if (isWin2K) return "Win2000";
				var isWinXP = sUserAgent.indexOf("Windows NT 5.1") > -1 || sUserAgent.indexOf("Windows XP") > -1;
				if (isWinXP) return "WinXP";
				var isWin2003 = sUserAgent.indexOf("Windows NT 5.2") > -1 || sUserAgent.indexOf("Windows 2003") > -1;
				if (isWin2003) return "Win2003";
				var isWinVista= sUserAgent.indexOf("Windows NT 6.0") > -1 || sUserAgent.indexOf("Windows Vista") > -1;
				if (isWinVista) return "WinVista";
				var isWin7 = sUserAgent.indexOf("Windows NT 6.1") > -1 || sUserAgent.indexOf("Windows 7") > -1;
				if (isWin7) return "Win7";
			}
			return "other";
		}

		if(comm.BASEVAR.ISANDROID){
			return {'SYSTEM':'android','DEVICE':'mobile'};
		}else if(comm.BASEVAR.ISIOS){
			return {'SYSTEM':'ios','DEVICE':'mobile'};
		}else{
			return {'SYSTEM':detectOS(),'DEVICE':'pc'};
		}
	}

	//查询某个业务模块是否有公告
	function queryNotice(oParms,fnCallbak){
		$.getData(SENDTO60,$.extend({
			UniqueKey:'ANNC_1004',
			type:'',
			subtype:''
		},oParms), function (oReturn) {

			if (oReturn.ERRORNO != 0) {
				waiting.hide();
				layerUtils.alert($.replaceString(BASETIP, { error_no: oReturn.ERRORNO, error_info: oReturn.ERRORMESSAGE }));
				return false;
			}
			var oData = oReturn.GRID0;//errno = oData.error_no;
			if (oData.code != 0 ) {
				waiting.hide();
				layerUtils.alert($.replaceString(BASETIP, { error_no: oData.code, error_info: oData.message }));
				return false;
			}
			NUM = oData.code==undefined? oData.error_no:oData.code;
			if(NUM == '0'){
				if(oData.result && oData.result.announceList.length >0){
					var serverTime=oData.result.serverTime;  //服务器时间
					var announceList=oData.result.announceList;
					var notice = 0;
					for(var i=0;i<announceList.length;i++){
						var validFrom=new Date(announceList[i].validFrom.replace(/-/g,"/")).getTime();
						var validTo=new Date(announceList[i].validTo.replace(/-/g,"/")).getTime();
						if(serverTime<=validTo && serverTime>=validFrom && announceList[i].status == 0){
							//展示公告
							var title=announceList[i].title;
							var content=announceList[i].content;
							$(".notice_title").html(title);
							$(".notice_content").html(content);

							$('.content').hide();
							$('#footer').hide();
							$('.notice').show();
							$('body').show();

						}else{
							//非公告时间
							notice ++;
						}
					}
					//所有的公告都不在有效期，视为无公告
					if(notice == announceList.length){
						fnCallbak(true);
					}
				}else{
					fnCallbak(true);
				}
			}else{
				fnCallbak(true);
			}
		})
	}
</script>
</body>
</html>
