<!DOCTYPE html>
<html style="">
<head>
<title>投资风险测评</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<meta name="viewport" content="initial-scale=1.0,maximum-scale=1.0,minimum-scale=1.0,user-scalable=no,target-densitydpi = medium-dpi">
<meta name="format-detection" content="telephone=no">
<meta name="apple-touch-fullscreen" content="YES">
<meta name="apple-mobile-web-app-capable" content="yes">
<meta name="apple-mobile-web-app-status-bar-style" content="black">
<link href="../common/css/base-min.css" rel="stylesheet" type="text/css" media="all"/>
<script src="../js/config.js"></script><script src="../common/js/jquery-1.8.3.min.js"></script> <script src="../../common/scripts/common-full.js"></script> <script src="../../common/scripts/jssdk.js"></script><script src="../../common/scripts/bridge/controller.js"></script> <script src="../../common/scripts/bridge/app.js"></script>
<script src="../common/js/common-min.js"></script>
<script src="../js/publicSelect.js"></script>
<link href="../css/account.css" rel="stylesheet" type="text/css" media="all" />
<link href="riskresult.css" rel="stylesheet" type="text/css" media="all" />
<script src="../js/vconsole.min.js"></script>
<!-- <script src="https://cdn.bootcss.com/vConsole/3.2.0/vconsole.min.js"></script> -->
<script>
    var vConsole = new VConsole();
    if(localStorage.getItem('isSdTest') == null || localStorage.getItem('isSdTest') == '0') {
        localStorage.setItem('isSdTest', '1');
        localStorage.setItem('isSdUatTest', '1');
    }
</script>
</head>
<body>
<div class="mainBox" style="display: none;">
    <p class="riskTip">
        
    </p>
    <div class="riskResult-container">
        <div class="riskAudi">loading...</div>
        <div id="endDate" class="endDate"></div>
        <div class="riskPeriod">本次风险承受能力评定结果有效期为2年</div>
        <div id="compareBox" class="compareBox"></div>
        <div class="riskRepeat"><a href="javascript:;"> 重新测评</a></div>
    </div>

    <!--<div class="riskResult">-->
        <!--<p class="riskHalf">-->
            <!--<span class="icon"></span>-->
            <!--<span class="resultTip">loading...</span>-->
        <!--</p>-->
        <!--<ul class="riskLevel">-->
            <!--<li class="riskAudi">loading...</li>-->
            <!--<li class="riskRepeat"><a href="javascript:;"> 重新测评</a></li>-->
        <!--</ul>-->
    <!--</div>-->


    <div style="margin: 5px 0;" id="deal">
        <!--<div class="risk_deal_item" style="text-align: justify;">-->
            <!--<div class="risk_deal_item_up" style="color: #444444;font-size: 14px;margin: 10px;">-->
                <!--根据适当性新规要求，稳健型仅适合购买或享受中风险级别的产品或服务，在您购买股票（退市整理期、风险警示、港股通）、基金（？）、债券（AA-级）等中高及以上风险等级的交易品种时，我司将会对您进行风险提示和交易限制，请您再次确认您的风险承受能力等级符合您的基本情况和投资标的。风险等级说明可详细参见-->
                <!--<span id="levelTip" class="risk_deal_item_tip" style="color: #2a8ee3;">《风险等级对照表》</span>-->
                <!--。-->
            <!--</div>-->
            <!--<div class="risk_deal_item_down" style="font-size: 11px;color: #666666;margin: 10px;padding-top: 10px;border-top: solid #eceaea 1px;">-->
                <!--郑重提醒您，本次风险承受能力评定结果有效期为2年，有效期满，请您务必根据我司的提示及时完成风险承受能力评定的更新和确认。我司向您销售的产品或提供的服务将以您的风险承受能力等级和投资品种、期限为基础。若您的信息发生任何重大变化，应当及时重新测评。投资结果仅供参考，不构成请您如实完成风险承受能力的自我测评，结合自身投资行为，作出审慎的投资判断。-->
            <!--</div>-->
        <!--</div>-->
    </div>

    
    <!--<div class="signPact"><p class="pact" onclick="$(this).toggleClass('pactCheck');signStatus++;"><span class="check"></span>我已仔细阅读并同意签署</p></div>-->
    <div id="limitTip" class="limitTip" style="display: none;">
        <p class="limitTipTitle">温馨提示：</p>
        <p id="levelTip" class="limitTipContent" style="display: none;">1. 请仔细阅读<span style="color:#249CFD;">《风险等级对照表》</span>中产品或服务风险目录及适配规定。</p>
        <p class="limitTipContent">2. 为确保投资者适当性测评结果的有效性，投资者在参与适当性测评时，<span style="color:#F0392F;">一个交易日内测评次数不超过3次，任意连续60个自然日内累计测评次数不超过5次。</span>请您根据自身实际情况如实填写，审慎测评。</p>
    </div>
    <!--<p class="levelTip">查看适应您风险承受能力的理财产品分类<i class="arrDouble"></i></p>-->
    <div class="footer">
        <div class="btn-wrapper">
        <a class="btnNext" href="javascript:;" style="display: none;" >确定</a>
        </div>
    </div>
</div>

<div class="fail-containter" style="display: none;">
    <div class="fail-ret">
        <img src="../img/fail_ret.png" />
    </div>
    <div class="fail-word">亲，宝宝未找到您的风险测评结果</div>
    <div style="margin: 0 6%;">
        风险测评结果缺失将影响到您的正常交易，请立即 <span class="riskRepeat"><a href="javascript:;"> 重新测评</a></span>
    </div>
</div>

<div class="notice" style="display: none;font-size: 14px;">
    <div class="topimg" style="width: 270px;margin: 0 auto;padding-top: 40px;">
        <img style="width: 100%;" src="notice_top.png">
    </div>
    <div class="notice_title" style="text-align: center;line-height: 22px;padding-top: 10px;font-size: 20px;"></div>
    <div class="notice_content" style="margin: 0 10%;padding: 10px 0;line-height: 22px;color: #666666;text-align: center;"></div>
</div>

<script src="../tol/service.js"></script>
<script src="../tol/user.js"></script>
<script src="../tol/utils.js"></script>
<script type="text/javascript">
var objPact = {},signStatus = 1,g_group_nos="";
var fromlogin;
var account_type;
var backUrl;
var app_id;
var preSaveResult;
var userParams;
var opStation;
var isAutoCheck;
var persent_num;// 当天问卷记录数
var history_num;// 历史问卷记录数
var formatedquestionnaire = [];// 回访问卷
var sbumitQuestionnaireUserAnswer = []
var strPanperAnser = {}
var isOrganClient = null
var  USERMESG = {};
var compareResult = '';
var client_id = ''

window.GoBackOnLoad=function() {
    pageInit();
};

Controller.onAppear(function(){
    pageInit();
});

$(function(){
    pageInit();

    try{
        //行为采集
        PUBLCISELECT.LoggercollectUnlogin({
            event_id:1105711,
            event_name:'risk-result',
            message: window.location.href 
        });
    }catch(e){}
});

function pageInit(){
    var clientInverstor
    var kind = $.getUrlParameter('kind');
    isAutoCheck = $.getUrlParameter('is_auto_check');
    /**
     *  接入(桥接)逻辑处理
     */
    App.appInit(function(result){
        if(result&&result.code === 0){
            if(comm.TOOLS.getCookie('app_id') == 'yjb3.0') {
                // 客户端版本校验，每个业务可能限制不同的客户端版本
                try {
                    jssdk.ready(() => {
                        jssdk.getAppVersion(v => {
                            // 如果groupName在sdBothSwitch配置中且版本
                            if (!((settings.sdBothSwitch || []).includes('risk') && comm.TOOLS.compareVersions.compare(v, '8.01.001', '<'))) {
                                Controller.basePageInit(location.href.split('yjbwebmoc/moc/')[0]+'yjbwebmoc/moc/web/moc-pro/build/goGroupView.html?groupName=riskForWeb&' + location.search.split('?')[1]);
                            } else {
                                $('body').show();
                            }
                        });
                    });
                } catch (_) {
                    Controller.basePageInit(location.href.split('yjbwebmoc/moc/')[0]+'yjbwebmoc/moc/web/moc-pro/build/goGroupView.html?groupName=riskForWeb&' + location.search.split('?')[1]);				
                }
            } else {
                Controller.basePageInit(location.href.split('yjbwebmoc/moc/')[0]+'yjbwebmoc/moc/web/moc-pro/build/goGroupView.html?groupName=riskForWeb&' + location.search.split('?')[1]);
            }
			client_id = result.result.clientId;
            opStation = result.result.opStation;
            //判断公告
            queryNotice({type:1002,subtype:10020018},function(status){
                if(status){
                    if ((settings.sdSwitch || []).includes('risk')) {
                        if(comm.TOOLS.getCookie('app_id') == 'yjb3.0') {
                            // 客户端版本校验，每个业务可能限制不同的客户端版本
                            try {
                                jssdk.ready(() => {
                                    jssdk.getAppVersion(v => {
                                        // 如果groupName在sdBothSwitch配置中且版本
                                        if (!((settings.sdBothSwitch || []).includes('risk') && comm.TOOLS.compareVersions.compare(v, '8.01.001', '<'))) {
                                            Controller.basePageInit(location.href.split('yjbwebmoc/moc/')[0]+'yjbwebmoc/moc/web/moc-pro/build/goGroupView.html?groupName=riskForWeb&' + location.search.split('?')[1]);
                                        } else {
                                            $('body').show();
                                        }
                                    });
                                });
                            } catch (_) {
                                Controller.basePageInit(location.href.split('yjbwebmoc/moc/')[0]+'yjbwebmoc/moc/web/moc-pro/build/goGroupView.html?groupName=riskForWeb&' + location.search.split('?')[1]);				
                            }
                        } else {
                            Controller.basePageInit(location.href.split('yjbwebmoc/moc/')[0]+'yjbwebmoc/moc/web/moc-pro/build/goGroupView.html?groupName=riskForWeb&' + location.search.split('?')[1]);
                        }
                    }
                    PUBLCISELECT.getUserInfo({fundAccount: '($fund_account)', clientId: client_id},function(oReturn){
                        console.log('oReturn',oReturn);
                        if(oReturn.is_organ_client == 0){ //机构户
                            isOrganClient = 1
                        }
                    })
                    $('.mainBox').show();
                        
                    service.getClientInvestor({client_id:user.usercode,fund_account:user.fund_account,password:user.password,branch_no:"($khbranch)"},function(data){
                        console.log('data',data);
                        if(data.code == 0){
                            if(data.result.prof_flag == 1){
                                // 专业投资者
                                $('.riskPeriod').hide()
                                $('.riskResult-container').addClass('prof');
                                $('.riskAudi').html('专业投资者无需测评');
                                $('.riskAudi').css('text-align', 'center');
                                $('.riskAudi').css('padding', '50px 0');
                                $('.riskRepeat').css('margin-bottom', '0');
                                $('.riskRepeat a').html('返回')
                                $('.riskRepeat a').on('click',function(){
                                    // console.log('返回上一页')
                                    Controller.goBackPage()
                                })
                            }else{
                                if(kind == 1){
                                    /**
                                     * 问卷前置结果展示页
                                     */
                                    $.getData(SENDTO60, {
                                        UniqueKey: 'CWRQ',
                                        client_id:'($usercode)', // 客户号  Y  client_id 和 prodta_no 必须传一个
                                        password: '($password)'
                                    }, function (oReturn) {
                                        if (oReturn.ERRORNO != 0) {
                                            waiting.hide();
                                            layerUtils.alert($.replaceString(BASETIP, { error_no: oReturn.ERRORNO, error_info: oReturn.ERRORMESSAGE }));
                                            return false;
                                        }
                                        var oData = oReturn.GRID0;//errno = oData.error_no;
                                        if (oData.code != 0 ) {
                                            waiting.hide();
                                            layerUtils.alert($.replaceString(BASETIP, { error_no: oData.code, error_info: oData.message }));
                                            return false;
                                        }
                                        saveData=oData.result; //得到的风险测评结果

                                        var USERMESG = {};
                                        TZT.TOOLS.readMapMesg(['backUrl','account_type','app_id','fromlogin'],function(oRead){
                                            USERMESG = oRead;
                                            //获取内存数据
                                            fromlogin = USERMESG.fromlogin;
                                            account_type = USERMESG.account_type;
                                            backUrl = USERMESG.backUrl;
                                            app_id = USERMESG.app_id;
                                            /**
                                             * 不适合在session清除,会造成从其他页面跳回来 or 页面刷新 导致参数丢失问题(如微信),正确的做法是url get参数的方式,此次先不处理这个问题.
                                             */
                                            //TZT.TOOLS.saveMapMesgLong({backUrl:'',fromlogin:'',preSaveResult:'',userParams:''},function(){});
                                            //TZT.TOOLS.readMapMesg(['backUrl','account_type','app_id','fromlogin','preSaveResult','userParams'],function(oRead){});

                                            var name = saveData.risk_level_name;
                                            var level = saveData.corp_risk_level;
                                            //name="保守型（最低类别）（低风险承受）";
                                            var index = name.lastIndexOf("（");
                                            //var riskLevel=saveData.corp_risk_level;

                                            //适配3.0页面返回按钮--事件
                                            if(app_id == 'yjb3.0' && fromlogin=='login'){
                                                window.doneSuitability1 = function(){
                                                    Controller.doneSuitability(account_type,0);
                                                }
                                                Controller.setTitle({
                                                    navBarBgColor: "#FFFFFF",
                                                    titleColor: "#333333",
                                                    rightTextColor: "#333333",
                                                    leftText: '/common/imgs/left_arrow_grey.png',
                                                    'fullScreen':0,
                                                    'leftType':98,
                                                    'leftJS':'window.doneSuitability1()',
                                                    'rightType':0,
                                                });
                                                jssdk.ready(function(){
                                                    jssdk.statusBar({
                                                        style: 0,
                                                    });
                                                })

                                            }

                                            var riskLevel = name.slice(0,index);
                                            console.log(riskLevel);

                                            $('.resultTip').html(name.slice(0,index));
                                            $('.riskResult-container').addClass('bg_risk_'+riskLevel);

                                            if(level == '99'){
                                                $('.riskAudi').html(name);
                                            }else{
                                                var riskName = name.slice(index+1,-1);
                                                if(riskName.indexOf('风险承受') == -1){
                                                    riskName = riskName + '风险承受';
                                                }
                                                $('.riskAudi').html(riskName);
                                            }

                                            //前置展示页面
                                            $("#deal").hide();
                                            $("#levelTip").show();
                                            $("#limitTip").show();
                                            /**
                                             * 添加点击事件
                                             */
                                            $("#levelTip").unbind("click");
                                            $("#levelTip").on('click',function(e){
                                                try{
                                                    PUBLCISELECT.Loggercollect({event_id:settings.loggerId['risk_leveltip'],logtype:'risk_leveltip'}); //行为采集
                                                }catch(e){}
                                                Controller.newPageInit('riskTip.html');
                                            });

                                            if(saveData.risklevel_status==-1){
                                                //旧试卷情况
                                                $("#endDate").html('非最新版本，请重新测评');
                                            }
                                            else if(saveData.risklevel_status==3){
                                                //未做情况
                                                $(".mainBox").hide();
                                                $(".fail-containter").show();
                                            }
                                            else {
                                                if(saveData.corp_end_date==undefined||saveData.corp_end_date=='0'||saveData.corp_end_date==''){
                                                    saveData.corp_end_date="";
                                                    $("#endDate").html('非最新版本，请重新测评');
                                                }else{
                                                    saveData.corp_end_date=saveData.corp_end_date.substr(0,4)+'-'+saveData.corp_end_date.substr(4,2)+'-'+saveData.corp_end_date.substr(6,2);
                                                    $("#endDate").html('有效期至：'+saveData.corp_end_date);
                                                }
                                            }

                                            //绑定事件
                                            bindEvent();
                                        })
                                    });
                                }else{  //来自试算结果
                                    // 获取当日测评次数及近60天内测评次数
                                    $.getData(SENDTO60, {
                                        UniqueKey: 'QCWCQEQ',
                                        branch_no:'($khbranch)',
                                        op_entrust_way:7,
                                        op_station: "($op_station)",
                                        client_id:'($usercode)',
                                        password: '($password)',
                                        password_type:'2',
                                        paper_type:'1',
                                        forth_days:60,
                                    }, function (oReturn) {
                                        // oReturn = {
                                        //     ERRORNO:0,
                                        //     GRID0:{
                                        //         code:0,
                                        //         message:null,
                                        //         result:{
                                        //             persent_num:'1',
                                        //             present_list:'',
                                        //             history_num:'2',
                                        //             history_list:'',
                                        //         }
                                        //     }
                                        // }
                                        if (oReturn.ERRORNO != 0) {
                                            waiting.hide();
                                            layerUtils.alert($.replaceString(BASETIP, { error_no: oReturn.ERRORNO, error_info: oReturn.ERRORMESSAGE }));
                                            return false;
                                        }
                                        var oData = oReturn.GRID0;
                                        if (oData.code != 0 ) {
                                            waiting.hide();
                                            layerUtils.alert($.replaceString(BASETIP, { error_no: oData.code, error_info: oData.message }));
                                            return false;
                                        }
                                        persent_num = Number(oData.result.persent_num)
                                        history_num = Number(oData.result.history_num)
                                        console.log('客户问卷记录数  当天  历史',persent_num,history_num);
                                    });
                                    
									//获取回访问卷
									$.getData(SENDTO60, {
										UniqueKey: 'QCQ',
										questionnaire_id:'20230522001'
									}, function (oReturn) {
                                        if (oReturn.ERRORNO != 0) {
											waiting.hide();
											layerUtils.alert($.replaceString(BASETIP, { error_no: oReturn.ERRORNO, error_info: oReturn.ERRORMESSAGE }));
											return false;
										}
										var oData = oReturn.GRID0;
										console.log(oData);
										if (oData.code != 0 ) {
											waiting.hide();
											layerUtils.alert($.replaceString(BASETIP, { error_no: oData.code, error_info: oData.message }));
											return false;
										}
                                        // oData.result = {
                                        //     questionnaire_id:'20230522001',
                                        //     questionnaire_version:'1.1',
                                        //     questionnaire_name:'测试问卷',
                                        //     questionnaire_content:[
                                        //         {
                                        //             is_additional:'0',
                                        //             question_id:'1',
                                        //             question_title:'尊敬的投资者，近期您已多次进行风险承受能力测评，根据投资者适当性管理要求，如您确认需重新进行风险承受能力评估，烦请选择原因：',
                                        //             question_type:'2',
                                        //             question_options:[
                                        //                 {option_id:'1',option_content:'前一次评估填报的内容不真实或不正确'},
                                        //                 {option_id:'2',option_content:'本人信息发生变更，主动更新'},
                                        //                 {option_id:'3',option_content:'前一次评估曹祖有误'},
                                        //                 {option_id:'4',option_content:'对本人现有风险承受能力级别存在质疑'},
                                        //             ]
                                        //         }
                                        //     ]
                                        // }
                                        // 格式化回访问卷
										var questions = oData.result.questionnaire_content;
                                        questions.forEach((item,idx) => {
                                            item.index = idx
                                            item.select = ''
                                            item.question_options.forEach(element => {
                                                element.selected = false
                                            })
                                            formatedquestionnaire.push(item)
                                        });
                                        console.log('formatedquestionnaire',formatedquestionnaire);
                                    })
                                    TZT.TOOLS.readMapMesg(['backUrl','account_type','app_id','fromlogin','preSaveResult','userParams'],function(oRead){
                                        USERMESG = oRead;
                                        //获取内存数据
                                        fromlogin = USERMESG.fromlogin;
                                        account_type = USERMESG.account_type;
                                        app_id = USERMESG.app_id;
                                        backUrl = USERMESG.backUrl;
                                        preSaveResult = USERMESG.preSaveResult;
                                        userParams = USERMESG.userParams;

                                        var riskLevel = preSaveResult.corp_risk_level;  //类别级别
                                        var result_level = preSaveResult.result_level;//接口返回的类别名称
                                        var min_rank_flag = preSaveResult.min_rank_flag;  //是否为c1最低等级标识

                                        if(isOrganClient != 1 ) queryCompare();// 风险测评答案比对查询接口

                                        if(riskLevel=="1" && min_rank_flag=="1"){
                                            //C1最低类别前端容错逻辑
                                            riskLevel='L';
                                        }

                                        /**
                                         * 自动评测从url参数中读取参数
                                         */
                                        if(+isAutoCheck){
                                            riskLevel = $.getUrlParameter('corp_risk_level');  //类别级别
                                            result_level = decodeURIComponent($.getUrlParameter('result_level'));//接口返回的类别名称
                                            fromlogin = $.getUrlParameter('from');
                                            account_type = $.getUrlParameter('account_type');
                                            app_id = $.getUrlParameter('app_id');

                                            
                                            TZT.TOOLS.saveMapMesgLong({fromlogin:fromlogin,app_id:app_id},function(){});
                                            
                                            if(result_level.indexOf("最低")>-1){
                                                //C1最低类别前端容错逻辑
                                                riskLevel='L';
                                            }

                                        }

                                        var name = result_level //直接展示接口返回结果，min_rank_flag的判断逻辑交由中间件完成
                                        var index = name.lastIndexOf("（");

                                        //适配3.0页面返回按钮--事件
                                        if(app_id == 'yjb3.0' && fromlogin=='login'){
                                            window.doneSuitability1 = function(){
                                                // 弹窗提示
                                                var message = '尊敬的投资者，您的测评结果尚未确认，您可在页面下方点击“确认测评结果”按钮，对测评结果进行确认。现在退出，将丢失您最新的测评结果。是否仍然退出？';
                                                layerUtils.confirmCustom(message,'暂不退出','退出',function(){ 
                                                    console.log('暂不退出');
                                                    return;
                                                },function(){
                                                    console.log('退出');
                                                    Controller.doneSuitability(account_type,0);
                                                    return;
                                                })
                                                
                                            }
                                            Controller.setTitle({
                                                navBarBgColor: "#FFFFFF",
                                                titleColor: "#333333",
                                                rightTextColor: "#333333",
                                                leftText: '/common/imgs/left_arrow_grey.png',
                                                'fullScreen':0,
                                                'leftType':98,
                                                'leftJS':'window.doneSuitability1()',
                                                'rightType':0,
                                            });
                                            jssdk.ready(function(){
                                                jssdk.statusBar({
                                                    style: 0,
                                                });
                                            })

                                        }else{
                                            // fromlogin 为login之外的其他场景
                                            window.doneSuitability1 = function(){
                                                // 弹窗提示
                                                var message = '尊敬的投资者，您的测评结果尚未确认，您可在页面下方点击“确认测评结果”按钮，对测评结果进行确认。现在退出，将丢失您最新的测评结果。是否仍然退出？';
                                                layerUtils.confirmCustom(message,'暂不退出','退出',function(){ 
                                                    console.log('暂不退出');
                                                    return;
                                                },function(){
                                                    console.log('退出');
                                                    Controller.goBackPage();
                                                    return;
                                                })
                                                
                                            }
                                            Controller.setTitle({
                                                navBarBgColor: "#FFFFFF",
                                                titleColor: "#333333",
                                                rightTextColor: "#333333",
                                                leftText: '/common/imgs/left_arrow_grey.png',
                                                'fullScreen':0,
                                                'leftType':98,
                                                'leftJS':'window.doneSuitability1()',
                                                'rightType':0,
                                                'title':'投资风险测评',
                                            });
                                            jssdk.ready(function(){
                                                jssdk.statusBar({
                                                    style: 0,
                                                });
                                            })
                                        }

                                        var riskLevelValue = name.slice(0,index);
                                        console.log(riskLevel);

                                        $('.resultTip').html(name.slice(0,index));
                                        $('.riskResult-container').addClass('bg_risk_'+riskLevelValue);

                                        var riskName = name.slice(index+1,-1);
                                        if(riskName.indexOf('风险承受') == -1){
                                            riskName = riskName + '风险承受';
                                        }

                                        $('.riskAudi').html(riskName);

                                        //$("#riskTitle").html("根据您填写的信息，您的风险承受能力评估如下。请认真阅读适当性匹配意见和特别提示，确认后本次评测结果方能生效。");
                                        $(".btnNext").show().html("确认测评结果");
                                        // 1, 获取协议配置
                                        getParameter(function(p_data){
                                            var paramStr=p_data.result.paramValue;
                                            var paramArray=paramStr.split(";");
                                            for(var i=0;i<paramArray.length;i++){
                                                var fArray=paramArray[i].split("-");
                                                if(fArray[0]==riskLevel){
                                                    g_group_nos=fArray[1];
                                                    getPactMesgList(g_group_nos);
                                                    break;
                                                }
                                            }
                                        });
                                        $('.btnNext').unbind("click");
                                        $('.btnNext').on('click',function(){
                                            loggerEvent(function(){
                                                if(persent_num >=1 || (history_num + persent_num) >= 2){
                                                // 展示回访问卷弹窗
                                                let quesListHtml = questionnaireDom(formatedquestionnaire[0])
                                                $(".mainBox").append(quesListHtml)
                                                
                                                bindEvent();
                                                return
                                            }else{                       
                                                // 直接提交
                                                updataAndSubmit()
                                            }
                                            })
                                           
                                        })

                                        //绑定事件
                                        bindEvent();
                                    })
                                }
                            }
                        } else{
                            //CORP_END_DATE = null;  //这里当处理出错时，调到对应的错误页面，用了原来的CORP_END_DATE变量
                            // layerUtils.iMsg(-1,"接口报错");
                        }
                    })
                    
                }
            });
        }else{
            setTimeout(function(){
                // $.use 异步加载
                layerUtils.iMsg(-1,'登录超时请返回重新登录！');
            },1000);
            setTimeout(function(){
                Controller.closeAllPage('',3);
            },3000);
            return false;
        }
    },true);
}

function bindEvent(){
    $('.answer').each(function(index,obj){
        // console.log(index,obj);
        $(obj).on('click',function(){

			var question_options = formatedquestionnaire[0].question_options;
			var quesCode = $(this).parents('.quesMask').find('.answer').index(this);
			var select = [];

            question_options.forEach(function(option,idx){
                if(idx == quesCode){
                    option.selected = true;
                    select.push(option.option_id)
                }else{
                    option.selected = false;
                }
            })
            formatedquestionnaire[0].select = select.sort().join('')
            formatedquestionnaire[0].question_options = question_options;
            console.log('formatedquestionnaire',formatedquestionnaire);
            // 更新渲染页面
			$(this).addClass('answerCur').siblings().removeClass('answerCur').parents('.quesMask').removeClass('mistake');
            $('#questionnaire-confirm').addClass('submit')
        })

    })

    $('#questionnaire-cancel').on('click',function(){
        // 取消 清空选择
        formatedquestionnaire[0].select =''
        formatedquestionnaire[0].question_options.forEach(function(option){
            option.selected = false;
        })
        $("#quesMask").remove();
    })

    $('#questionnaire-confirm').on('click',function(){
        // 确认
        if(!formatedquestionnaire[0].select){
            return
        }
        sbumitQuestionnaireUserAnswer=[]
        var user_answer = {
            question_id:'',
            answer:[],
        }
        user_answer.question_id = formatedquestionnaire[0].question_id
        formatedquestionnaire[0].question_options.forEach(item => {
            if(item.selected){
                user_answer.answer.push({
                    choice:item.option_id
                })
            }
        })
        sbumitQuestionnaireUserAnswer.push(user_answer)
        console.log('sbumitQuestionnaireUserAnswer',sbumitQuestionnaireUserAnswer);
		var osInfo =getOsInfo();

        // 最后的答案
        strPanperAnser = {
            user_account :client_id,
            account_type: 4, // 2 普通 3 信用 4 客户编号
            opstation:opStation,
            origin:"CHANNEL:"+app_id+";DEVICE:"+osInfo['DEVICE']+";SYSTEM:"+osInfo['SYSTEM'],
            user_answers:sbumitQuestionnaireUserAnswer
        };
        sbumitQuestionnaire(strPanperAnser)
    })

    $('.riskRepeat a').unbind("click");
    $('.riskRepeat a').on('click',function(){
        try{
            PUBLCISELECT.Loggercollect({event_id:settings.loggerId['risk_review'],logtype:'risk_review'}); //行为采集
        }catch(e){}
				
				//重新测评存在来自登录场景，需要将from=login存储起来，便于传递
				TZT.TOOLS.saveMapMesgLong({fromlogin:fromlogin,app_id:app_id},function(){});

        if($.getUrlParameter('is_sa')!='1'){
            // Controller.pageInit('risk.html?');
            Controller.pageInit('risk.html?again=second&account_type='+account_type);
        }else{
            //当风测页面带了backUrl时，重新测评再带着返回
            if(backUrl==null||backUrl=='undefined'||backUrl==''){
                Controller.pageInit('risk.html?is_sa=1&account_type='+account_type);
            }else{
                var jumpUrl = 'risk.html?is_sa=1&account_type='+account_type;
                if(app_id){
                    jumpUrl = jumpUrl + '&app_id=' + app_id;
                }
                jumpUrl = jumpUrl + '&backUrl='+backUrl;
                Controller.pageInit(jumpUrl);
            }
        }
    })
}

function getOsInfo(){
		function detectOS() {
			var sUserAgent = navigator.userAgent;
			var isWin = (navigator.platform == "Win32") || (navigator.platform == "Windows");
			var isMac = (navigator.platform == "Mac68K") || (navigator.platform == "MacPPC") || (navigator.platform == "Macintosh") || (navigator.platform == "MacIntel");
			if (isMac) return "Mac";
			var isUnix = (navigator.platform == "X11") && !isWin && !isMac;
			if (isUnix) return "Unix";
			var isLinux = (String(navigator.platform).indexOf("Linux") > -1);
			if (isLinux) return "Linux";
			if (isWin) {
				var isWin2K = sUserAgent.indexOf("Windows NT 5.0") > -1 || sUserAgent.indexOf("Windows 2000") > -1;
				if (isWin2K) return "Win2000";
				var isWinXP = sUserAgent.indexOf("Windows NT 5.1") > -1 || sUserAgent.indexOf("Windows XP") > -1;
				if (isWinXP) return "WinXP";
				var isWin2003 = sUserAgent.indexOf("Windows NT 5.2") > -1 || sUserAgent.indexOf("Windows 2003") > -1;
				if (isWin2003) return "Win2003";
				var isWinVista= sUserAgent.indexOf("Windows NT 6.0") > -1 || sUserAgent.indexOf("Windows Vista") > -1;
				if (isWinVista) return "WinVista";
				var isWin7 = sUserAgent.indexOf("Windows NT 6.1") > -1 || sUserAgent.indexOf("Windows 7") > -1;
				if (isWin7) return "Win7";
			}
			return "other";
		}

		if(comm.BASEVAR.ISANDROID){
			return {'SYSTEM':'android','DEVICE':'mobile'};
		}else if(comm.BASEVAR.ISIOS){
			return {'SYSTEM':'ios','DEVICE':'mobile'};
		}else{
			return {'SYSTEM':detectOS(),'DEVICE':'pc'};
		}
}

//获取系统参数
function getParameter(callback){
    waiting.show();
    $.getData(SENDTO60,{  //CONTRACT_001
        UniqueKey:'SYSCOMMON',
        param_key:'RISK_LEVEL_ECONTRACT_NO'
    },function(oReturn){
        waiting.hide();
        if(oReturn.ERRORNO!=0){
            layerUtils.alert($.replaceString(BASETIP,{error_no:oReturn.ERRORNO,error_info:oReturn.ERRORMESSAGE}));
            return false;
        }
        var oData = oReturn.GRID0;
        if(oData.code != 0){
            waiting.hide();
            layerUtils.alert($.replaceString(BASETIP,{error_no:oData.code,error_info:oData.message}));
            return false;
        }
        callback(oData);
    })
}

//获取协议信息
function getPactMesgList(group_nos){
    waiting.show();
    $.getData(SENDTO60,{  //CONTRACT_001
        UniqueKey:'CONTRACT_001',
        branch_no:'($khbranch)',
        client_id:"($usercode)",
        fund_account:"($fund_account)",
        password:"($password)",
        password_type:2,
        group_nos:group_nos,
        include_content:1
        //econtract_id:pactlist[market]
    },function(oReturn){
        waiting.hide();
        if(oReturn.ERRORNO!=0){
            layerUtils.alert($.replaceString(BASETIP,{error_no:oReturn.ERRORNO,error_info:oReturn.ERRORMESSAGE}));
            return false;
        }
        var oData = oReturn.GRID0;
        if(oData.code != 0){
            waiting.hide();
            layerUtils.alert($.replaceString(BASETIP,{error_no:oData.code,error_info:oData.message}));
            return false;
        }
        var list = oData.result.econtract_list,code ='';
        for(var x = 0 ; x < list.length ; x++){
            var oThis = list[x];
            if(oThis.econtract_no==1104){
                //电子签名约定书,不展示及签署
                continue;
            }
            objPact[('pact_'+oThis.econtract_id)] = {
                name:oThis.econtract_name,
                md5:oThis.econtract_md5,
                content:oThis.econtract_content
            };
            var sContent = objPact[('pact_'+oThis.econtract_id)].content
                    .replace(/<br[^>]*?>/g, "")// 将换行符替换成一个双字节字符
                    .replace(/[\r\n	]*/g, "")	// 过去换行和 tab 符。
                    .replace(/[\r\n]/g, "");     //过滤回车
            code += '<div style="margin: 5px;">'+sContent+'</div>';
            //code += '<p>《<a href="javascript:;"  onclick="showPact('+oThis.econtract_id+')" >'+oThis.econtract_name+'</a>》</p>';
        }
        //$('.signPact').append(code);
        $('#deal').html(code);

        /**
         * 添加点击事件
         */
        $("#levelTip").unbind("click");
        $("#levelTip").on('click',function(e){
            Controller.newPageInit('riskTip.html');
        });
    })
}


//签署协议信息
function submitToBack(group_nos,callback){
    //协议签署
    signRiskPact(group_nos,function(){
        /**
         * 动态测评提交
         */
        if(+isAutoCheck){
            $.getData(SENDTO60, {
                UniqueKey: 'APPROPRIATE_SURE',
                //questionnaire_id: questionnaire_id,
                client_id:"($usercode)", //v 客户号  Y  client_id 和 prodta_no 必须传一个
                paper_type:1, //试卷类别 Y
                //user_answers:user_answers, //试卷的答案 Y 格式：801&1|802&2^3|  801，802表示题号1,2,3　是答案
                //prodta_no:'', //产品TA编号
                branch_no:'($khbranch)',
                //organ_flag:organ_flag
                autoeligtest_status:1,  //确认
                password:"($password)",
                password_type:2,
                op_entrust_way:7

            }, function (oReturn) {
                if (oReturn.ERRORNO != 0) {
                    waiting.hide();
                    layerUtils.alert($.replaceString(BASETIP, { error_no: oReturn.ERRORNO, error_info: oReturn.ERRORMESSAGE }));
                    return false;
                }
                var oData = oReturn.GRID0;//errno = oData.error_no;
                if (oData.code != 0 ) {
                    waiting.hide();
                    layerUtils.alert($.replaceString(BASETIP, { error_no: oData.code, error_info: oData.message }));
                    return false;
                }
                //执行成功的回调
                callback();
            })
            return;
        }

        var questionnaire_id = userParams['questionnaire_id'];
        var paper_type = userParams['paper_type'];
        var user_answers = userParams['user_answers'];
        var organ_flag = userParams['organ_flag'];
        $.getData(SENDTO60, {
            UniqueKey: 'QRMS',
            questionnaire_id: questionnaire_id,
            client_id:"($usercode)", // 客户号  Y  client_id 和 prodta_no 必须传一个
            paper_type:paper_type, //试卷类别 Y
            user_answers:user_answers, //试卷的答案 Y 格式：801&1|802&2^3|  801，802表示题号1,2,3　是答案
            prodta_no:'', //产品TA编号
            branch_no:'($khbranch)',
            organ_flag:organ_flag
        }, function (oReturn) {
            if (oReturn.ERRORNO != 0) {
                waiting.hide();
                layerUtils.alert($.replaceString(BASETIP, { error_no: oReturn.ERRORNO, error_info: oReturn.ERRORMESSAGE }));
                return false;
            }
            var oData = oReturn.GRID0;//errno = oData.error_no;
            if (oData.code != 0 ) {
                waiting.hide();
                layerUtils.alert($.replaceString(BASETIP, { error_no: oData.code, error_info: oData.message }));
                return false;
            }
            //执行成功的回调
            callback();
        })


    });
}

function signRiskPact(group_nos,callback){
   waiting.show();
   var sub_info = '';
    if(+isAutoCheck){
        sub_info += '动态测评已签署'
    }else{
        sub_info += '已签署'
    }

   $.getData(SENDTO60, {
       UniqueKey: 'CONTRACT_SIGN_GROUPNO',
       branch_no:'($khbranch)',
       client_id:"($usercode)",
       fund_account:"($fund_account)",
       password:"($password)",
       group_nos:group_nos,
       include_content:1,
       password_type:2,
       account_type:12,//协议接口变更 增加account_type参数
       business_flag:'1533',
       sub_info:sub_info
   }, function (oReturn) {
       if (oReturn.ERRORNO != 0) {
           waiting.hide();
           layerUtils.alert($.replaceString(BASETIP, { error_no: oReturn.ERRORNO, error_info: oReturn.ERRORMESSAGE }));
           return false;
       }
       var oData = oReturn.GRID0;//errno = oData.error_no;
       if (oData.code != 0 ) {
           waiting.hide();
           layerUtils.alert($.replaceString(BASETIP, { error_no: oData.code, error_info: oData.message }));
           return false;
       }
       //执行成功的回调
       callback();
   });
    //执行成功的回调
    //callback();
}

//查询某个业务模块是否有公告
function queryNotice(oParms,fnCallbak){
    $.getData(SENDTO60,$.extend({
        UniqueKey:'ANNC_1004',
        type:'',
        subtype:''
    },oParms), function (oReturn) {

        if (oReturn.ERRORNO != 0) {
            waiting.hide();
            layerUtils.alert($.replaceString(BASETIP, { error_no: oReturn.ERRORNO, error_info: oReturn.ERRORMESSAGE }));
            return false;
        }
        var oData = oReturn.GRID0;//errno = oData.error_no;
        if (oData.code != 0 ) {
            waiting.hide();
            layerUtils.alert($.replaceString(BASETIP, { error_no: oData.code, error_info: oData.message }));
            return false;
        }
        NUM = oData.code==undefined? oData.error_no:oData.code;
        if(NUM == '0'){
            if(oData.result && oData.result.announceList.length >0){
                var serverTime=oData.result.serverTime;  //服务器时间
                var announceList=oData.result.announceList;
                var notice = 0;
                for(var i=0;i<announceList.length;i++){
                    var validFrom=new Date(announceList[i].validFrom.replace(/-/g,"/")).getTime();
                    var validTo=new Date(announceList[i].validTo.replace(/-/g,"/")).getTime();
                    if(serverTime<=validTo && serverTime>=validFrom && announceList[i].status == 0){
                        //展示公告
                        var title=announceList[i].title;
                        var content=announceList[i].content;
                        $(".notice_title").html(title);
                        $(".notice_content").html(content);

                        $('.mainBox').hide();
                        $('.fail-containter').hide();
                        $('.notice').show();
                        $('body').show();
                    }else{
                        //非公告时间
                        notice ++;
                    }
                }
                //所有的公告都不在有效期，视为无公告
                if(notice == announceList.length){
                    fnCallbak(true);
                }
            }else{
                fnCallbak(true);
            }
        }else{
            fnCallbak(true);
        }
    })
}

// 问卷提交
function sbumitQuestionnaire(param){
    $.getData(SENDTO60, {
        UniqueKey: 'QRMS',
        questionnaire_id: '20230522001',
        user_answers:JSON.stringify(param),
        paper_type:'K',
        to_local:1,
        to_counter:1,
    }, function (oReturn) {
        // 回访问卷无感提交
        console.log('回访问卷的返回',oReturn);
        updataAndSubmit()
    });
}

// 更新用户缓存并提交问卷
function updataAndSubmit(){
    waiting.show();
    submitToBack(g_group_nos,function(){
        // 更新用户缓存
        service.getClientDetailInfo({client_id:user.usercode,password:user.password,page_type:isOrganClient}, function(dataClient){
            utils.saveClientDetailInfo(dataClient)
            var backUrl = USERMESG.BACKURL;
            waiting.hide();
            //适配3.0页面完成的点击事件
            TZT.TOOLS.saveMapMesgLong({backUrl:'',fromlogin:'',preSaveResult:'',userParams:''},function(){});
            $("#quesMask").remove();
            if(app_id == 'yjb3.0' && fromlogin=='login'){
                Controller.doneSuitability(account_type,1);
            }else{
                if(backUrl==null||backUrl=='undefined'||backUrl==''){
                    if(sessionStorage.getItem('channel_type')=='*************'
                    &&Controller.getAppId()=='yjbweb'){
                        //同花顺渠道话术适配TEST-2512
                        layerUtils.alert('<div style="text-align:center">您已完成当前业务操作，请点击左上角“关闭”退出本业务后，继续办理业务</div>',function(){});
                    } else if (sessionStorage.getItem('channel_type')=='*************'
                    &&Controller.getAppId()=='yjbweb') {
                        // 腾讯自选股小程序渠道适配
                        layerUtils.alert('<div style="text-align:center">您已完成当前业务操作，请点击左上角“返回”退出本业务后，继续办理业务</div>',function(){});
                    }else{
                        Controller.goBackPage();
                    }
                }else{
                    if(backUrl.indexOf('https%3')==0
                    ||backUrl.indexOf('http%3')==0){
                        backUrl=decodeURIComponent(backUrl);
                    }
                    Controller.basePageInit(backUrl);
                }
            }
        });
    })
}


// 获取回访问卷内容
function questionnaireDom(quesContent){
    var quesCode = '';
	var temp_question_title=quesContent.question_title;
    quesCode = quesCode + '<div class="quesMask" id="quesMask">'
                        +'<div class="quesContainer">'
                        +'<p class="quesText" >' + temp_question_title + '</p>';
			
    var anserList = quesContent.question_options;

    quesCode+='<div class="options">'

    for(var obj=0;obj<anserList.length;obj++) {
        var option = anserList[obj];
        var selected = option.selected;
        var className = "answer";
        if(selected){
            className += " answerCur";
        }

        quesCode += '<p id="ques-'+'option-'+option.option_id+'" class="'+className+'" ><span class="ico"></span>' + option.option_id + '、' + option.option_content + '</p>';
    }

    quesCode += '<div class="ques-footer">' 
    quesCode += '<div class="left" id="questionnaire-cancel">' + '取消' + '</div>'
    quesCode += '<div class="right" id="questionnaire-confirm">' + '确认' + '</div>'   



    quesCode+='</div>'

    quesCode+='</div>'

    quesCode +='</div>';

    quesCode +='</div>';
    return quesCode;
}

//风险测评答案比对查询接口
function queryCompare(){
    var questionnaire_id = userParams['questionnaire_id'];
    var user_answers = JSON.parse(userParams['user_answers']) ;
    var questionnaire_version = userParams['questionnaire_version'];

    $.getData(SENDTO60, {
        UniqueKey: 'CWECQ',
        client_id:'($usercode)', // 客户号  Y  client_id 和 prodta_no 必须传一个
        password: '($password)',
        op_station: "($op_station)",  
        fund_account:"($fund_account)",           
        user_answers:JSON.stringify(user_answers.user_answers),
        questionnaire_id,
        questionnaire_version,
        }, function (oReturn) {
            if (oReturn.ERRORNO != 0) {
                waiting.hide();
                layerUtils.alert($.replaceString(BASETIP, { error_no: oReturn.ERRORNO, error_info: oReturn.ERRORMESSAGE }));
                    return false;
            }
            var oData = oReturn.GRID0;//errno = oData.error_no;
            if (oData.code != 0 ) {
                waiting.hide();
                layerUtils.alert($.replaceString(BASETIP, { error_no: oData.code, error_info: oData.message }));
                return false;
            }
            if(oData.result.compareText){
                compareResult = oData.result.compareText ;//得到的风险测评对比结果
                $("#compareBox").html('您本次在第<span style="color:red">'+oData.result.compareText+'</span>题中选择的选项较上次测评结果变化较大，请您确认本次提供的信息是否真实准确。若不准确，请重新测评。');
            }
            
        }
    )
}
// 留痕弹框
function loggerEvent(fnCallback){
    var questionnaire_version = userParams['questionnaire_version'];

    if(compareResult){
        waiting.show();
        var  defaultParam = {
            UniqueKey:'YACTA',
            account_id: '($usercode)',
            account_type: 5,
            business_type: '30014',
            addition:JSON.stringify({
                datetime:+new Date, //留痕时间
                op_name:"投资风险测评题目答案差异过大留痕弹窗留痕", //业务留痕场景说明，固定值
                content:"本次风险测评在第"+compareResult+"题中选择的选项较上次测评结果变化较大，客户确认本次提供的信息是否真实准确。", //页面弹窗提示文本
                client_id:client_id, //用户客户号
                questionnaire_version, // 问卷版本
            }),
            op_info:'确认',
            op_id:'1105111',
            op_station:"($op_station)",
        };
        $.getData(SENDTO60,defaultParam,function(oReturn){
            console.log('[数据库][留痕操作] ：成功',oReturn);
            if(oReturn.ERRORNO==0){
                waiting.hide();
            }else{
                console.log('success');
                waiting.hide();
            }
            fnCallback && fnCallback(oReturn);
        })
    }else{
        fnCallback && fnCallback();
    }
}

</script>
</body>
</html>


